/**
 * 智能推荐引擎
 * 
 * 提供基于用户环境和使用模式的个性化配置推荐：
 * - 系统环境分析
 * - 配置冲突检测
 * - 性能优化建议
 * - 个性化推荐算法
 * 
 * @since 2.0.0
 */

import type { SettingsData, SystemInfo, SmartRecommendation } from '../types'

// 推荐配置类型
export interface RecommendationConfig {
  performance_level: 'conservative' | 'balanced' | 'aggressive'
  api_page_size: number
  concurrent_requests: number
  batch_size: number
  log_buffer_size: number
  sync_schedule: string
  enable_auto_sync: boolean
}

// 环境分析结果
export interface EnvironmentAnalysis {
  memory_available: number
  php_version: string
  server_type: 'shared' | 'vps' | 'dedicated' | 'cloud'
  performance_tier: 'low' | 'medium' | 'high'
  risk_level: 'low' | 'medium' | 'high'
}

// 配置冲突
export interface ConfigConflict {
  type: 'performance' | 'compatibility' | 'resource'
  severity: 'warning' | 'error' | 'critical'
  description: string
  suggestion: string
  auto_fix?: boolean
}

// 推荐历史记录
export interface RecommendationHistory {
  timestamp: string
  config: RecommendationConfig
  reason: string
  applied: boolean
  performance_impact?: string
}

export class SmartRecommendationEngine {
  private static instance: SmartRecommendationEngine
  private history: RecommendationHistory[] = []

  private constructor() {
    this.loadHistory()
  }

  public static getInstance(): SmartRecommendationEngine {
    if (!SmartRecommendationEngine.instance) {
      SmartRecommendationEngine.instance = new SmartRecommendationEngine()
    }
    return SmartRecommendationEngine.instance
  }

  /**
   * 分析系统环境
   */
  public analyzeEnvironment(systemInfo?: SystemInfo): EnvironmentAnalysis {
    // 默认环境信息（如果没有提供系统信息）
    const defaultAnalysis: EnvironmentAnalysis = {
      memory_available: 128,
      php_version: '7.4',
      server_type: 'shared',
      performance_tier: 'medium',
      risk_level: 'medium'
    }

    if (!systemInfo) {
      return defaultAnalysis
    }

    // 分析内存
    const memoryMB = this.parseMemoryLimit(systemInfo.memory_limit || '128M')
    
    // 分析服务器类型
    const serverType = this.detectServerType(systemInfo)
    
    // 确定性能等级
    const performanceTier = this.determinePerformanceTier(memoryMB, systemInfo.php_version)
    
    // 评估风险等级
    const riskLevel = this.assessRiskLevel(memoryMB, systemInfo.php_version, serverType)

    return {
      memory_available: memoryMB,
      php_version: systemInfo.php_version || '7.4',
      server_type: serverType,
      performance_tier: performanceTier,
      risk_level: riskLevel
    }
  }

  /**
   * 检测配置冲突
   */
  public detectConflicts(config: Partial<SettingsData>, environment: EnvironmentAnalysis): ConfigConflict[] {
    const conflicts: ConfigConflict[] = []

    // 检查内存与并发请求冲突
    if (config.concurrent_requests && config.concurrent_requests > 5 && environment.memory_available < 256) {
      conflicts.push({
        type: 'resource',
        severity: 'warning',
        description: `并发请求数 (${config.concurrent_requests}) 对于可用内存 (${environment.memory_available}MB) 来说可能过高`,
        suggestion: '建议将并发请求数降低到 3-4 个',
        auto_fix: true
      })
    }

    // 检查API分页大小与性能冲突
    if (config.api_page_size && config.api_page_size > 50 && environment.performance_tier === 'low') {
      conflicts.push({
        type: 'performance',
        severity: 'warning',
        description: `API分页大小 (${config.api_page_size}) 对于当前性能等级可能过大`,
        suggestion: '建议将分页大小设置为 20-30',
        auto_fix: true
      })
    }

    // 检查自动同步频率冲突
    if (config.enable_auto_sync && config.sync_schedule === 'hourly' && environment.server_type === 'shared') {
      conflicts.push({
        type: 'compatibility',
        severity: 'error',
        description: '共享主机环境下每小时同步可能导致资源限制',
        suggestion: '建议改为每日同步或手动同步',
        auto_fix: true
      })
    }

    return conflicts
  }

  /**
   * 生成个性化推荐
   */
  public generateRecommendations(
    currentConfig: Partial<SettingsData>,
    environment: EnvironmentAnalysis,
    usagePattern?: 'light' | 'moderate' | 'heavy'
  ): {
    config: RecommendationConfig
    explanation: string
    expected_improvement: string
    confidence: number
    conflicts: ConfigConflict[]
  } {
    const pattern = usagePattern || this.detectUsagePattern(currentConfig)
    const conflicts = this.detectConflicts(currentConfig, environment)
    
    // 基于环境和使用模式生成推荐配置
    const recommendedConfig = this.calculateOptimalConfig(environment, pattern)
    
    // 生成解释
    const explanation = this.generateExplanation(environment, pattern, recommendedConfig)
    
    // 计算预期改进
    const expectedImprovement = this.calculateExpectedImprovement(currentConfig, recommendedConfig, environment)
    
    // 计算置信度
    const confidence = this.calculateConfidence(environment, conflicts.length)

    // 记录推荐历史
    this.addToHistory(recommendedConfig, explanation, false)

    return {
      config: recommendedConfig,
      explanation,
      expected_improvement: expectedImprovement,
      confidence,
      conflicts
    }
  }

  /**
   * 解析内存限制
   */
  private parseMemoryLimit(memoryLimit: string): number {
    const match = memoryLimit.match(/^(\d+)([KMG]?)$/i)
    if (!match) return 128

    const value = parseInt(match[1])
    const unit = match[2]?.toUpperCase() || 'M'

    switch (unit) {
      case 'K': return Math.floor(value / 1024)
      case 'G': return value * 1024
      default: return value
    }
  }

  /**
   * 检测服务器类型
   */
  private detectServerType(systemInfo: SystemInfo): 'shared' | 'vps' | 'dedicated' | 'cloud' {
    const memoryMB = this.parseMemoryLimit(systemInfo.memory_limit || '128M')
    
    if (memoryMB < 256) return 'shared'
    if (memoryMB < 1024) return 'vps'
    if (memoryMB < 4096) return 'dedicated'
    return 'cloud'
  }

  /**
   * 确定性能等级
   */
  private determinePerformanceTier(memoryMB: number, phpVersion?: string): 'low' | 'medium' | 'high' {
    const phpMajor = phpVersion ? parseInt(phpVersion.split('.')[0]) : 7
    
    if (memoryMB < 256 || phpMajor < 7) return 'low'
    if (memoryMB < 512 || phpMajor < 8) return 'medium'
    return 'high'
  }

  /**
   * 评估风险等级
   */
  private assessRiskLevel(memoryMB: number, phpVersion?: string, serverType?: string): 'low' | 'medium' | 'high' {
    let riskScore = 0
    
    if (memoryMB < 256) riskScore += 2
    if (phpVersion && parseFloat(phpVersion) < 7.4) riskScore += 2
    if (serverType === 'shared') riskScore += 1
    
    if (riskScore >= 3) return 'high'
    if (riskScore >= 1) return 'medium'
    return 'low'
  }

  /**
   * 检测使用模式
   */
  private detectUsagePattern(config: Partial<SettingsData>): 'light' | 'moderate' | 'heavy' {
    let score = 0
    
    if (config.enable_auto_sync) score += 1
    if (config.sync_schedule && ['hourly', 'twicedaily'].includes(config.sync_schedule)) score += 2
    if (config.concurrent_requests && config.concurrent_requests > 3) score += 1
    if (config.api_page_size && config.api_page_size > 50) score += 1
    
    if (score >= 4) return 'heavy'
    if (score >= 2) return 'moderate'
    return 'light'
  }

  /**
   * 计算最优配置
   */
  private calculateOptimalConfig(environment: EnvironmentAnalysis, pattern: string): RecommendationConfig {
    const baseConfigs = {
      light: {
        performance_level: 'conservative' as const,
        api_page_size: 20,
        concurrent_requests: 2,
        batch_size: 10,
        log_buffer_size: 50,
        sync_schedule: 'daily',
        enable_auto_sync: false
      },
      moderate: {
        performance_level: 'balanced' as const,
        api_page_size: 30,
        concurrent_requests: 3,
        batch_size: 15,
        log_buffer_size: 100,
        sync_schedule: 'daily',
        enable_auto_sync: true
      },
      heavy: {
        performance_level: 'aggressive' as const,
        api_page_size: 50,
        concurrent_requests: 5,
        batch_size: 25,
        log_buffer_size: 200,
        sync_schedule: 'twicedaily',
        enable_auto_sync: true
      }
    }

    const baseConfig = baseConfigs[pattern as keyof typeof baseConfigs]
    
    // 根据环境调整配置
    if (environment.performance_tier === 'low') {
      baseConfig.concurrent_requests = Math.min(baseConfig.concurrent_requests, 2)
      baseConfig.api_page_size = Math.min(baseConfig.api_page_size, 20)
      baseConfig.performance_level = 'conservative'
    } else if (environment.performance_tier === 'high') {
      baseConfig.concurrent_requests = Math.min(baseConfig.concurrent_requests + 1, 6)
      baseConfig.api_page_size = Math.min(baseConfig.api_page_size + 20, 100)
    }

    // 根据服务器类型调整
    if (environment.server_type === 'shared') {
      baseConfig.sync_schedule = 'daily'
      baseConfig.enable_auto_sync = false
    }

    return baseConfig
  }

  /**
   * 生成解释文本
   */
  private generateExplanation(environment: EnvironmentAnalysis, pattern: string, config: RecommendationConfig): string {
    const reasons = []
    
    reasons.push(`基于您的${environment.server_type}服务器环境`)
    reasons.push(`${environment.memory_available}MB可用内存`)
    reasons.push(`${pattern}使用模式`)
    
    if (environment.performance_tier === 'low') {
      reasons.push('采用保守配置确保稳定性')
    } else if (environment.performance_tier === 'high') {
      reasons.push('采用优化配置提升性能')
    }

    return `${reasons.join('，')}，推荐使用${config.performance_level}性能级别。`
  }

  /**
   * 计算预期改进
   */
  private calculateExpectedImprovement(
    current: Partial<SettingsData>,
    recommended: RecommendationConfig,
    environment: EnvironmentAnalysis
  ): string {
    const improvements = []
    
    if (!current.concurrent_requests || recommended.concurrent_requests > current.concurrent_requests) {
      improvements.push('同步速度提升15-25%')
    }
    
    if (!current.api_page_size || recommended.api_page_size !== current.api_page_size) {
      improvements.push('API效率优化10-20%')
    }
    
    if (environment.performance_tier === 'high' && recommended.performance_level === 'aggressive') {
      improvements.push('整体性能提升30-40%')
    }
    
    return improvements.length > 0 ? improvements.join('，') : '配置优化，提升稳定性'
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(environment: EnvironmentAnalysis, conflictCount: number): number {
    let confidence = 85 // 基础置信度
    
    if (environment.performance_tier === 'high') confidence += 10
    if (environment.risk_level === 'low') confidence += 5
    if (conflictCount === 0) confidence += 5
    
    confidence -= conflictCount * 10
    
    return Math.max(60, Math.min(95, confidence))
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(config: RecommendationConfig, reason: string, applied: boolean): void {
    const historyItem: RecommendationHistory = {
      timestamp: new Date().toISOString(),
      config,
      reason,
      applied
    }
    
    this.history.unshift(historyItem)
    
    // 保持最近20条记录
    if (this.history.length > 20) {
      this.history = this.history.slice(0, 20)
    }
    
    this.saveHistory()
  }

  /**
   * 获取推荐历史
   */
  public getHistory(): RecommendationHistory[] {
    return [...this.history]
  }

  /**
   * 标记推荐为已应用
   */
  public markAsApplied(timestamp: string, performanceImpact?: string): void {
    const item = this.history.find(h => h.timestamp === timestamp)
    if (item) {
      item.applied = true
      item.performance_impact = performanceImpact
      this.saveHistory()
    }
  }

  /**
   * 保存历史记录
   */
  private saveHistory(): void {
    try {
      localStorage.setItem('notion-wp-recommendation-history', JSON.stringify(this.history))
    } catch (error) {
      console.warn('无法保存推荐历史:', error)
    }
  }

  /**
   * 加载历史记录
   */
  private loadHistory(): void {
    try {
      const stored = localStorage.getItem('notion-wp-recommendation-history')
      if (stored) {
        this.history = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('无法加载推荐历史:', error)
      this.history = []
    }
  }
}

// 导出单例实例
export const smartRecommendationEngine = SmartRecommendationEngine.getInstance()
