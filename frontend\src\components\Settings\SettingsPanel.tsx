import React from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Loading } from '../Common'

export const SettingsPanel: React.FC = () => {
  const {
    settings,
    isLoading,
    isSaving,
    updateSettings,
    saveSettings,
    hasUnsavedChanges
  } = useSettingsStore()

  const handleSave = async () => {
    const success = await saveSettings()
    if (success) {
      // 可以显示成功提示
      console.log('设置保存成功')
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <Loading variant="spinner" size="lg" />
        <p className="text-gray-600">正在加载设置...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          其他设置
        </h2>
      </div>

      {/* 卸载设置 */}
      <Card title="卸载设置" shadow="md">
        <CardContent className="space-y-4">
          <div className="notion-wp-form-group">
            <label className="notion-wp-checkbox-label flex items-center space-x-2">
              <input
                type="checkbox"
                className="notion-wp-checkbox"
                checked={settings?.delete_on_uninstall || false}
                onChange={(e) => updateSettings({ delete_on_uninstall: e.target.checked })}
              />
              <span>卸载插件时，删除所有从Notion同步的文章和页面</span>
            </label>
            <p className="notion-wp-description text-red-600 mt-2">
              警告：此操作不可逆！所有通过Notion同步的内容将被永久删除。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* iframe 白名单域名 */}
      <Card title="iframe 白名单域名" shadow="md">
        <CardContent className="space-y-4">
          <div className="notion-wp-form-group">
            <label className="notion-wp-label">
              iframe 白名单域名
            </label>
            <textarea
              className="notion-wp-textarea w-full"
              rows={3}
              value={settings?.iframe_whitelist || 'www.youtube.com,youtu.be,player.bilibili.com,b23.tv,v.qq.com'}
              onChange={(e) => updateSettings({ iframe_whitelist: e.target.value })}
              placeholder="www.youtube.com,youtu.be,player.bilibili.com,b23.tv,v.qq.com"
            />
            <p className="notion-wp-description text-sm text-gray-600 mt-2">
              允许在内容中嵌入的 iframe 域名白名单，多个域名请用英文逗号分隔。输入 * 表示允许所有域名（不推荐）。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 允许的图片格式 */}
      <Card title="允许的图片格式" shadow="md">
        <CardContent className="space-y-4">
          <div className="notion-wp-form-group">
            <label className="notion-wp-label">
              允许的图片格式
            </label>
            <textarea
              className="notion-wp-textarea w-full"
              rows={2}
              value={settings?.allowed_image_types || 'image/jpeg,image/png,image/gif,image/webp'}
              onChange={(e) => updateSettings({ allowed_image_types: e.target.value })}
              placeholder="image/jpeg,image/png,image/gif,image/webp"
            />
            <p className="notion-wp-description text-sm text-gray-600 mt-2">
              允许下载和导入的图片 MIME 类型，多个类型请用英文逗号分隔。输入 * 表示允许所有格式。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 插件界面语言 */}
      <Card title="插件界面语言" shadow="md">
        <CardContent className="space-y-4">
          <div className="notion-wp-form-group">
            <label className="notion-wp-label">
              插件界面语言
            </label>
            <select
              className="notion-wp-select"
              value={settings?.plugin_language || 'auto'}
              onChange={(e) => updateSettings({ plugin_language: e.target.value })}
            >
              <option value="auto">自动检测（跟随站点语言）</option>
              <option value="zh_CN">简体中文</option>
              <option value="en_US">English</option>
            </select>
            <p className="notion-wp-description text-sm text-gray-600 mt-2">
              选择插件界面显示的语言。自动检测将跟随WordPress站点语言设置。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 最大图片大小 */}
      <Card title="最大图片大小" shadow="md">
        <CardContent className="space-y-4">
          <div className="notion-wp-form-group">
            <label className="notion-wp-label">
              最大图片大小 (MB)
            </label>
            <input
              type="number"
              className="notion-wp-input"
              value={settings?.max_image_size || 5}
              onChange={(e) => updateSettings({ max_image_size: parseInt(e.target.value) })}
              min="1"
              max="20"
              step="1"
            />
            <p className="notion-wp-description text-sm text-gray-600 mt-2">
              允许下载的最大图片大小（以 MB 为单位）。建议不超过 10MB。
            </p>
          </div>
        </CardContent>
      </Card>



      {hasUnsavedChanges && (
        <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">您有未保存的更改</p>
          <Button
            variant="primary"
            onClick={handleSave}
            loading={isSaving}
            disabled={isSaving}
          >
            {isSaving ? '保存中...' : '保存设置'}
          </Button>
        </div>
      )}
    </div>
  )
}