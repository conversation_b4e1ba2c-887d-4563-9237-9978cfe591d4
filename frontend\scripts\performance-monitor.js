#!/usr/bin/env node

/**
 * 性能监控脚本
 * 
 * 分析构建产物，生成性能报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PerformanceAnalyzer {
  constructor() {
    this.distPath = path.resolve(__dirname, '../../assets/dist');
    this.manifestPath = path.join(this.distPath, 'manifest.json');
    this.report = {
      timestamp: new Date().toISOString(),
      bundleSize: {},
      recommendations: [],
      metrics: {}
    };
  }

  /**
   * 分析Bundle大小
   */
  analyzeBundleSize() {
    console.log('📊 分析Bundle大小...');
    
    if (!fs.existsSync(this.manifestPath)) {
      console.error('❌ 找不到manifest.json，请先运行构建');
      return;
    }

    const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'));
    const files = {};
    let totalSize = 0;

    // 分析所有文件
    Object.entries(manifest).forEach(([key, value]) => {
      const filePath = path.join(this.distPath, value.file);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const size = stats.size;
        const type = path.extname(value.file).slice(1);
        
        files[key] = {
          file: value.file,
          size,
          sizeKB: (size / 1024).toFixed(2),
          type
        };
        
        totalSize += size;
      }
    });

    this.report.bundleSize = {
      files,
      totalSize,
      totalSizeKB: (totalSize / 1024).toFixed(2),
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2)
    };

    // 生成大小建议
    if (totalSize > 1024 * 1024) { // 1MB
      this.report.recommendations.push('Bundle总大小超过1MB，考虑进一步代码分割');
    }

    const jsFiles = Object.values(files).filter(f => f.type === 'js');
    const largeJSFiles = jsFiles.filter(f => f.size > 200 * 1024); // 200KB
    
    if (largeJSFiles.length > 0) {
      this.report.recommendations.push(`发现${largeJSFiles.length}个大型JS文件，考虑拆分`);
    }

    console.log(`✅ Bundle分析完成，总大小: ${this.report.bundleSize.totalSizeKB} KB`);
  }

  /**
   * 分析依赖
   */
  analyzeDependencies() {
    console.log('📦 分析依赖...');
    
    try {
      const packagePath = path.resolve(__dirname, '../package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const heavyDeps = [];
      const unusedDeps = [];

      // 检查重型依赖
      Object.keys(dependencies).forEach(dep => {
        if (dep.includes('lodash') && !dep.includes('lodash-es')) {
          heavyDeps.push(`${dep}: 建议使用lodash-es减少Bundle大小`);
        }
        if (dep.includes('moment') && !dependencies['date-fns']) {
          heavyDeps.push(`${dep}: 建议使用date-fns替代moment.js`);
        }
      });

      this.report.dependencies = {
        total: Object.keys(dependencies).length,
        heavyDeps,
        unusedDeps
      };

      if (heavyDeps.length > 0) {
        this.report.recommendations.push(...heavyDeps);
      }

      console.log(`✅ 依赖分析完成，共${Object.keys(dependencies).length}个依赖`);
    } catch (error) {
      console.error('❌ 依赖分析失败:', error.message);
    }
  }

  /**
   * 检查代码质量
   */
  analyzeCodeQuality() {
    console.log('🔍 检查代码质量...');
    
    try {
      // 运行TypeScript检查
      execSync('npm run type-check', { stdio: 'pipe' });
      this.report.metrics.typeCheck = 'passed';
      console.log('✅ TypeScript检查通过');
    } catch (error) {
      this.report.metrics.typeCheck = 'failed';
      this.report.recommendations.push('TypeScript类型检查失败，请修复类型错误');
      console.log('❌ TypeScript检查失败');
    }

    try {
      // 运行ESLint检查
      execSync('npm run lint', { stdio: 'pipe' });
      this.report.metrics.lint = 'passed';
      console.log('✅ ESLint检查通过');
    } catch (error) {
      this.report.metrics.lint = 'failed';
      this.report.recommendations.push('ESLint检查失败，请修复代码质量问题');
      console.log('❌ ESLint检查失败');
    }
  }

  /**
   * 生成性能建议
   */
  generateRecommendations() {
    console.log('💡 生成性能建议...');

    const { bundleSize } = this.report;
    
    // Bundle大小建议
    if (bundleSize.totalSize > 500 * 1024) {
      this.report.recommendations.push('考虑启用gzip压缩减少传输大小');
    }

    // 代码分割建议
    const jsFiles = Object.values(bundleSize.files || {}).filter(f => f.type === 'js');
    if (jsFiles.length < 3) {
      this.report.recommendations.push('考虑进一步代码分割，提高缓存效率');
    }

    // 性能优化建议
    this.report.recommendations.push(
      '使用React.memo优化组件重渲染',
      '实现虚拟化长列表',
      '使用懒加载减少初始Bundle大小',
      '优化图片资源，使用WebP格式',
      '启用Service Worker缓存静态资源'
    );
  }

  /**
   * 生成报告
   */
  generateReport() {
    const reportPath = path.join(this.distPath, 'performance-report.json');
    const htmlReportPath = path.join(this.distPath, 'performance-report.html');

    // 生成JSON报告
    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));

    // 生成HTML报告
    const htmlReport = this.generateHTMLReport();
    fs.writeFileSync(htmlReportPath, htmlReport);

    console.log(`📄 性能报告已生成:`);
    console.log(`   JSON: ${reportPath}`);
    console.log(`   HTML: ${htmlReportPath}`);
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport() {
    const { bundleSize, dependencies, metrics, recommendations } = this.report;

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notion to WordPress - 性能报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 40px; }
        .header { border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .metric { background: #f9fafb; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .recommendation { background: #fef3c7; padding: 10px; border-radius: 6px; margin: 5px 0; }
        .success { color: #059669; }
        .warning { color: #d97706; }
        .error { color: #dc2626; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        th { background: #f9fafb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Notion to WordPress - 性能报告</h1>
        <p>生成时间: ${this.report.timestamp}</p>
    </div>

    <div class="section">
        <h2>Bundle大小分析</h2>
        <div class="metric">
            <strong>总大小:</strong> ${bundleSize.totalSizeKB} KB (${bundleSize.totalSizeMB} MB)
        </div>
        <table>
            <thead>
                <tr><th>文件</th><th>大小</th><th>类型</th></tr>
            </thead>
            <tbody>
                ${Object.values(bundleSize.files || {}).map(file => `
                    <tr>
                        <td>${file.file}</td>
                        <td>${file.sizeKB} KB</td>
                        <td>${file.type}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>代码质量检查</h2>
        <div class="metric">
            <strong>TypeScript检查:</strong> 
            <span class="${metrics.typeCheck === 'passed' ? 'success' : 'error'}">
                ${metrics.typeCheck === 'passed' ? '✅ 通过' : '❌ 失败'}
            </span>
        </div>
        <div class="metric">
            <strong>ESLint检查:</strong> 
            <span class="${metrics.lint === 'passed' ? 'success' : 'error'}">
                ${metrics.lint === 'passed' ? '✅ 通过' : '❌ 失败'}
            </span>
        </div>
    </div>

    <div class="section">
        <h2>性能优化建议</h2>
        ${recommendations.map(rec => `<div class="recommendation">💡 ${rec}</div>`).join('')}
    </div>
</body>
</html>
    `;
  }

  /**
   * 运行完整分析
   */
  run() {
    console.log('🚀 开始性能分析...\n');
    
    this.analyzeBundleSize();
    this.analyzeDependencies();
    this.analyzeCodeQuality();
    this.generateRecommendations();
    this.generateReport();
    
    console.log('\n✨ 性能分析完成！');
    
    // 输出摘要
    console.log('\n📊 摘要:');
    console.log(`   Bundle大小: ${this.report.bundleSize.totalSizeKB} KB`);
    console.log(`   优化建议: ${this.report.recommendations.length} 条`);
    
    if (this.report.bundleSize.totalSize > 1024 * 1024) {
      console.log('\n⚠️  警告: Bundle大小超过1MB，建议优化');
    } else {
      console.log('\n✅ Bundle大小合理');
    }
  }
}

// 运行分析
const analyzer = new PerformanceAnalyzer();
analyzer.run();
