/**
 * FormField组件测试
 * 
 * @since 2.0.0
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { FormField } from './FormField'
import { TextInput } from './FormInput'

describe('FormField', () => {
  it('应该正确渲染标签和输入框', () => {
    render(
      <FormField label="测试标签" id="test-field">
        <TextInput placeholder="测试输入框" />
      </FormField>
    )

    expect(screen.getByText('测试标签')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('测试输入框')).toBeInTheDocument()
  })

  it('应该显示必填标记', () => {
    render(
      <FormField label="必填字段" required>
        <TextInput />
      </FormField>
    )

    expect(screen.getByText('*')).toBeInTheDocument()
  })

  it('应该显示描述文本', () => {
    render(
      <FormField label="字段" description="这是描述文本">
        <TextInput />
      </FormField>
    )

    expect(screen.getByText('这是描述文本')).toBeInTheDocument()
  })

  it('应该显示错误信息', () => {
    const error = { message: '这是错误信息', type: 'required' }
    
    render(
      <FormField label="字段" error={error}>
        <TextInput />
      </FormField>
    )

    expect(screen.getByText('这是错误信息')).toBeInTheDocument()
    expect(screen.getByRole('alert')).toBeInTheDocument()
  })

  it('应该显示警告信息', () => {
    render(
      <FormField label="字段" warning="这是警告信息">
        <TextInput />
      </FormField>
    )

    expect(screen.getByText('这是警告信息')).toBeInTheDocument()
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('应该显示成功信息', () => {
    render(
      <FormField label="字段" success="验证成功">
        <TextInput />
      </FormField>
    )

    expect(screen.getByText('验证成功')).toBeInTheDocument()
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('应该正确设置aria属性', () => {
    const error = { message: '错误信息', type: 'required' }

    render(
      <FormField label="字段" error={error} id="test-field">
        <TextInput />
      </FormField>
    )

    // 检查错误反馈元素存在
    expect(screen.getByText('错误信息')).toBeInTheDocument()
    // 检查反馈容器有正确的ID
    const feedbackContainer = screen.getByRole('alert')
    expect(feedbackContainer).toHaveAttribute('id', 'test-field-feedback')
  })

  it('应该应用正确的CSS类', () => {
    const error = { message: '错误信息', type: 'required' }

    render(
      <FormField label="字段" error={error}>
        <TextInput />
      </FormField>
    )

    // 检查FormField容器有正确的类
    const formField = screen.getByText('字段').closest('.form-field')
    expect(formField).toHaveClass('form-field')
  })
})
