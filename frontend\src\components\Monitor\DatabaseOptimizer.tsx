/**
 * 数据库优化器组件
 * 
 * 提供数据库索引优化功能：
 * - 索引状态检查
 * - 一键优化所有索引
 * - 性能提升统计
 * - 索引管理操作
 * 
 * @since 2.0.0
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, Button } from '../Common'
import {
  CircleStackIcon,
  BoltIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { useI18n } from '../../hooks/useI18n'
import { ApiService } from '../../services/api'
import type { DatabaseIndex } from '../../types'

interface IndexInfo {
  name: string
  table: string
  columns: string[]
  exists: boolean
  description: string
  status: 'optimal' | 'needed' | 'redundant'
  impact: 'high' | 'medium' | 'low'
  size?: string
  usage?: number
}

interface OptimizationResult {
  success: boolean
  message: string
  created_indexes: string[]
  performance_improvement: string
  execution_time: number
}

export const DatabaseOptimizer: React.FC = () => {
  const [indexes, setIndexes] = useState<IndexInfo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [isRemoving, setIsRemoving] = useState(false)
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)
  const { __ } = useI18n()
  const apiService = new ApiService()

  // 检查索引状态
  const checkIndexStatus = async () => {
    setIsLoading(true)
    try {
      const response = await apiService.getIndexStatus()
      if (response) {
        // 转换后端数据格式为前端格式
        const indexList: IndexInfo[] = []

        // 处理现有索引
        response.existing_indexes?.forEach((index: DatabaseIndex) => {
          indexList.push({
            name: `${index.table}_${index.column}`,
            table: index.table,
            columns: [index.column],
            exists: true,
            description: `Existing ${index.type} index on ${index.table}.${index.column}`,
            status: 'optimal',
            impact: index.column.includes('page_id') ? 'high' : index.column.includes('sync_time') ? 'medium' : 'low'
          })
        })

        // 处理缺失索引
        response.missing_indexes?.forEach((index: DatabaseIndex) => {
          indexList.push({
            name: `${index.table}_${index.column}`,
            table: index.table,
            columns: [index.column],
            exists: false,
            description: `Recommended ${index.type} index on ${index.table}.${index.column}`,
            status: 'needed',
            impact: index.column.includes('page_id') ? 'high' : index.column.includes('sync_time') ? 'medium' : 'low'
          })
        })

        setIndexes(indexList)
        setLastChecked(new Date())
      }
    } catch (error) {
      console.error(__('Failed to check index status'), error)
    } finally {
      setIsLoading(false)
    }
  }

  // 优化所有索引
  const optimizeAllIndexes = async () => {
    setIsOptimizing(true)
    setOptimizationResult(null)

    try {
      const response = await apiService.optimizeAllIndexes()
      if (response.success) {
        setOptimizationResult({
          success: true,
          message: response.message || __('Indexes optimized successfully'),
          created_indexes: response.data?.created_indexes || [],
          performance_improvement: response.data?.performance_improvement || __('Performance improved'),
          execution_time: response.data?.execution_time || 0
        })
        // 重新检查索引状态
        await checkIndexStatus()
      } else {
        setOptimizationResult({
          success: false,
          message: response.message || __('Optimization failed'),
          created_indexes: [],
          performance_improvement: '',
          execution_time: 0
        })
      }
    } catch (error) {
      setOptimizationResult({
        success: false,
        message: __('Error occurred during optimization'),
        created_indexes: [],
        performance_improvement: '',
        execution_time: 0
      })
    } finally {
      setIsOptimizing(false)
    }
  }

  // 删除所有索引
  const removeAllIndexes = async () => {
    if (!confirm(__('Are you sure you want to remove all Notion plugin indexes? This will reduce query performance.'))) {
      return
    }

    setIsRemoving(true)
    try {
      const response = await apiService.removeDatabaseIndexes()
      if (response.success) {
        await checkIndexStatus()
      }
    } catch (error) {
      console.error(__('Failed to remove indexes'), error)
    } finally {
      setIsRemoving(false)
    }
  }

  // 组件挂载时检查索引状态
  useEffect(() => {
    checkIndexStatus()
  }, [])

  // 获取状态统计
  const getStatusStats = () => {
    const total = indexes.length
    const optimal = indexes.filter(idx => idx.status === 'optimal').length
    const needed = indexes.filter(idx => idx.status === 'needed').length
    const redundant = indexes.filter(idx => idx.status === 'redundant').length

    return { total, optimal, needed, redundant }
  }

  const stats = getStatusStats()

  // 渲染索引状态图标
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'optimal':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'needed':
        return <XCircleIcon className="h-4 w-4 text-red-500" />
      case 'redundant':
        return <XCircleIcon className="h-4 w-4 text-yellow-500" />
      default:
        return <CircleStackIcon className="h-4 w-4 text-gray-400" />
    }
  }

  // 渲染影响级别标签
  const renderImpactBadge = (impact: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }

    const labels = {
      high: '高影响',
      medium: '中影响',
      low: '低影响'
    }

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[impact as keyof typeof colors]}`}>
        {labels[impact as keyof typeof labels]}
      </span>
    )
  }

  return (
    <Card
      title={`🗄️ ${__('Database Index Optimization')}`}
      subtitle={__('Optimize database indexes to improve query performance')}
      shadow="md"
    >
      <CardContent className="space-y-6">
        {/* 状态概览 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
            <div className="text-lg font-semibold text-blue-600">{stats.total}</div>
            <div className="text-sm text-blue-700">{__('Total Indexes')}</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
            <div className="text-lg font-semibold text-green-600">{stats.optimal}</div>
            <div className="text-sm text-green-700">{__('Optimized')}</div>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
            <div className="text-lg font-semibold text-red-600">{stats.needed}</div>
            <div className="text-sm text-red-700">{__('Need Optimization')}</div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
            <div className="text-lg font-semibold text-yellow-600">{stats.redundant}</div>
            <div className="text-sm text-yellow-700">{__('Redundant')}</div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-3">
          <Button
            variant="primary"
            onClick={optimizeAllIndexes}
            loading={isOptimizing}
            disabled={isOptimizing || isLoading}
          >
            <BoltIcon className="h-4 w-4 mr-2" />
            {isOptimizing ? __('Optimizing...') : __('Optimize All Indexes')}
          </Button>

          <Button
            variant="secondary"
            onClick={checkIndexStatus}
            loading={isLoading}
            disabled={isLoading}
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            {isLoading ? __('Checking...') : __('Refresh Status')}
          </Button>

          {stats.optimal > 0 && (
            <Button
              variant="danger"
              onClick={removeAllIndexes}
              loading={isRemoving}
              disabled={isRemoving}
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              {isRemoving ? __('Removing...') : __('Remove Indexes')}
            </Button>
          )}
        </div>

        {/* 优化结果 */}
        {optimizationResult && (
          <div className={`p-4 rounded-lg border ${
            optimizationResult.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-start space-x-3">
              {optimizationResult.success ? (
                <CheckCircleIcon className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
              ) : (
                <XCircleIcon className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
              )}
              <div className="flex-1">
                <h4 className={`text-sm font-medium mb-2 ${
                  optimizationResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {optimizationResult.message}
                </h4>
                
                {optimizationResult.success && (
                  <div className="space-y-1 text-sm text-green-700">
                    <p><strong>{__('Created indexes')}:</strong> {optimizationResult.created_indexes.length} {__('items')}</p>
                    <p><strong>{__('Expected performance improvement')}:</strong> {optimizationResult.performance_improvement}</p>
                    <p><strong>{__('Execution time')}:</strong> {optimizationResult.execution_time}ms</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 索引详情列表 */}
        {!isLoading && indexes.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">{__('Index Details')}</h4>
            <div className="space-y-2">
              {indexes.map((index, i) => (
                <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {renderStatusIcon(index.status)}
                    <div>
                      <div className="text-sm font-medium text-gray-900">{index.name}</div>
                      <div className="text-xs text-gray-500">
                        {index.table} ({index.columns.join(', ')})
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {index.description}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {renderImpactBadge(index.impact)}
                    {index.exists && (
                      <span className="text-xs text-green-600 font-medium">{__('Exists')}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 说明信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">🚀 {__('One-Click Optimization Guide')}</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>{__('Dedicated Indexes')}</strong>: {__('5 high-performance indexes optimized for Notion query patterns')}</li>
            <li>• <strong>{__('Smart Coverage')}</strong>: {__('Comprehensive optimization including meta_key, composite indexes, covering indexes')}</li>
            <li>• <strong>{__('Zero Risk Operation')}</strong>: {__('Safe creation, no impact on existing data, can be removed anytime')}</li>
            <li>• <strong>{__('Performance Boost')}</strong>: {__('Expected 20-30% query speed improvement, more significant for large datasets')}</li>
          </ul>
        </div>

        {/* 最后检查时间 */}
        {lastChecked && (
          <p className="text-xs text-gray-500 text-center">
            {__('Last checked')}: {lastChecked.toLocaleString()}
          </p>
        )}
      </CardContent>
    </Card>
  )
}