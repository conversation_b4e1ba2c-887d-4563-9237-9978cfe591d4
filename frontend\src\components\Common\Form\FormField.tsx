/**
 * 通用表单字段组件
 * 
 * 提供统一的表单字段样式和验证反馈显示
 * 
 * @since 2.0.0
 */

import React, { ReactNode } from 'react'
import { clsx } from 'clsx'
import { FieldError } from 'react-hook-form'

export interface FormFieldProps {
  /** 字段标签 */
  label?: string
  /** 字段描述 */
  description?: string
  /** 是否必填 */
  required?: boolean
  /** 验证错误信息 */
  error?: FieldError | string
  /** 警告信息 */
  warning?: string
  /** 成功信息 */
  success?: string
  /** 字段ID */
  id?: string
  /** 自定义类名 */
  className?: string
  /** 子组件 */
  children: React.ReactNode
}

/**
 * 表单字段容器组件
 */
export const FormField: React.FC<FormFieldProps> = ({
  label,
  description,
  required = false,
  error,
  warning,
  success,
  id,
  className,
  children,
}) => {
  const fieldId = id || `field-${Math.random().toString(36).substr(2, 9)}`
  
  // 确定验证状态
  const validationLevel = error ? 'error' : warning ? 'warning' : success ? 'success' : null
  
  // 获取验证消息
  const validationMessage = error 
    ? (typeof error === 'string' ? error : error.message)
    : warning || success

  return (
    <div className={clsx('form-field', className)}>
      {/* 字段标签 */}
      {label && (
        <label 
          htmlFor={fieldId}
          className={clsx(
            'form-field-label',
            'block text-sm font-medium mb-1',
            {
              'text-red-700': validationLevel === 'error',
              'text-yellow-700': validationLevel === 'warning',
              'text-green-700': validationLevel === 'success',
              'text-gray-700': !validationLevel,
            }
          )}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* 字段描述 */}
      {description && (
        <p className="form-field-description text-sm text-gray-600 mb-2">
          {description}
        </p>
      )}

      {/* 表单控件容器 */}
      <div className="input-with-validation">
        {/* 渲染子组件 */}
        <div className="form-field-input">
          {children}
        </div>

        {/* 验证反馈 */}
        {validationMessage && (
          <div
            id={`${fieldId}-feedback`}
            className={clsx(
              'validation-feedback',
              'mt-1 text-sm',
              {
                'text-red-600': validationLevel === 'error',
                'text-yellow-600': validationLevel === 'warning',
                'text-green-600': validationLevel === 'success',
              }
            )}
            role={validationLevel === 'error' ? 'alert' : 'status'}
          >
            {validationLevel === 'error' && (
              <span className="inline-flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {validationMessage}
              </span>
            )}
            {validationLevel === 'warning' && (
              <span className="inline-flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {validationMessage}
              </span>
            )}
            {validationLevel === 'success' && (
              <span className="inline-flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {validationMessage}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default FormField
