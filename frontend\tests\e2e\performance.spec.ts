/**
 * 性能测试
 * 
 * 测试应用的加载性能、响应性能等
 * 
 * @since 2.0.0
 */

import { test, expect } from '@playwright/test'
import { PluginPage } from '../page-objects/PluginPage'
import { checkPagePerformance } from '../fixtures/test-utils'

test.describe('性能测试', () => {
  let pluginPage: PluginPage

  test.beforeEach(async ({ page }) => {
    pluginPage = new PluginPage(page)
  })

  test('页面加载性能应该符合标准', async () => {
    const startTime = Date.now()
    
    // 导航到插件页面
    await pluginPage.goto()
    
    const loadTime = Date.now() - startTime
    
    // 验证加载时间
    expect(loadTime).toBeLessThan(5000) // 5秒内加载完成
    
    // 检查详细性能指标
    const metrics = await checkPagePerformance(pluginPage.page)
    
    console.log('性能指标:', metrics)
    
    // 性能断言
    expect(metrics.totalLoadTime).toBeLessThan(10000) // 总加载时间 < 10秒
    expect(metrics.firstContentfulPaint).toBeLessThan(3000) // FCP < 3秒
    expect(metrics.domContentLoaded).toBeLessThan(2000) // DCL < 2秒
  })

  test('React应用启动性能', async () => {
    await pluginPage.page.goto('/wp-admin/admin.php?page=notion-to-wordpress')
    
    // 测量React应用加载时间
    const reactLoadTime = await pluginPage.page.evaluate(() => {
      return new Promise<number>((resolve) => {
        const startTime = performance.now()
        
        const checkReactReady = () => {
          if (document.querySelector('.notion-wp-admin')) {
            resolve(performance.now() - startTime)
          } else {
            setTimeout(checkReactReady, 10)
          }
        }
        
        checkReactReady()
      })
    })
    
    console.log('React应用加载时间:', reactLoadTime, 'ms')
    expect(reactLoadTime).toBeLessThan(3000) // React应用3秒内启动
  })

  test('大量数据渲染性能', async () => {
    await pluginPage.goto()
    await pluginPage.switchToTab('logs')
    
    // 模拟大量日志数据
    const largeLogs = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      timestamp: new Date().toISOString(),
      level: i % 4 === 0 ? 'error' : i % 3 === 0 ? 'warning' : 'info',
      message: `测试日志消息 ${i} - 这是一条较长的日志消息用于测试渲染性能`,
      context: { page: `page-${i}`, action: `action-${i}` }
    }))
    
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_get_logs')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: largeLogs
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 测量渲染时间
    const renderStartTime = Date.now()
    
    // 刷新日志
    const refreshButton = pluginPage.page.locator('[data-testid="refresh-logs"]')
    await refreshButton.click()
    
    // 等待渲染完成
    await pluginPage.page.waitForSelector('[data-testid="log-item"]')
    
    const renderTime = Date.now() - renderStartTime
    
    console.log('大量数据渲染时间:', renderTime, 'ms')
    expect(renderTime).toBeLessThan(2000) // 2秒内完成渲染
    
    // 验证虚拟化是否生效（只渲染可见项）
    const renderedItems = await pluginPage.page.locator('[data-testid="log-item"]').count()
    expect(renderedItems).toBeLessThan(100) // 应该使用虚拟化，只渲染部分项目
  })

  test('内存使用情况', async () => {
    await pluginPage.goto()
    
    // 获取初始内存使用
    const initialMemory = await pluginPage.page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })
    
    // 执行一系列操作
    const tabs = ['api-settings', 'sync', 'logs', 'performance', 'settings']
    
    for (const tab of tabs) {
      await pluginPage.switchToTab(tab as any)
      await pluginPage.page.waitForTimeout(1000)
    }
    
    // 强制垃圾回收（如果支持）
    await pluginPage.page.evaluate(() => {
      if ((window as any).gc) {
        (window as any).gc()
      }
    })
    
    // 获取最终内存使用
    const finalMemory = await pluginPage.page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })
    
    const memoryIncrease = finalMemory - initialMemory
    const memoryIncreaseMB = memoryIncrease / (1024 * 1024)
    
    console.log('内存增长:', memoryIncreaseMB.toFixed(2), 'MB')
    
    // 内存增长应该在合理范围内
    expect(memoryIncreaseMB).toBeLessThan(50) // 内存增长不超过50MB
  })

  test('API响应性能', async () => {
    await pluginPage.goto()
    await pluginPage.switchToTab('api-settings')
    
    // 测试API调用性能
    const apiCalls = [
      'notion_to_wordpress_get_system_info',
      'notion_to_wordpress_get_performance_metrics',
      'notion_to_wordpress_get_sync_status'
    ]
    
    for (const apiCall of apiCalls) {
      const startTime = Date.now()
      
      await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
        const postData = route.request().postData()
        if (postData?.includes(apiCall)) {
          // 模拟API响应
          setTimeout(() => {
            route.fulfill({
              status: 200,
              contentType: 'application/json',
              body: JSON.stringify({
                success: true,
                data: { test: 'data' }
              })
            })
          }, 100) // 模拟100ms延迟
        } else {
          route.continue()
        }
      })
      
      // 触发API调用
      const refreshButton = pluginPage.page.locator('[data-testid="refresh-data"]').first()
      await refreshButton.click()
      
      // 等待响应
      await pluginPage.page.waitForResponse(response => 
        response.url().includes('admin-ajax.php') && response.status() === 200
      )
      
      const responseTime = Date.now() - startTime
      
      console.log(`${apiCall} 响应时间:`, responseTime, 'ms')
      expect(responseTime).toBeLessThan(1000) // API响应时间 < 1秒
    }
  })

  test('并发操作性能', async () => {
    await pluginPage.goto()
    
    // 并发执行多个操作
    const concurrentOperations = [
      () => pluginPage.switchToTab('api-settings'),
      () => pluginPage.switchToTab('sync'),
      () => pluginPage.switchToTab('logs'),
      () => pluginPage.switchToTab('performance')
    ]
    
    const startTime = Date.now()
    
    // 快速连续执行操作
    for (const operation of concurrentOperations) {
      operation()
      await pluginPage.page.waitForTimeout(100) // 短暂间隔
    }
    
    // 等待所有操作完成
    await pluginPage.page.waitForTimeout(2000)
    
    const totalTime = Date.now() - startTime
    
    console.log('并发操作总时间:', totalTime, 'ms')
    expect(totalTime).toBeLessThan(5000) // 并发操作5秒内完成
  })

  test('长时间运行稳定性', async () => {
    await pluginPage.goto()
    
    // 模拟长时间使用
    const iterations = 20
    const startTime = Date.now()
    
    for (let i = 0; i < iterations; i++) {
      // 随机切换标签页
      const tabs = ['api-settings', 'sync', 'logs', 'performance']
      const randomTab = tabs[Math.floor(Math.random() * tabs.length)]
      
      await pluginPage.switchToTab(randomTab as any)
      await pluginPage.page.waitForTimeout(200)
      
      // 每5次迭代检查一次性能
      if (i % 5 === 0) {
        const currentTime = Date.now()
        const elapsedTime = currentTime - startTime
        const avgTimePerIteration = elapsedTime / (i + 1)
        
        console.log(`迭代 ${i + 1}/${iterations}, 平均时间:`, avgTimePerIteration.toFixed(2), 'ms')
        
        // 确保性能没有显著下降
        expect(avgTimePerIteration).toBeLessThan(1000) // 平均每次操作 < 1秒
      }
    }
    
    const totalTime = Date.now() - startTime
    console.log('长时间运行总时间:', totalTime, 'ms')
    
    // 验证应用仍然响应
    await expect(pluginPage.container).toBeVisible()
    
    // 检查是否有内存泄漏迹象
    const finalMemory = await pluginPage.page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })
    
    const finalMemoryMB = finalMemory / (1024 * 1024)
    console.log('最终内存使用:', finalMemoryMB.toFixed(2), 'MB')
    
    // 内存使用应该在合理范围内
    expect(finalMemoryMB).toBeLessThan(100) // 最终内存使用 < 100MB
  })

  test('网络条件对性能的影响', async () => {
    // 模拟慢速网络
    await pluginPage.page.route('**/*', route => {
      setTimeout(() => route.continue(), 200) // 200ms延迟
    })
    
    const startTime = Date.now()
    await pluginPage.goto()
    const slowNetworkTime = Date.now() - startTime
    
    console.log('慢速网络加载时间:', slowNetworkTime, 'ms')
    
    // 即使在慢速网络下，也应该在合理时间内加载
    expect(slowNetworkTime).toBeLessThan(15000) // 慢速网络15秒内加载
    
    // 恢复正常网络
    await pluginPage.page.unroute('**/*')
    
    // 测试正常网络性能
    const normalStartTime = Date.now()
    await pluginPage.page.reload()
    await pluginPage.waitForLoad()
    const normalNetworkTime = Date.now() - normalStartTime
    
    console.log('正常网络加载时间:', normalNetworkTime, 'ms')
    
    // 正常网络应该明显更快
    expect(normalNetworkTime).toBeLessThan(slowNetworkTime / 2)
  })
})
