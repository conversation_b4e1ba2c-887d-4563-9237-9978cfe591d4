/**
 * 性能监控工具
 * 
 * 监控React组件性能、状态同步延迟、内存使用等关键指标
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'render' | 'state' | 'memory' | 'network' | 'user';
  metadata?: Record<string, any>;
}

interface PerformanceReport {
  metrics: PerformanceMetric[];
  summary: {
    averageRenderTime: number;
    averageStateSyncTime: number;
    memoryUsage: number;
    totalMetrics: number;
  };
  recommendations: string[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = false;
  private maxMetrics: number = 1000;

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development' || 
                     (window as any).wpNotionConfig?.debug === true;
    
    if (this.isEnabled) {
      this.initializeObservers();
    }
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers() {
    // 监控渲染性能
    if ('PerformanceObserver' in window) {
      try {
        const renderObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.name.includes('notion-wp') || entry.name.includes('react')) {
              this.addMetric({
                name: entry.name,
                value: entry.duration,
                timestamp: Date.now(),
                category: 'render',
                metadata: {
                  entryType: entry.entryType,
                  startTime: entry.startTime,
                }
              });
            }
          });
        });

        renderObserver.observe({ entryTypes: ['measure', 'navigation'] });
        this.observers.push(renderObserver);
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }
    }

    // 监控内存使用
    this.startMemoryMonitoring();
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.addMetric({
          name: 'memory-usage',
          value: memory.usedJSHeapSize,
          timestamp: Date.now(),
          category: 'memory',
          metadata: {
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
          }
        });
      }, 5000); // 每5秒检查一次
    }
  }

  /**
   * 添加性能指标
   */
  addMetric(metric: PerformanceMetric) {
    if (!this.isEnabled) return;

    this.metrics.push(metric);

    // 限制指标数量，避免内存泄漏
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // 输出到控制台（开发模式）
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 [Performance] ${metric.name}: ${metric.value.toFixed(2)}ms`, metric);
    }
  }

  /**
   * 测量组件渲染时间
   */
  measureRender<T>(componentName: string, renderFn: () => T): T {
    if (!this.isEnabled) return renderFn();

    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();

    this.addMetric({
      name: `render-${componentName}`,
      value: endTime - startTime,
      timestamp: Date.now(),
      category: 'render',
      metadata: { componentName }
    });

    return result;
  }

  /**
   * 测量状态同步时间
   */
  measureStateSync(operation: string, syncFn: () => void | Promise<void>) {
    if (!this.isEnabled) {
      if (typeof syncFn === 'function') {
        return syncFn();
      }
      return;
    }

    const startTime = performance.now();
    
    const result = syncFn();
    
    if (result instanceof Promise) {
      return result.then(() => {
        const endTime = performance.now();
        this.addMetric({
          name: `state-sync-${operation}`,
          value: endTime - startTime,
          timestamp: Date.now(),
          category: 'state',
          metadata: { operation, async: true }
        });
      });
    } else {
      const endTime = performance.now();
      this.addMetric({
        name: `state-sync-${operation}`,
        value: endTime - startTime,
        timestamp: Date.now(),
        category: 'state',
        metadata: { operation, async: false }
      });
    }
  }

  /**
   * 测量用户交互响应时间
   */
  measureUserInteraction(action: string, interactionFn: () => void) {
    if (!this.isEnabled) return interactionFn();

    const startTime = performance.now();
    interactionFn();
    const endTime = performance.now();

    this.addMetric({
      name: `user-${action}`,
      value: endTime - startTime,
      timestamp: Date.now(),
      category: 'user',
      metadata: { action }
    });
  }

  /**
   * 获取性能报告
   */
  getReport(): PerformanceReport {
    const renderMetrics = this.metrics.filter(m => m.category === 'render');
    const stateMetrics = this.metrics.filter(m => m.category === 'state');
    const memoryMetrics = this.metrics.filter(m => m.category === 'memory');

    const averageRenderTime = renderMetrics.length > 0 
      ? renderMetrics.reduce((sum, m) => sum + m.value, 0) / renderMetrics.length 
      : 0;

    const averageStateSyncTime = stateMetrics.length > 0
      ? stateMetrics.reduce((sum, m) => sum + m.value, 0) / stateMetrics.length
      : 0;

    const latestMemory = memoryMetrics[memoryMetrics.length - 1];
    const memoryUsage = latestMemory ? latestMemory.value : 0;

    const recommendations = this.generateRecommendations({
      averageRenderTime,
      averageStateSyncTime,
      memoryUsage,
      totalMetrics: this.metrics.length
    });

    return {
      metrics: this.metrics,
      summary: {
        averageRenderTime,
        averageStateSyncTime,
        memoryUsage,
        totalMetrics: this.metrics.length
      },
      recommendations
    };
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(summary: PerformanceReport['summary']): string[] {
    const recommendations: string[] = [];

    if (summary.averageRenderTime > 16) {
      recommendations.push('组件渲染时间过长，考虑使用React.memo或useMemo优化');
    }

    if (summary.averageStateSyncTime > 10) {
      recommendations.push('状态同步延迟较高，考虑优化状态桥接器的防抖设置');
    }

    if (summary.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('内存使用量较高，检查是否存在内存泄漏');
    }

    if (summary.totalMetrics > 800) {
      recommendations.push('性能指标数量较多，考虑增加采样率或清理旧数据');
    }

    return recommendations;
  }

  /**
   * 清理性能数据
   */
  clear() {
    this.metrics = [];
  }

  /**
   * 导出性能数据
   */
  export(): string {
    return JSON.stringify(this.getReport(), null, 2);
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clear();
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 导出类型
export type { PerformanceMetric, PerformanceReport };

// 便捷函数
export const measureRender = (componentName: string, renderFn: () => any) => 
  performanceMonitor.measureRender(componentName, renderFn);

export const measureStateSync = (operation: string, syncFn: () => void | Promise<void>) =>
  performanceMonitor.measureStateSync(operation, syncFn);

export const measureUserInteraction = (action: string, interactionFn: () => void) =>
  performanceMonitor.measureUserInteraction(action, interactionFn);

// 全局访问
if (typeof window !== 'undefined') {
  (window as any).notionWpPerformance = performanceMonitor;
}

export default performanceMonitor;
