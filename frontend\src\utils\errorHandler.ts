/**
 * 统一错误处理系统
 * 
 * 提供错误分类、处理、恢复和用户反馈的完整解决方案
 * 
 * @since 2.0.0
 */

import { useUIStore } from '../stores/uiStore'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // API错误
  API_ERROR = 'API_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 业务逻辑错误
  SYNC_ERROR = 'SYNC_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  
  // 系统错误
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  PERFORMANCE_ERROR = 'PERFORMANCE_ERROR',
  
  // 用户错误
  USER_INPUT_ERROR = 'USER_INPUT_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  
  // 未知错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  LOW = 'low',        // 轻微错误，不影响主要功能
  MEDIUM = 'medium',  // 中等错误，影响部分功能
  HIGH = 'high',      // 严重错误，影响主要功能
  CRITICAL = 'critical' // 致命错误，系统无法正常工作
}

/**
 * 错误恢复策略
 */
export enum RecoveryStrategy {
  RETRY = 'retry',           // 重试操作
  FALLBACK = 'fallback',     // 使用备用方案
  REFRESH = 'refresh',       // 刷新页面
  REDIRECT = 'redirect',     // 重定向到其他页面
  MANUAL = 'manual',         // 需要手动处理
  IGNORE = 'ignore'          // 忽略错误
}

/**
 * 应用错误接口
 */
export interface AppError {
  readonly id: string
  readonly type: ErrorType
  readonly severity: ErrorSeverity
  readonly message: string
  readonly details?: string
  readonly code?: string
  readonly timestamp: Date
  readonly context?: Record<string, unknown>
  readonly stack?: string
  readonly recoveryStrategy: RecoveryStrategy
  readonly userMessage: string
  readonly actionable: boolean
}

/**
 * 错误处理配置
 */
export interface ErrorHandlerConfig {
  enableLogging: boolean
  enableReporting: boolean
  enableUserNotification: boolean
  maxRetryAttempts: number
  retryDelay: number
  reportingEndpoint?: string
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private config: ErrorHandlerConfig
  private errorHistory: AppError[] = []
  private retryAttempts: Map<string, number> = new Map()

  private constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableLogging: true,
      enableReporting: process.env.NODE_ENV === 'production',
      enableUserNotification: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      ...config
    }
  }

  static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config)
    }
    return ErrorHandler.instance
  }

  /**
   * 处理错误
   */
  async handleError(error: Error | AppError, context?: Record<string, unknown>): Promise<void> {
    const appError = this.normalizeError(error, context)
    
    // 记录错误
    this.logError(appError)
    
    // 添加到历史记录
    this.addToHistory(appError)
    
    // 用户通知
    if (this.config.enableUserNotification) {
      this.notifyUser(appError)
    }
    
    // 错误恢复
    await this.attemptRecovery(appError)
    
    // 错误上报
    if (this.config.enableReporting) {
      this.reportError(appError)
    }
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: Error | AppError, context?: Record<string, unknown>): AppError {
    if (this.isAppError(error)) {
      return error
    }

    // 根据错误信息推断错误类型
    const type = this.inferErrorType(error)
    const severity = this.inferSeverity(type, error)
    const recoveryStrategy = this.getRecoveryStrategy(type, severity)
    const userMessage = this.generateUserMessage(type, error.message)

    return {
      id: this.generateErrorId(),
      type,
      severity,
      message: error.message,
      details: error.stack,
      timestamp: new Date(),
      context,
      stack: error.stack,
      recoveryStrategy,
      userMessage,
      actionable: this.isActionable(type, severity)
    }
  }

  /**
   * 推断错误类型
   */
  private inferErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK_ERROR
    }
    if (message.includes('timeout')) {
      return ErrorType.TIMEOUT_ERROR
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorType.AUTHENTICATION_ERROR
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorType.AUTHORIZATION_ERROR
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.VALIDATION_ERROR
    }
    if (message.includes('sync') || message.includes('notion')) {
      return ErrorType.SYNC_ERROR
    }
    if (message.includes('memory') || message.includes('heap')) {
      return ErrorType.MEMORY_ERROR
    }
    
    return ErrorType.UNKNOWN_ERROR
  }

  /**
   * 推断错误严重程度
   */
  private inferSeverity(type: ErrorType, error: Error): ErrorSeverity {
    switch (type) {
      case ErrorType.AUTHENTICATION_ERROR:
      case ErrorType.AUTHORIZATION_ERROR:
      case ErrorType.SYSTEM_ERROR:
        return ErrorSeverity.CRITICAL
      
      case ErrorType.SYNC_ERROR:
      case ErrorType.CONFIGURATION_ERROR:
      case ErrorType.API_ERROR:
        return ErrorSeverity.HIGH
      
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
      case ErrorType.VALIDATION_ERROR:
        return ErrorSeverity.MEDIUM
      
      default:
        return ErrorSeverity.LOW
    }
  }

  /**
   * 获取恢复策略
   */
  private getRecoveryStrategy(type: ErrorType, severity: ErrorSeverity): RecoveryStrategy {
    switch (type) {
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
        return RecoveryStrategy.RETRY
      
      case ErrorType.AUTHENTICATION_ERROR:
        return RecoveryStrategy.REDIRECT
      
      case ErrorType.VALIDATION_ERROR:
      case ErrorType.USER_INPUT_ERROR:
        return RecoveryStrategy.MANUAL
      
      case ErrorType.SYSTEM_ERROR:
      case ErrorType.MEMORY_ERROR:
        return severity === ErrorSeverity.CRITICAL 
          ? RecoveryStrategy.REFRESH 
          : RecoveryStrategy.FALLBACK
      
      default:
        return RecoveryStrategy.MANUAL
    }
  }

  /**
   * 生成用户友好的错误消息
   */
  private generateUserMessage(type: ErrorType, originalMessage: string): string {
    const messages = {
      [ErrorType.NETWORK_ERROR]: '网络连接出现问题，请检查网络设置后重试',
      [ErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
      [ErrorType.AUTHENTICATION_ERROR]: '身份验证失败，请重新登录',
      [ErrorType.AUTHORIZATION_ERROR]: '权限不足，请联系管理员',
      [ErrorType.VALIDATION_ERROR]: '输入数据有误，请检查后重新提交',
      [ErrorType.SYNC_ERROR]: '同步过程中出现错误，请检查API配置',
      [ErrorType.CONFIGURATION_ERROR]: '配置错误，请检查设置',
      [ErrorType.SYSTEM_ERROR]: '系统错误，请刷新页面或联系技术支持',
      [ErrorType.MEMORY_ERROR]: '内存不足，请关闭其他应用程序',
      [ErrorType.USER_INPUT_ERROR]: '输入有误，请检查并重新输入',
      [ErrorType.UNKNOWN_ERROR]: '发生未知错误，请稍后重试'
    }

    return messages[type] || originalMessage
  }

  /**
   * 判断错误是否可操作
   */
  private isActionable(type: ErrorType, severity: ErrorSeverity): boolean {
    const actionableTypes = [
      ErrorType.VALIDATION_ERROR,
      ErrorType.USER_INPUT_ERROR,
      ErrorType.CONFIGURATION_ERROR,
      ErrorType.NETWORK_ERROR,
      ErrorType.TIMEOUT_ERROR
    ]

    return actionableTypes.includes(type) || severity === ErrorSeverity.LOW
  }

  /**
   * 记录错误
   */
  private logError(error: AppError): void {
    if (!this.config.enableLogging) return

    const logLevel = this.getLogLevel(error.severity)
    const logMessage = `[${error.type}] ${error.message}`
    
    console[logLevel](logMessage, {
      id: error.id,
      severity: error.severity,
      timestamp: error.timestamp,
      context: error.context,
      stack: error.stack
    })
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      default:
        return 'info'
    }
  }

  /**
   * 通知用户
   */
  private notifyUser(error: AppError): void {
    const { showError, showWarning, showInfo } = useUIStore.getState()
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        showError(error.userMessage, error.details)
        break
      case ErrorSeverity.MEDIUM:
        showWarning(error.userMessage, error.details)
        break
      default:
        showInfo(error.userMessage)
        break
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: AppError): Promise<void> {
    switch (error.recoveryStrategy) {
      case RecoveryStrategy.RETRY:
        await this.retryOperation(error)
        break
      case RecoveryStrategy.REFRESH:
        this.refreshPage()
        break
      case RecoveryStrategy.REDIRECT:
        this.redirectToLogin()
        break
      case RecoveryStrategy.FALLBACK:
        await this.useFallback(error)
        break
      default:
        // 手动处理或忽略
        break
    }
  }

  /**
   * 重试操作
   */
  private async retryOperation(error: AppError): Promise<void> {
    const attempts = this.retryAttempts.get(error.id) || 0
    
    if (attempts >= this.config.maxRetryAttempts) {
      console.warn(`最大重试次数已达到: ${error.id}`)
      return
    }

    this.retryAttempts.set(error.id, attempts + 1)
    
    setTimeout(() => {
      console.info(`重试操作 ${attempts + 1}/${this.config.maxRetryAttempts}: ${error.id}`)
      // 这里应该重新执行失败的操作
    }, this.config.retryDelay * Math.pow(2, attempts)) // 指数退避
  }

  /**
   * 刷新页面
   */
  private refreshPage(): void {
    window.location.reload()
  }

  /**
   * 重定向到登录页
   */
  private redirectToLogin(): void {
    window.location.href = '/wp-admin/'
  }

  /**
   * 使用备用方案
   */
  private async useFallback(error: AppError): Promise<void> {
    console.info(`使用备用方案处理错误: ${error.id}`)
    // 实现具体的备用逻辑
  }

  /**
   * 上报错误
   */
  private reportError(error: AppError): void {
    if (!this.config.reportingEndpoint) return

    fetch(this.config.reportingEndpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        error: {
          id: error.id,
          type: error.type,
          severity: error.severity,
          message: error.message,
          timestamp: error.timestamp,
          context: error.context,
          userAgent: navigator.userAgent,
          url: window.location.href
        }
      })
    }).catch(reportError => {
      console.warn('错误上报失败:', reportError)
    })
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(error: AppError): void {
    this.errorHistory.unshift(error)
    
    // 限制历史记录数量
    if (this.errorHistory.length > 100) {
      this.errorHistory = this.errorHistory.slice(0, 100)
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): readonly AppError[] {
    return this.errorHistory
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = []
    this.retryAttempts.clear()
  }

  /**
   * 工具方法
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    errorHandler.handleError(event.error, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  })

  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handleError(new Error(event.reason), {
      type: 'unhandledPromiseRejection'
    })
  })
}
