/**
 * 错误监控组件
 * 
 * 显示错误历史、统计和恢复状态
 * 
 * @since 2.0.0
 */

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader } from '../Common/Card'
import { Button } from '../Common/Button'
import { ResponsiveTable, TableColumn } from '../Common/ResponsiveTable'
import { errorHandler, ErrorType, ErrorSeverity } from '../../utils/errorHandler'
import { useI18n } from '../../hooks/useI18n'
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

export const ErrorMonitor: React.FC = () => {
  const { __ } = useI18n()
  const [errorHistory, setErrorHistory] = useState(errorHandler.getErrorHistory())
  const [selectedError, setSelectedError] = useState<any>(null)
  const [showDetails, setShowDetails] = useState(false)

  // 定期更新错误历史
  useEffect(() => {
    const interval = setInterval(() => {
      setErrorHistory(errorHandler.getErrorHistory())
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  // 错误统计
  const errorStats = useMemo(() => {
    const total = errorHistory.length
    const byType = errorHistory.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const bySeverity = errorHistory.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recent = errorHistory.filter(error => 
      Date.now() - error.timestamp.getTime() < 60 * 60 * 1000 // 最近1小时
    ).length

    return { total, byType, bySeverity, recent }
  }, [errorHistory])

  // 表格列定义
  const columns: TableColumn[] = [
    {
      key: 'timestamp',
      title: __('Time'),
      dataIndex: 'timestamp',
      render: (timestamp: Date) => timestamp.toLocaleString('zh-CN'),
      priority: 'high'
    },
    {
      key: 'severity',
      title: __('Severity'),
      dataIndex: 'severity',
      render: (severity: ErrorSeverity) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(severity)}`}>
          {getSeverityIcon(severity)}
          <span className="ml-1">{getSeverityText(severity)}</span>
        </span>
      ),
      priority: 'high'
    },
    {
      key: 'type',
      title: __('Type'),
      dataIndex: 'type',
      render: (type: ErrorType) => (
        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {type.replace('_', ' ')}
        </span>
      ),
      priority: 'medium'
    },
    {
      key: 'message',
      title: __('Message'),
      dataIndex: 'userMessage',
      render: (message: string) => (
        <div className="max-w-xs truncate" title={message}>
          {message}
        </div>
      ),
      priority: 'high'
    },
    {
      key: 'actionable',
      title: __('Actionable'),
      dataIndex: 'actionable',
      render: (actionable: boolean) => (
        actionable ? (
          <CheckCircleIcon className="h-5 w-5 text-green-500" />
        ) : (
          <XCircleIcon className="h-5 w-5 text-gray-400" />
        )
      ),
      priority: 'low'
    },
    {
      key: 'actions',
      title: __('Actions'),
      dataIndex: 'id',
      render: (id: string, record: any) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleViewDetails(record)}
          >
            <EyeIcon className="h-4 w-4" />
          </Button>
        </div>
      ),
      priority: 'medium'
    }
  ]

  // 获取严重程度颜色
  const getSeverityColor = (severity: ErrorSeverity): string => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 'bg-red-100 text-red-800'
      case ErrorSeverity.HIGH:
        return 'bg-orange-100 text-orange-800'
      case ErrorSeverity.MEDIUM:
        return 'bg-yellow-100 text-yellow-800'
      case ErrorSeverity.LOW:
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取严重程度图标
  const getSeverityIcon = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return <ExclamationTriangleIcon className="h-3 w-3" />
      case ErrorSeverity.MEDIUM:
        return <ClockIcon className="h-3 w-3" />
      default:
        return <CheckCircleIcon className="h-3 w-3" />
    }
  }

  // 获取严重程度文本
  const getSeverityText = (severity: ErrorSeverity): string => {
    const texts = {
      [ErrorSeverity.CRITICAL]: __('Critical'),
      [ErrorSeverity.HIGH]: __('High'),
      [ErrorSeverity.MEDIUM]: __('Medium'),
      [ErrorSeverity.LOW]: __('Low')
    }
    return texts[severity] || severity
  }

  // 查看错误详情
  const handleViewDetails = (error: any) => {
    setSelectedError(error)
    setShowDetails(true)
  }

  // 清除错误历史
  const handleClearHistory = () => {
    if (confirm(__('Are you sure you want to clear all error history?'))) {
      errorHandler.clearErrorHistory()
      setErrorHistory([])
    }
  }

  // 刷新错误历史
  const handleRefresh = () => {
    setErrorHistory(errorHandler.getErrorHistory())
  }

  return (
    <div className="space-y-6">
      {/* 错误统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{__('Total Errors')}</p>
                <p className="text-2xl font-semibold text-gray-900">{errorStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-orange-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{__('Recent (1h)')}</p>
                <p className="text-2xl font-semibold text-gray-900">{errorStats.recent}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{__('Critical')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {errorStats.bySeverity[ErrorSeverity.CRITICAL] || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{__('Actionable')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {errorHistory.filter(e => e.actionable).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 错误历史表格 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <h3 className="text-lg font-medium">{__('Error History')}</h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              {__('Refresh')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearHistory}
              disabled={errorHistory.length === 0}
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              {__('Clear')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveTable
            columns={columns}
            dataSource={errorHistory}
            pagination={true}
            pageSize={10}
            emptyText={__('No errors recorded')}
          />
        </CardContent>
      </Card>

      {/* 错误详情模态框 */}
      {showDetails && selectedError && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-medium">{__('Error Details')}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDetails(false)}
                >
                  <XCircleIcon className="h-5 w-5" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('Error ID')}</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{selectedError.id}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('Type')}</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedError.type}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('Severity')}</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedError.severity}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('Message')}</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedError.message}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('User Message')}</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedError.userMessage}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">{__('Timestamp')}</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedError.timestamp.toLocaleString('zh-CN')}</p>
                </div>

                {selectedError.context && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">{__('Context')}</label>
                    <pre className="mt-1 text-xs text-gray-900 bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(selectedError.context, null, 2)}
                    </pre>
                  </div>
                )}

                {selectedError.stack && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">{__('Stack Trace')}</label>
                    <pre className="mt-1 text-xs text-gray-900 bg-gray-100 p-2 rounded overflow-auto max-h-40">
                      {selectedError.stack}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
