/**
 * 响应式设计Hook
 * 
 * 提供屏幕尺寸检测和响应式断点管理
 * 
 * @since 2.0.0
 */

import { useState, useEffect, useCallback } from 'react'

// 响应式断点定义（与Tailwind CSS保持一致）
export const BREAKPOINTS = {
  sm: 640,   // 小屏幕（手机横屏）
  md: 768,   // 中等屏幕（平板）
  lg: 1024,  // 大屏幕（桌面）
  xl: 1280,  // 超大屏幕
  '2xl': 1536 // 超超大屏幕
} as const

export type BreakpointKey = keyof typeof BREAKPOINTS

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 屏幕方向
export type Orientation = 'portrait' | 'landscape'

// 响应式状态
export interface ResponsiveState {
  width: number
  height: number
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  deviceType: DeviceType
  orientation: Orientation
  breakpoint: BreakpointKey | null
  isTouch: boolean
}

/**
 * 获取当前断点
 */
const getCurrentBreakpoint = (width: number): BreakpointKey | null => {
  const breakpointEntries = Object.entries(BREAKPOINTS)
    .sort(([, a], [, b]) => b - a) // 从大到小排序
  
  for (const [key, value] of breakpointEntries) {
    if (width >= value) {
      return key as BreakpointKey
    }
  }
  
  return null // 小于最小断点
}

/**
 * 获取设备类型
 */
const getDeviceType = (width: number): DeviceType => {
  if (width < BREAKPOINTS.md) return 'mobile'
  if (width < BREAKPOINTS.lg) return 'tablet'
  return 'desktop'
}

/**
 * 获取屏幕方向
 */
const getOrientation = (width: number, height: number): Orientation => {
  return width > height ? 'landscape' : 'portrait'
}

/**
 * 检测是否为触摸设备
 */
const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 响应式设计Hook
 */
export const useResponsive = () => {
  const [state, setState] = useState<ResponsiveState>(() => {
    // 初始状态（SSR安全）
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        deviceType: 'desktop' as DeviceType,
        orientation: 'landscape' as Orientation,
        breakpoint: 'lg' as BreakpointKey,
        isTouch: false
      }
    }

    const width = window.innerWidth
    const height = window.innerHeight
    const deviceType = getDeviceType(width)
    
    return {
      width,
      height,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      deviceType,
      orientation: getOrientation(width, height),
      breakpoint: getCurrentBreakpoint(width),
      isTouch: isTouchDevice()
    }
  })

  // 更新响应式状态
  const updateState = useCallback(() => {
    if (typeof window === 'undefined') return

    const width = window.innerWidth
    const height = window.innerHeight
    const deviceType = getDeviceType(width)
    
    setState({
      width,
      height,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      deviceType,
      orientation: getOrientation(width, height),
      breakpoint: getCurrentBreakpoint(width),
      isTouch: isTouchDevice()
    })
  }, [])

  // 监听窗口大小变化
  useEffect(() => {
    if (typeof window === 'undefined') return

    // 防抖处理，避免频繁更新
    let timeoutId: number
    const handleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = window.setTimeout(updateState, 100)
    }

    // 初始化
    updateState()

    // 监听事件
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [updateState])

  // 断点检查函数
  const isBreakpoint = useCallback((breakpoint: BreakpointKey): boolean => {
    return state.width >= BREAKPOINTS[breakpoint]
  }, [state.width])

  // 断点范围检查
  const isBetween = useCallback((min: BreakpointKey, max: BreakpointKey): boolean => {
    return state.width >= BREAKPOINTS[min] && state.width < BREAKPOINTS[max]
  }, [state.width])

  // 媒体查询匹配
  const matchMedia = useCallback((query: string): boolean => {
    if (typeof window === 'undefined') return false
    return window.matchMedia(query).matches
  }, [])

  return {
    ...state,
    isBreakpoint,
    isBetween,
    matchMedia,
    // 便捷的断点检查
    isSmUp: isBreakpoint('sm'),
    isMdUp: isBreakpoint('md'),
    isLgUp: isBreakpoint('lg'),
    isXlUp: isBreakpoint('xl'),
    is2XlUp: isBreakpoint('2xl'),
    // 便捷的范围检查
    isSmOnly: isBetween('sm', 'md'),
    isMdOnly: isBetween('md', 'lg'),
    isLgOnly: isBetween('lg', 'xl'),
    isXlOnly: isBetween('xl', '2xl')
  }
}

/**
 * 响应式类名Hook
 * 根据当前设备类型返回相应的CSS类名
 */
export const useResponsiveClasses = () => {
  const { deviceType, orientation, isTouch } = useResponsive()

  return {
    deviceClass: `device-${deviceType}`,
    orientationClass: `orientation-${orientation}`,
    touchClass: isTouch ? 'touch-device' : 'no-touch',
    containerClass: `responsive-container ${deviceType}-container`,
    // 组合类名
    allClasses: [
      `device-${deviceType}`,
      `orientation-${orientation}`,
      isTouch ? 'touch-device' : 'no-touch'
    ].join(' ')
  }
}

/**
 * 移动端优化Hook
 * 专门为移动端交互优化
 */
export const useMobileOptimization = () => {
  const { isMobile, isTouch } = useResponsive()

  // 移动端滚动优化
  const enableMobileScrolling = useCallback(() => {
    if (!isMobile) return

    // 启用iOS弹性滚动
    document.body.style.webkitOverflowScrolling = 'touch'
    
    // 防止双击缩放
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      )
    }
  }, [isMobile])

  // 触摸反馈优化
  const optimizeTouchFeedback = useCallback(() => {
    if (!isTouch) return

    // 减少触摸延迟
    document.body.style.touchAction = 'manipulation'
    
    // 禁用文本选择（在某些交互元素上）
    const style = document.createElement('style')
    style.textContent = `
      .touch-device .no-select {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      
      .touch-device button, 
      .touch-device .clickable {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
      }
    `
    document.head.appendChild(style)
  }, [isTouch])

  useEffect(() => {
    enableMobileScrolling()
    optimizeTouchFeedback()
  }, [enableMobileScrolling, optimizeTouchFeedback])

  return {
    isMobile,
    isTouch,
    enableMobileScrolling,
    optimizeTouchFeedback
  }
}

export default useResponsive
