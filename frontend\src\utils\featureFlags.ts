/**
 * 功能标志管理系统
 * 支持动态控制功能开关，便于A/B测试和渐进式发布
 */

import React, { useEffect, useState } from 'react'

// 功能标志类型定义
export type FeatureFlagKey = 
  | 'react_settings_panel'      // React设置面板
  | 'react_sync_dashboard'      // React同步控制面板
  | 'react_field_mapping'       // React字段映射
  | 'react_error_boundary'      // React错误边界
  | 'performance_monitoring'    // 性能监控
  | 'debug_mode'               // 调试模式
  | 'storybook_enabled'        // Storybook文档
  | 'new_ui_components'        // 新UI组件

// 功能标志配置接口
export interface FeatureFlag {
  key: FeatureFlagKey
  enabled: boolean
  description: string
  environment?: 'development' | 'production' | 'all'
  rolloutPercentage?: number
  dependencies?: FeatureFlagKey[]
}

// 默认功能标志配置
const DEFAULT_FLAGS: Record<FeatureFlagKey, FeatureFlag> = {
  react_settings_panel: {
    key: 'react_settings_panel',
    enabled: false,
    description: 'React设置面板重构',
    environment: 'all'
  },
  react_sync_dashboard: {
    key: 'react_sync_dashboard',
    enabled: false,
    description: 'React同步控制面板',
    environment: 'all',
    dependencies: ['react_settings_panel']
  },
  react_field_mapping: {
    key: 'react_field_mapping',
    enabled: false,
    description: 'React字段映射组件',
    environment: 'all'
  },
  react_error_boundary: {
    key: 'react_error_boundary',
    enabled: true,
    description: 'React错误边界',
    environment: 'all'
  },
  performance_monitoring: {
    key: 'performance_monitoring',
    enabled: true,
    description: '性能监控',
    environment: 'development'
  },
  debug_mode: {
    key: 'debug_mode',
    enabled: true,
    description: '调试模式',
    environment: 'development'
  },
  storybook_enabled: {
    key: 'storybook_enabled',
    enabled: true,
    description: 'Storybook组件文档',
    environment: 'development'
  },
  new_ui_components: {
    key: 'new_ui_components',
    enabled: true,
    description: '新UI组件',
    environment: 'all'
  }
}

// 功能标志存储键
const STORAGE_KEY = 'notion_wp_feature_flags'

/**
 * 功能标志管理器类
 */
class FeatureFlagManager {
  private flags: Map<FeatureFlagKey, FeatureFlag> = new Map()
  private listeners: Set<() => void> = new Set()
  private initialized = false

  constructor() {
    this.initializeFlags()
  }

  /**
   * 初始化功能标志
   */
  private async initializeFlags() {
    try {
      // 从WordPress选项加载功能标志
      const wpFlags = await this.loadFromWordPress()
      
      // 从本地存储加载功能标志（开发环境）
      const localFlags = this.loadFromLocalStorage()
      
      // 合并配置
      Object.values(DEFAULT_FLAGS).forEach(flag => {
        const wpFlag = wpFlags[flag.key]
        const localFlag = localFlags[flag.key]
        
        // 优先级：WordPress选项 > 本地存储 > 默认配置
        const finalFlag: FeatureFlag = {
          ...flag,
          enabled: wpFlag?.enabled ?? localFlag?.enabled ?? flag.enabled
        }
        
        // 检查环境限制
        if (this.isEnvironmentAllowed(finalFlag)) {
          this.flags.set(flag.key, finalFlag)
        }
      })
      
      this.initialized = true
      this.notifyListeners()
    } catch (error) {
      console.error('初始化功能标志失败:', error)
      // 使用默认配置
      Object.values(DEFAULT_FLAGS).forEach(flag => {
        if (this.isEnvironmentAllowed(flag)) {
          this.flags.set(flag.key, flag)
        }
      })
      this.initialized = true
      this.notifyListeners()
    }
  }

  /**
   * 从WordPress选项加载功能标志
   */
  private async loadFromWordPress(): Promise<Record<string, FeatureFlag>> {
    try {
      const formData = new FormData()
      formData.append('action', 'notion_get_feature_flags')
      formData.append('nonce', window.wpNotionConfig?.nonce || '')
      
      const response = await fetch(window.wpNotionConfig?.ajaxUrl || '/wp-admin/admin-ajax.php', {
        method: 'POST',
        body: formData
      })
      
      const result = await response.json()
      
      if (result.success && result.data) {
        return result.data
      }
    } catch (error) {
      console.warn('从WordPress加载功能标志失败:', error)
    }
    
    return {}
  }

  /**
   * 从本地存储加载功能标志
   */
  private loadFromLocalStorage(): Record<string, FeatureFlag> {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      console.warn('从本地存储加载功能标志失败:', error)
      return {}
    }
  }

  /**
   * 检查环境是否允许
   */
  private isEnvironmentAllowed(flag: FeatureFlag): boolean {
    if (!flag.environment || flag.environment === 'all') {
      return true
    }
    
    const isDevelopment = process.env.NODE_ENV === 'development' || window.wpNotionConfig?.isDebug
    
    return flag.environment === 'development' ? isDevelopment : !isDevelopment
  }

  /**
   * 检查依赖是否满足
   */
  private areDependenciesMet(flag: FeatureFlag): boolean {
    if (!flag.dependencies || flag.dependencies.length === 0) {
      return true
    }
    
    return flag.dependencies.every(dep => this.isEnabled(dep))
  }

  /**
   * 获取功能标志状态
   */
  public isEnabled(key: FeatureFlagKey): boolean {
    const flag = this.flags.get(key)
    
    if (!flag) {
      return false
    }
    
    // 检查基本启用状态
    if (!flag.enabled) {
      return false
    }
    
    // 检查依赖
    if (!this.areDependenciesMet(flag)) {
      return false
    }
    
    // 检查推出百分比
    if (flag.rolloutPercentage !== undefined && flag.rolloutPercentage < 100) {
      const hash = this.hashString(key + (window.wpNotionConfig?.nonce || ''))
      const percentage = hash % 100
      return percentage < flag.rolloutPercentage
    }
    
    return true
  }

  /**
   * 设置功能标志状态（仅开发环境）
   */
  public setFlag(key: FeatureFlagKey, enabled: boolean): void {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('功能标志只能在开发环境中修改')
      return
    }
    
    const flag = this.flags.get(key)
    if (flag) {
      const updatedFlag = { ...flag, enabled }
      this.flags.set(key, updatedFlag)
      
      // 保存到本地存储
      this.saveToLocalStorage()
      
      this.notifyListeners()
    }
  }

  /**
   * 保存到本地存储
   */
  private saveToLocalStorage(): void {
    try {
      const flagsObject: Record<string, FeatureFlag> = {}
      this.flags.forEach((flag, key) => {
        flagsObject[key] = flag
      })
      localStorage.setItem(STORAGE_KEY, JSON.stringify(flagsObject))
    } catch (error) {
      console.warn('保存功能标志到本地存储失败:', error)
    }
  }

  /**
   * 简单哈希函数
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 添加监听器
   */
  public addListener(listener: () => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener())
  }

  /**
   * 获取所有功能标志
   */
  public getAllFlags(): FeatureFlag[] {
    return Array.from(this.flags.values())
  }

  /**
   * 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized
  }
}

// 全局功能标志管理器实例
export const featureFlagManager = new FeatureFlagManager()

/**
 * React Hook：使用功能标志
 */
export function useFeatureFlag(key: FeatureFlagKey): boolean {
  const [enabled, setEnabled] = useState(() => featureFlagManager.isEnabled(key))
  
  useEffect(() => {
    const unsubscribe = featureFlagManager.addListener(() => {
      setEnabled(featureFlagManager.isEnabled(key))
    })
    
    return unsubscribe
  }, [key])
  
  return enabled
}

/**
 * React Hook：获取所有功能标志
 */
export function useAllFeatureFlags(): FeatureFlag[] {
  const [flags, setFlags] = useState(() => featureFlagManager.getAllFlags())
  
  useEffect(() => {
    const unsubscribe = featureFlagManager.addListener(() => {
      setFlags(featureFlagManager.getAllFlags())
    })
    
    return unsubscribe
  }, [])
  
  return flags
}

/**
 * 功能标志门控组件
 */
interface FeatureGateProps {
  flag: FeatureFlagKey
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function FeatureGate({ flag, children, fallback = null }: FeatureGateProps) {
  const enabled = useFeatureFlag(flag)
  
  return enabled ? React.createElement(React.Fragment, null, children) : React.createElement(React.Fragment, null, fallback);
};

// 便捷函数
export const isFeatureEnabled = (key: FeatureFlagKey): boolean => {
  return featureFlagManager.isEnabled(key)
}

export const setFeatureFlag = (key: FeatureFlagKey, enabled: boolean): void => {
  featureFlagManager.setFlag(key, enabled)
}
