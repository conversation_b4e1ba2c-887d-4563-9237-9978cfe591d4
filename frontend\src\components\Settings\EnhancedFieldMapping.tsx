/**
 * 增强字段映射组件
 * 
 * 完整实现PHP后台的字段映射功能：
 * - 所有基础字段映射 (title, status, post_type, date, excerpt, featured_image, categories, tags, password)
 * - 自定义字段映射管理
 * - 字段类型支持 (text, number, date, checkbox, select, multi_select, url, email, phone, rich_text)
 * - 字段验证和智能建议
 * 
 * @since 2.0.0
 */

import React, { useState } from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Input, Select } from '../Common'
// 使用Unicode图标替代heroicons
const TrashIcon = ({ className }: { className?: string }) => (
  <span className={className}>🗑️</span>
)
const PlusIcon = ({ className }: { className?: string }) => (
  <span className={className}>➕</span>
)
const InformationCircleIcon = ({ className }: { className?: string }) => (
  <span className={className}>ℹ️</span>
)

// 使用与后端一致的类型
import type { FieldMapping, CustomFieldMapping } from '../../types'

// 扩展CustomFieldMapping以支持更多字段类型
interface ExtendedCustomFieldMapping extends Omit<CustomFieldMapping, 'field_type'> {
  field_type: 'text' | 'number' | 'date' | 'checkbox' | 'select' | 'multi_select' | 'url' | 'email' | 'phone' | 'rich_text'
}

// 字段类型选项
const FIELD_TYPE_OPTIONS = [
  { value: 'text', label: '文本' },
  { value: 'number', label: '数字' },
  { value: 'date', label: '日期' },
  { value: 'checkbox', label: '复选框' },
  { value: 'select', label: '选择' },
  { value: 'multi_select', label: '多选' },
  { value: 'url', label: 'URL' },
  { value: 'email', label: '电子邮件' },
  { value: 'phone', label: '电话' },
  { value: 'rich_text', label: '富文本' }
]

// 基础字段配置（与FieldMapping类型对应）
const BASIC_FIELDS = [
  {
    key: 'title_field',
    label: '文章标题',
    placeholder: 'Title,标题,Name,名称',
    helperText: '用于设置WordPress文章标题的Notion属性名称',
    required: true
  },
  {
    key: 'status_field',
    label: '状态',
    placeholder: 'Status,状态,Published,发布状态',
    helperText: '值为 "Published" 或 "已发布" 的页面会被设为 "已发布" 状态，其他则为 "草稿"'
  },
  {
    key: 'post_type_field',
    label: '文章类型',
    placeholder: 'Type,类型,Post Type,文章类型',
    helperText: '用于确定WordPress文章类型的Notion属性名称'
  },
  {
    key: 'date_field',
    label: '日期',
    placeholder: 'Date,日期,Created,创建日期,Published Date,发布日期',
    helperText: '用于设置WordPress文章发布日期的Notion属性名称'
  },
  {
    key: 'excerpt_field',
    label: '摘要',
    placeholder: 'Summary,摘要,Description,描述,Excerpt,简介',
    helperText: '用于设置WordPress文章摘要的Notion属性名称'
  },
  {
    key: 'featured_image_field',
    label: '特色图片',
    placeholder: 'Featured Image,特色图片,Cover,封面,Image,图片',
    helperText: '用于设置WordPress特色图片的Notion属性名称（应为URL或文件类型）'
  },
  {
    key: 'category_field',
    label: '分类',
    placeholder: 'Categories,分类,Category,类别',
    helperText: '用于设置WordPress文章分类的Notion属性名称'
  },
  {
    key: 'tag_field',
    label: '标签',
    placeholder: 'Tags,标签,Tag',
    helperText: '用于设置WordPress文章标签的Notion属性名称'
  },
  {
    key: 'password_field',
    label: '文章密码',
    placeholder: 'Password,密码,Private,私密',
    helperText: '用于设置WordPress文章密码的Notion属性名称'
  }
]

export const EnhancedFieldMapping: React.FC = () => {
  const { settings, updateSettings, saveSettings, hasUnsavedChanges, isSaving } = useSettingsStore()
  
  // 获取当前字段映射配置
  const fieldMapping: FieldMapping = settings?.field_mapping || {
    title_field: 'Title,标题,Name,名称',
    content_field: '',
    excerpt_field: 'Summary,摘要,Description,描述,Excerpt,简介',
    featured_image_field: 'Featured Image,特色图片,Cover,封面',
    category_field: 'Categories,分类,Category,类别',
    tag_field: 'Tags,标签,Tag',
    status_field: 'Status,状态,Published,发布状态',
    post_type_field: 'Type,类型,Post Type,文章类型',
    date_field: 'Date,日期,Created,创建日期',
    password_field: 'Password,密码,Private,私密',
    custom_fields: []
  }

  // 获取自定义字段映射
  const customFieldMappings: ExtendedCustomFieldMapping[] = (settings?.custom_field_mapping || []).map(field => ({
    ...field,
    field_type: field.field_type as ExtendedCustomFieldMapping['field_type']
  }))

  // 新增自定义字段的状态
  const [newCustomField, setNewCustomField] = useState<ExtendedCustomFieldMapping>({
    notion_field: '',
    wordpress_field: '',
    field_type: 'text'
  })

  // 处理基础字段更改
  const handleBasicFieldChange = (field: keyof FieldMapping, value: string) => {
    updateSettings({
      field_mapping: {
        ...fieldMapping,
        [field]: value
      }
    })
  }

  // 添加自定义字段
  const handleAddCustomField = () => {
    if (newCustomField.notion_field && newCustomField.wordpress_field) {
      const updatedMappings = [...customFieldMappings, newCustomField]
      updateSettings({
        custom_field_mapping: updatedMappings.map(field => ({
          notion_field: field.notion_field,
          wordpress_field: field.wordpress_field,
          field_type: field.field_type as CustomFieldMapping['field_type']
        }))
      })
      
      // 重置表单
      setNewCustomField({
        notion_field: '',
        wordpress_field: '',
        field_type: 'text'
      })
    }
  }

  // 删除自定义字段
  const handleRemoveCustomField = (index: number) => {
    const updatedMappings = customFieldMappings.filter((_, i) => i !== index)
    updateSettings({
      custom_field_mapping: updatedMappings.map(field => ({
        notion_field: field.notion_field,
        wordpress_field: field.wordpress_field,
        field_type: field.field_type as CustomFieldMapping['field_type']
      }))
    })
  }

  // 保存设置
  const handleSave = async () => {
    const success = await saveSettings()
    if (success) {
      console.log('字段映射保存成功')
    }
  }

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          🔗 字段映射
        </h2>
        <p className="text-sm text-gray-600">
          设置您的Notion数据库属性名称与WordPress字段的对应关系。多个备选名称请用英文逗号隔开。
        </p>
      </div>

      {/* 基础字段映射 */}
      <Card
        title="基础字段映射"
        subtitle="配置Notion属性与WordPress标准字段的映射关系"
        shadow="md"
      >
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {BASIC_FIELDS.map((field) => (
              <div key={field.key} className="space-y-2">
                <Input
                  label={field.label}
                  value={String(fieldMapping[field.key as keyof FieldMapping] || '')}
                  onChange={(e) => handleBasicFieldChange(field.key as keyof FieldMapping, e.target.value)}
                  placeholder={field.placeholder}
                  helperText={field.helperText}
                  required={field.required}
                />
              </div>
            ))}
          </div>

          {/* 映射说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-blue-800 mb-2">字段映射说明</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 多个备选名称请用英文逗号隔开，如：Title,标题,名称</li>
                  <li>• 系统会按顺序匹配，找到第一个存在的属性名称</li>
                  <li>• 字段名称匹配不区分大小写</li>
                  <li>• 标题字段为必填项，其他字段为可选</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 自定义字段映射 */}
      <Card
        title="自定义字段映射"
        subtitle="将Notion属性映射到WordPress自定义字段。您可以添加任意数量的自定义字段映射。"
        shadow="md"
      >
        <CardContent className="space-y-6">
          {/* 现有自定义字段列表 */}
          {customFieldMappings.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">已配置的自定义字段</h4>
              <div className="space-y-2">
                {customFieldMappings.map((mapping, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {mapping.notion_field}
                          </div>
                          <div className="text-xs text-gray-500">Notion属性</div>
                        </div>
                        <div className="text-gray-400">→</div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {mapping.wordpress_field}
                          </div>
                          <div className="text-xs text-gray-500">WordPress字段</div>
                        </div>
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {FIELD_TYPE_OPTIONS.find(opt => opt.value === mapping.field_type)?.label}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={() => handleRemoveCustomField(index)}
                      className="ml-4"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 添加新的自定义字段 */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700">添加新的自定义字段映射</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                label="Notion属性名称"
                value={newCustomField.notion_field}
                onChange={(e) => setNewCustomField(prev => ({
                  ...prev,
                  notion_field: e.target.value
                }))}
                placeholder="例如：Price, 价格"
              />
              
              <Input
                label="WordPress字段名称"
                value={newCustomField.wordpress_field}
                onChange={(e) => setNewCustomField(prev => ({
                  ...prev,
                  wordpress_field: e.target.value
                }))}
                placeholder="例如：product_price"
              />
              
              <Select
                label="字段类型"
                value={newCustomField.field_type}
                onChange={(value) => setNewCustomField({
                  ...newCustomField,
                  field_type: value as ExtendedCustomFieldMapping['field_type']
                })}
                options={FIELD_TYPE_OPTIONS}
              />
              
              <div className="flex items-end">
                <Button
                  variant="primary"
                  onClick={handleAddCustomField}
                  disabled={!newCustomField.notion_field || !newCustomField.wordpress_field}
                  className="w-full"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  添加
                </Button>
              </div>
            </div>
          </div>

          {/* 自定义字段说明 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">💡 自定义字段使用说明</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• <strong>Notion属性名称</strong>：在Notion数据库中的属性名称</li>
              <li>• <strong>WordPress字段名称</strong>：在WordPress中存储的自定义字段名称（meta_key）</li>
              <li>• <strong>字段类型</strong>：确定数据如何处理和存储</li>
              <li>• 自定义字段将保存为WordPress文章的meta数据</li>
              <li>• 可以在主题中使用 get_post_meta() 函数获取这些数据</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 保存提示 */}
      {hasUnsavedChanges && (
        <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">您有未保存的字段映射更改</p>
          <Button
            variant="primary"
            onClick={handleSave}
            loading={isSaving}
            disabled={isSaving}
          >
            {isSaving ? '保存中...' : '保存字段映射'}
          </Button>
        </div>
      )}
    </div>
  )
}