/**
 * React Query Provider
 * 
 * 提供统一的服务器状态管理，集成现有API服务
 * 
 * @since 2.0.0
 */

import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

/**
 * 创建QueryClient实例
 */
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // 缓存时间：5分钟
        staleTime: 5 * 60 * 1000,
        // 垃圾回收时间：10分钟
        cacheTime: 10 * 60 * 1000,
        // 重试配置
        retry: (failureCount, error) => {
          // 权限错误不重试
          if (error instanceof Error && error.message.includes('权限')) {
            return false
          }
          // 最多重试2次
          return failureCount < 2
        },
        // 重试延迟：指数退避
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // 窗口聚焦时重新获取
        refetchOnWindowFocus: false,
        // 网络重连时重新获取
        refetchOnReconnect: true,
        // 组件挂载时重新获取
        refetchOnMount: true,
      },
      mutations: {
        // 变更重试配置
        retry: (failureCount, error) => {
          // 权限错误不重试
          if (error instanceof Error && error.message.includes('权限')) {
            return false
          }
          // 最多重试1次
          return failureCount < 1
        },
        // 变更重试延迟
        retryDelay: 1000,
      },
    },
    // 全局错误处理
    mutationCache: {
      onError: (error, variables, context, mutation) => {
        console.error('Mutation error:', error)
        
        // 集成WordPress错误处理
        if (typeof window !== 'undefined' && window.wpNotionConfig?.debug) {
          // 这里可以调用WordPress错误处理API
          console.warn('Error reported to WordPress:', error)
        }
      },
    },
    queryCache: {
      onError: (error, query) => {
        console.error('Query error:', error)
        
        // 集成WordPress错误处理
        if (typeof window !== 'undefined' && window.wpNotionConfig?.debug) {
          console.warn('Query error reported:', error)
        }
      },
    },
  })
}

// 全局QueryClient实例
let queryClient: QueryClient | null = null

/**
 * 获取QueryClient实例
 */
export const getQueryClient = () => {
  if (!queryClient) {
    queryClient = createQueryClient()
  }
  return queryClient
}

/**
 * 重置QueryClient实例（用于测试）
 */
export const resetQueryClient = () => {
  queryClient = null
}

/**
 * QueryProvider组件属性
 */
interface QueryProviderProps {
  children: React.ReactNode
  client?: QueryClient
}

/**
 * QueryProvider组件
 * 
 * 为应用提供React Query功能
 */
export const QueryProvider: React.FC<QueryProviderProps> = ({ 
  children, 
  client 
}) => {
  const queryClientInstance = client || getQueryClient()

  return (
    <QueryClientProvider client={queryClientInstance}>
      {children}
      {/* 开发环境显示DevTools */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}

export default QueryProvider
