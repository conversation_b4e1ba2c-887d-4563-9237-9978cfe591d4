/**
 * Checkbox复选框组件
 * 
 * 提供简洁的复选框功能，支持自定义样式和状态
 * 
 * @since 2.0.0
 */

import React from 'react'
import { cn } from '../../utils/cn'

export interface CheckboxProps {
  label?: string
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  helperText?: string
  error?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  helperText,
  error,
  className,
  size = 'md'
}) => {
  const checkboxId = React.useId()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.checked)
  }

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id={checkboxId}
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className={cn(
              sizeClasses[size],
              'text-blue-600 border-gray-300 rounded',
              'focus:ring-blue-500 focus:border-blue-500',
              'disabled:bg-gray-100 disabled:cursor-not-allowed',
              error && 'border-red-300 focus:ring-red-500 focus:border-red-500'
            )}
          />
        </div>
        {label && (
          <div className="ml-3 text-sm">
            <label 
              htmlFor={checkboxId}
              className={cn(
                'font-medium text-gray-700',
                disabled && 'text-gray-400 cursor-not-allowed'
              )}
            >
              {label}
            </label>
          </div>
        )}
      </div>

      {(helperText || error) && (
        <div className="ml-7">
          {error ? (
            <p className="text-sm text-red-600">{error}</p>
          ) : (
            <p className="text-sm text-gray-500">{helperText}</p>
          )}
        </div>
      )}
    </div>
  )
}