/**
 * Webhook配置组件
 * 
 * 提供完整的Notion Webhook配置功能，包括：
 * - Webhook启用/禁用
 * - 验证令牌管理
 * - Webhook URL展示
 * - 同步选项配置
 * 
 * @since 2.0.0
 */

import React, { useState, useEffect } from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Input, Checkbox } from '../Common'
import { ClipboardDocumentIcon, ArrowPathIcon, CheckIcon } from '@heroicons/react/24/outline'

interface WebhookSettings {
  webhook_enabled: boolean
  webhook_verify_token: string
  webhook_token: string
  webhook_incremental_sync: boolean
  webhook_check_deletions: boolean
}

export const WebhookConfig: React.FC = () => {
  const { settings, updateSettings, saveSettings, hasUnsavedChanges, isSaving } = useSettingsStore()
  const [copiedField, setCopiedField] = useState<string>('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  // 获取Webhook配置
  const webhookSettings: WebhookSettings = {
    webhook_enabled: settings?.webhook_enabled || false,
    webhook_verify_token: settings?.webhook_verify_token || '',
    webhook_token: settings?.webhook_token || '',
    webhook_incremental_sync: settings?.webhook_incremental_sync !== false,
    webhook_check_deletions: settings?.webhook_check_deletions !== false
  }

  // 生成Webhook URL
  const webhookUrl = webhookSettings.webhook_token 
    ? `${window.location.origin}/wp-json/notion-to-wordpress/v1/webhook/${webhookSettings.webhook_token}`
    : ''

  // 处理配置更改
  const handleConfigChange = (field: keyof WebhookSettings, value: boolean | string) => {
    updateSettings({ [field]: value })
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(fieldName)
      setTimeout(() => setCopiedField(''), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 刷新验证令牌
  const refreshVerificationToken = async () => {
    setIsRefreshing(true)
    try {
      const response = await fetch(`${window.wpNotionConfig?.apiUrl}/webhook/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.wpNotionConfig?.nonce || ''
        }
      })
      
      const data = await response.json()
      if (data.success && data.data.verification_token) {
        updateSettings({ webhook_verify_token: data.data.verification_token })
      }
    } catch (error) {
      console.error('刷新验证令牌失败:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  // 生成新的Webhook令牌
  useEffect(() => {
    if (webhookSettings.webhook_enabled && !webhookSettings.webhook_token) {
      const newToken = generateRandomToken(32)
      updateSettings({ webhook_token: newToken })
    }
  }, [webhookSettings.webhook_enabled])

  // 生成随机令牌
  const generateRandomToken = (length: number): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  return (
    <Card
      title="🪝 Webhook 支持"
      subtitle="配置Notion集成的Webhook，实现内容变更时自动触发同步"
      shadow="md"
    >
      <CardContent className="space-y-6">
        {/* Webhook启用开关 */}
        <div className="space-y-4">
          <Checkbox
            label="启用 Webhook 支持"
            checked={webhookSettings.webhook_enabled}
            onChange={(checked) => handleConfigChange('webhook_enabled', checked)}
          />
          <p className="text-sm text-gray-600">
            启用后，您可以设置 Notion 集成的 Webhook 以在内容变更时自动触发同步。
          </p>
        </div>

        {/* Webhook配置详情 */}
        {webhookSettings.webhook_enabled && (
          <div className="space-y-6 pt-4 border-t border-gray-200">
            {/* 验证令牌 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                验证令牌
              </label>
              <div className="flex space-x-2">
                <Input
                  value={webhookSettings.webhook_verify_token}
                  placeholder="等待 Notion 返回..."
                  readOnly
                  className="flex-1"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={refreshVerificationToken}
                  loading={isRefreshing}
                  disabled={isRefreshing}
                  title="刷新验证令牌"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => copyToClipboard(webhookSettings.webhook_verify_token, 'verification_token')}
                  disabled={!webhookSettings.webhook_verify_token}
                  title="复制令牌"
                >
                  {copiedField === 'verification_token' ? (
                    <CheckIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <ClipboardDocumentIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                首次发送 Webhook 时，Notion 将返回 verification_token，此处会自动展示。点击刷新按钮可获取最新的令牌。
              </p>
            </div>

            {/* Webhook URL */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Webhook 地址
              </label>
              <div className="flex space-x-2">
                <Input
                  value={webhookUrl}
                  readOnly
                  className="flex-1 font-mono text-sm"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => copyToClipboard(webhookUrl, 'webhook_url')}
                  disabled={!webhookUrl}
                  title="复制 URL"
                >
                  {copiedField === 'webhook_url' ? (
                    <CheckIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <ClipboardDocumentIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                在 Notion 开发者平台设置此 URL 作为您集成的 Webhook 终端点。
              </p>
            </div>

            {/* Webhook同步选项 */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-700">Webhook 同步选项</h4>
              
              <div className="space-y-3">
                <Checkbox
                  label="启用增量同步"
                  checked={webhookSettings.webhook_incremental_sync}
                  onChange={(checked) => handleConfigChange('webhook_incremental_sync', checked)}
                />
                <p className="text-xs text-gray-500 ml-6">
                  Webhook触发时仅同步有变化的页面，提高响应速度
                </p>

                <Checkbox
                  label="数据库事件检查删除"
                  checked={webhookSettings.webhook_check_deletions}
                  onChange={(checked) => handleConfigChange('webhook_check_deletions', checked)}
                />
                <p className="text-xs text-gray-500 ml-6">
                  数据库结构变化时检查删除的页面（单页面事件不受影响）
                </p>
              </div>
            </div>

            {/* 配置说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">💡 配置说明</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 在Notion开发者平台创建集成后，进入集成设置页面</li>
                <li>• 在"Webhooks"部分添加新的Webhook终端点</li>
                <li>• 使用上方的Webhook地址作为终端点URL</li>
                <li>• 选择要监听的事件类型（建议选择所有页面和数据库事件）</li>
                <li>• 保存后，Notion会发送验证请求，验证令牌会自动显示在上方</li>
              </ul>
            </div>
          </div>
        )}

        {/* 保存按钮 */}
        {hasUnsavedChanges && (
          <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">您有未保存的Webhook配置更改</p>
            <Button
              variant="primary"
              onClick={saveSettings}
              loading={isSaving}
              disabled={isSaving}
            >
              {isSaving ? '保存中...' : '保存Webhook设置'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}