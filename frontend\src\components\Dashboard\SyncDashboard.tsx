import React from 'react'
import { useSyncStore } from '../../stores/syncStore'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Input, Button, Select } from '../Common'
import { StatsCards } from './StatsCards'
import { SyncButtons } from './SyncButtons'
import { ProgressBar } from './ProgressBar'
import { ConnectionTest } from './ConnectionTest'
import { WebhookConfig } from './WebhookConfig'
import { QuickConfig } from './QuickConfig'
import { useI18n } from '../../hooks/useI18n'

export const SyncDashboard: React.FC = () => {
  const { isRunning, progress, status, currentStep, stats } = useSyncStore()
  const {
    settings,
    updateSettings,
    saveSettings,
    hasUnsavedChanges,
    isSaving
  } = useSettingsStore()
  const { __ } = useI18n()

  const handleApiKeyChange = (value: string) => {
    updateSettings({ api_key: value })
  }

  const handleDatabaseIdChange = (value: string) => {
    updateSettings({ database_id: value })
  }

  const handleSaveApiSettings = async () => {
    const success = await saveSettings()
    if (success) {
      console.log(__('API settings saved successfully'))
    }
  }

  // 处理自动同步设置
  const handleSyncScheduleChange = (schedule: string) => {
    updateSettings({
      sync_schedule: schedule,
      auto_sync_enabled: schedule !== 'manual'
    })
  }

  // 同步计划选项
  const syncScheduleOptions = [
    { value: 'manual', label: __('Manual Sync') },
    { value: 'hourly', label: __('Hourly') },
    { value: 'twicedaily', label: __('Twice Daily') },
    { value: 'daily', label: __('Daily') },
    { value: 'weekly', label: __('Weekly') },
    { value: 'biweekly', label: __('Biweekly') },
    { value: 'monthly', label: __('Monthly') }
  ]

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          🔄 {__('Sync Settings')}
        </h2>
        <p className="text-sm text-gray-600">
          {__('Configure Notion API and manage database synchronization')}
        </p>
      </div>

      {/* API配置表单 */}
      <Card
        title={__('API Configuration')}
        subtitle={__('Configure Notion API key and database ID')}
        shadow="md"
      >
        <CardContent className="space-y-4">
          <Input
            label={__('Notion API Key')}
            type="password"
            value={settings?.api_key || ''}
            onChange={(e) => handleApiKeyChange(e.target.value)}
            placeholder="secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            helperText={__('API key obtained after creating an integration in Notion developer page')}
          />

          <Input
            label={__('Notion Database ID')}
            value={settings?.database_id || ''}
            onChange={(e) => handleDatabaseIdChange(e.target.value)}
            placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            helperText={__('Notion database ID to sync, can be obtained from database URL')}
          />

          {/* 连接测试 */}
          <div className="pt-4 border-t border-gray-200">
            <ConnectionTest />
          </div>

          {hasUnsavedChanges && (
            <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">{__('You have unsaved API configuration changes')}</p>
              <Button
                variant="primary"
                onClick={handleSaveApiSettings}
                loading={isSaving}
                disabled={isSaving}
              >
                {isSaving ? __('Saving...') : __('Save API Settings')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 自動同步設置 */}
      <Card
        title={`⏰ ${__('Auto Sync Settings')}`}
        subtitle={__('Configure scheduled sync plan')}
        shadow="md"
      >
        <CardContent className="space-y-4">
          <Select
            label={__('Sync Schedule')}
            value={settings?.sync_schedule || 'manual'}
            onChange={(value) => handleSyncScheduleChange(value)}
            options={syncScheduleOptions}
          />
          <p className="text-sm text-gray-600">
            {__('Choose the frequency for automatic synchronization. Selecting "Manual Sync" will disable auto sync functionality.')}
          </p>

          {settings?.sync_schedule && settings.sync_schedule !== 'manual' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800">
                ✅ {__('Auto sync is enabled, will sync at')} <strong>{syncScheduleOptions.find(o => o.value === settings.sync_schedule)?.label}</strong> {__('frequency')}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 快速配置 */}
      <QuickConfig />

      {/* Webhook配置 */}
      <WebhookConfig />

      <StatsCards stats={stats || {
        imported_count: 0,
        published_count: 0,
        last_update: '',
        total_posts: 0,
        sync_errors: 0
      }} />

      {isRunning && (
        <Card title={__('Sync Progress')} shadow="md">
          <CardContent>
            <ProgressBar
              progress={progress}
              status={status}
              currentStep={currentStep}
            />
          </CardContent>
        </Card>
      )}

      {/* 同步操作 */}
      <Card
        title={`🔄 ${__('Sync Operations')}`}
        subtitle={__('Execute Notion database content synchronization')}
        shadow="md"
      >
        <CardContent className="space-y-4">
          <SyncButtons disabled={isRunning} />

          {/* 同步说明 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">{__('Sync Instructions')}</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <p><strong>{__('Smart Sync')}</strong>: {__('Only sync changed pages, faster speed, recommended for daily use')}</p>
              <p><strong>{__('Full Sync')}</strong>: {__('Sync all pages, ensure data consistency, suitable for first sync or failure recovery')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}