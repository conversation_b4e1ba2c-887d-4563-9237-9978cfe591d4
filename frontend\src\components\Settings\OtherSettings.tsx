/**
 * 其他设置组件
 * 
 * 包含插件的其他配置选项：
 * - 卸载时删除数据设置
 * - iframe 白名单域名配置
 * - 允许的图片格式设置
 * - 插件界面语言选择
 * - 最大图片大小限制
 * 
 * @since 2.0.0
 */

import React from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Input, Select, Checkbox, TextArea } from '../Common'
// 使用Unicode图标替代heroicons
const ExclamationTriangleIcon = ({ className }: { className?: string }) => (
  <span className={className}>⚠️</span>
)
const InformationCircleIcon = ({ className }: { className?: string }) => (
  <span className={className}>ℹ️</span>
)

export const OtherSettings: React.FC = () => {
  const { settings, updateSettings, saveSettings, hasUnsavedChanges, isSaving } = useSettingsStore()

  // 获取当前设置值
  const otherSettings = {
    delete_on_uninstall: settings?.delete_on_uninstall || false,
    iframe_whitelist: settings?.iframe_whitelist || 'www.youtube.com,youtu.be,player.bilibili.com,b23.tv,v.qq.com',
    allowed_image_types: settings?.allowed_image_types || 'image/jpeg,image/png,image/gif,image/webp',
    plugin_language: settings?.plugin_language || 'auto',
    max_image_size: settings?.max_image_size || 5
  }

  // 语言选项
  const languageOptions = [
    { value: 'auto', label: '自动检测（跟随站点语言）' },
    { value: 'zh_CN', label: '简体中文' },
    { value: 'en_US', label: 'English' }
  ]

  // 处理设置更改
  const handleSettingChange = (key: string, value: any) => {
    updateSettings({ [key]: value })
  }

  // 保存设置
  const handleSave = async () => {
    const success = await saveSettings()
    if (success) {
      console.log('其他设置保存成功')
    }
  }

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          ⚙️ 其他设置
        </h2>
        <p className="text-sm text-gray-600">
          配置插件的其他功能选项和安全设置
        </p>
      </div>

      {/* 安全设置 */}
      <Card
        title="🔒 安全设置"
        subtitle="配置内容安全和访问控制"
        shadow="md"
      >
        <CardContent className="space-y-6">
          {/* iframe 白名单 */}
          <div className="space-y-2">
            <TextArea
              label="iframe 白名单域名"
              value={otherSettings.iframe_whitelist}
              onChange={(e) => handleSettingChange('iframe_whitelist', e.target.value)}
              placeholder="www.youtube.com,youtu.be,player.bilibili.com,b23.tv,v.qq.com"
              rows={3}
              helperText="允许在内容中嵌入的 iframe 域名白名单，多个域名请用英文逗号分隔。输入 * 表示允许所有域名（不推荐）。"
            />
          </div>

          {/* 允许的图片格式 */}
          <div className="space-y-2">
            <TextArea
              label="允许的图片格式"
              value={otherSettings.allowed_image_types}
              onChange={(e) => handleSettingChange('allowed_image_types', e.target.value)}
              placeholder="image/jpeg,image/png,image/gif,image/webp"
              rows={2}
              helperText="允许下载和导入的图片 MIME 类型，多个类型请用英文逗号分隔。输入 * 表示允许所有格式。"
            />
          </div>

          {/* 最大图片大小 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Input
                label="最大图片大小"
                type="number"
                value={otherSettings.max_image_size.toString()}
                onChange={(e) => handleSettingChange('max_image_size', parseInt(e.target.value) || 5)}
                min="1"
                max="20"
                step="1"
                helperText="允许下载的最大图片大小（以 MB 为单位）。建议不超过 10MB。"
                suffix="MB"
              />
            </div>
          </div>

          {/* 安全提示 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-blue-800 mb-2">🛡️ 安全建议</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• iframe 白名单有助于防止恶意内容嵌入</li>
                  <li>• 限制图片格式可以防止上传危险文件</li>
                  <li>• 合理设置图片大小限制可以节省服务器存储空间</li>
                  <li>• 定期检查和更新白名单以确保安全性</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 界面设置 */}
      <Card
        title="🎨 界面设置"
        subtitle="自定义插件界面显示选项"
        shadow="md"
      >
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="插件界面语言"
              value={otherSettings.plugin_language}
              onChange={(value) => handleSettingChange('plugin_language', value)}
              options={languageOptions}
              helperText="选择插件界面显示的语言。自动检测将跟随WordPress站点语言设置。"
            />
          </div>

          {/* 语言说明 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">💬 语言支持说明</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>自动检测</strong>：根据WordPress站点语言自动选择</li>
              <li>• <strong>简体中文</strong>：完整的中文界面支持</li>
              <li>• <strong>English</strong>：英文界面支持</li>
              <li>• 更改语言后需要刷新页面生效</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 数据管理 */}
      <Card
        title="🗄️ 数据管理"
        subtitle="管理插件数据和卸载选项"
        shadow="md"
      >
        <CardContent className="space-y-6">
          {/* 卸载设置 */}
          <div className="space-y-4">
            <Checkbox
              label="卸载插件时，删除所有从Notion同步的文章和页面"
              checked={otherSettings.delete_on_uninstall}
              onChange={(checked) => handleSettingChange('delete_on_uninstall', checked)}
            />

            {/* 警告提示 */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-red-800 mb-2">⚠️ 重要警告</h4>
                  <p className="text-sm text-red-700">
                    启用此选项后，当您卸载插件时，所有通过Notion同步的内容将被永久删除。
                    此操作不可逆！请谨慎选择。
                  </p>
                </div>
              </div>
            </div>

            {/* 数据清理说明 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">📋 数据清理范围</h4>
              <p className="text-sm text-yellow-700 mb-2">
                启用卸载时删除数据后，以下内容将被清理：
              </p>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 所有同步的文章和页面</li>
                <li>• 相关的自定义字段（meta数据）</li>
                <li>• 上传的图片和媒体文件</li>
                <li>• 插件配置选项</li>
                <li>• 同步历史记录和日志</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 保存提示 */}
      {hasUnsavedChanges && (
        <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">您有未保存的其他设置更改</p>
          <Button
            variant="primary"
            onClick={handleSave}
            loading={isSaving}
            disabled={isSaving}
          >
            {isSaving ? '保存中...' : '保存其他设置'}
          </Button>
        </div>
      )}
    </div>
  )
}