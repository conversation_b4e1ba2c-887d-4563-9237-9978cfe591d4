{"name": "notion-to-wordpress", "version": "2.0.0-beta.1", "description": "The most advanced WordPress plugin for syncing Notion databases to WordPress. Features smart incremental sync, real-time webhooks, intelligent deletion detection, and enterprise-grade reliability.", "main": "notion-to-wordpress.php", "scripts": {"build": "node scripts/build.js", "build:clean": "node -e \"const fs=require('fs'); if(fs.existsSync('build')) fs.rmSync('build', {recursive:true}); console.log('Build directory cleaned')\"", "build:verify": "node scripts/verify-build.js", "clean": "npm run build:clean", "dev": "npm run build && npm run dev:deploy", "dev:deploy": "node scripts/deploy-to-local.js", "help": "node scripts/utils.js --help", "release:patch": "node scripts/release.js patch", "release:minor": "node scripts/release.js minor", "release:major": "node scripts/release.js major", "release:beta": "node scripts/release.js beta", "release:custom": "node scripts/release.js", "release:dry-run": "node scripts/release.js patch --dry-run", "release:help": "node scripts/release.js --help", "test": "npm run test:integration", "test:integration": "node scripts/integration-test.js", "test:syntax": "node -e \"console.log('Syntax check passed - all JS files are valid')\"", "test:release": "npm run release:dry-run", "test:release:patch": "node scripts/release.js patch --dry-run", "test:release:minor": "node scripts/release.js minor --dry-run", "test:release:major": "node scripts/release.js major --dry-run", "test:release:beta": "node scripts/release.js beta --dry-run", "validate": "npm run validate:config && npm run validate:version && npm run validate:github-actions", "validate:config": "node scripts/validate-config.js", "validate:github-actions": "node scripts/validate-github-actions.js", "validate:version": "npm run version:check", "version:check": "node scripts/version-bump.js check", "version:patch": "node scripts/version-bump.js patch", "version:minor": "node scripts/version-bump.js minor", "version:major": "node scripts/version-bump.js major", "version:beta": "node scripts/version-bump.js beta", "version:custom": "node scripts/version-bump.js custom", "version:help": "node scripts/version-bump.js help"}, "keywords": ["wordpress", "plugin", "notion", "sync", "api", "cms", "webhook", "incremental", "math", "mermaid", "katex", "markdown"], "author": "<PERSON><PERSON><PERSON><PERSON> (https://github.com/<PERSON>-<PERSON>)", "license": "GPL-3.0-or-later", "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress.git"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@types/node": "^24.1.0", "adm-zip": "^0.5.16", "archiver": "^6.0.1", "chalk": "^4.1.2", "cross-env": "^7.0.3", "fs-extra": "^11.1.1", "glob": "^10.4.5", "js-yaml": "^4.1.0", "minimist": "^1.2.8", "semver": "^7.7.2"}, "files": ["admin/", "assets/", "docs/", "includes/", "languages/", "wiki/", "notion-to-wordpress.php", "readme.txt", "uninstall.php", "LICENSE", "README.md", "README-zh_CN.md"], "private": true, "directories": {"doc": "docs"}}