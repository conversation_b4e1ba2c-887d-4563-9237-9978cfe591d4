/**
 * WordPress国际化Hook
 * 
 * 集成WordPress的i18n系统，提供翻译功能
 * 支持动态语言切换和文本域管理
 * 
 * @since 2.0.0-beta.1
 */

import { useCallback, useEffect, useState } from 'react'

// 翻译函数类型
export interface I18nFunctions {
  /** 基础翻译函数 */
  __: (text: string, domain?: string) => string
  /** 复数翻译函数 */
  _n: (single: string, plural: string, number: number, domain?: string) => string
  /** 带上下文的翻译函数 */
  _x: (text: string, context: string, domain?: string) => string
  /** 带上下文的复数翻译函数 */
  _nx: (single: string, plural: string, number: number, context: string, domain?: string) => string
  /** 检查是否为RTL语言 */
  isRTL: () => boolean
  /** 当前语言代码 */
  locale: string
  /** 文本域 */
  textdomain: string
}

// 默认文本域
const DEFAULT_TEXTDOMAIN = 'notion-to-wordpress'

/**
 * WordPress国际化Hook
 * 
 * @param customDomain 自定义文本域，默认使用插件文本域
 * @returns 翻译函数和语言信息
 */
export const useI18n = (customDomain?: string): I18nFunctions => {
  const [locale, setLocale] = useState<string>('en_US')
  const textdomain = customDomain || DEFAULT_TEXTDOMAIN

  // 初始化语言设置
  useEffect(() => {
    // 从WordPress配置获取当前语言
    const currentLocale = window.wpNotionConfig?.locale || 
                         document.documentElement.lang || 
                         'en_US'
    setLocale(currentLocale)

    // 监听语言变化事件
    const handleLocaleChange = (event: CustomEvent) => {
      setLocale(event.detail.locale)
    }

    window.addEventListener('notion-locale-change', handleLocaleChange as EventListener)
    
    return () => {
      window.removeEventListener('notion-locale-change', handleLocaleChange as EventListener)
    }
  }, [])

  // 基础翻译函数
  const __ = useCallback((text: string, domain?: string): string => {
    if (typeof window.wp?.i18n?.__ === 'function') {
      return window.wp.i18n.__(text, domain || textdomain)
    }
    // 降级处理：如果WordPress i18n不可用，返回原文
    return text
  }, [textdomain])

  // 复数翻译函数
  const _n = useCallback((single: string, plural: string, number: number, domain?: string): string => {
    if (typeof window.wp?.i18n?._n === 'function') {
      return window.wp.i18n._n(single, plural, number, domain || textdomain)
    }
    // 降级处理：简单的复数判断
    return number === 1 ? single : plural
  }, [textdomain])

  // 带上下文的翻译函数
  const _x = useCallback((text: string, context: string, domain?: string): string => {
    if (typeof window.wp?.i18n?._x === 'function') {
      return window.wp.i18n._x(text, context, domain || textdomain)
    }
    // 降级处理：返回原文
    return text
  }, [textdomain])

  // 带上下文的复数翻译函数
  const _nx = useCallback((single: string, plural: string, number: number, context: string, domain?: string): string => {
    if (typeof window.wp?.i18n?._nx === 'function') {
      return window.wp.i18n._nx(single, plural, number, context, domain || textdomain)
    }
    // 降级处理：简单的复数判断
    return number === 1 ? single : plural
  }, [textdomain])

  // RTL检查函数
  const isRTL = useCallback((): boolean => {
    if (typeof window.wp?.i18n?.isRTL === 'function') {
      return window.wp.i18n.isRTL()
    }
    // 降级处理：检查HTML dir属性
    return document.documentElement.dir === 'rtl'
  }, [])

  return {
    __,
    _n,
    _x,
    _nx,
    isRTL,
    locale,
    textdomain
  }
}

/**
 * 触发语言切换事件
 * 
 * @param newLocale 新的语言代码
 */
export const changeLocale = (newLocale: string): void => {
  const event = new CustomEvent('notion-locale-change', {
    detail: { locale: newLocale }
  })
  window.dispatchEvent(event)
}

/**
 * 预定义的常用翻译文本
 * 提供类型安全的翻译键值
 */
export const TRANSLATION_KEYS = {
  // 通用
  SAVE: 'Save',
  CANCEL: 'Cancel',
  DELETE: 'Delete',
  EDIT: 'Edit',
  LOADING: 'Loading...',
  SUCCESS: 'Success',
  ERROR: 'Error',
  WARNING: 'Warning',
  
  // 标签页
  SYNC_SETTINGS: 'Sync Settings',
  FIELD_MAPPING: 'Field Mapping',
  PERFORMANCE_CONFIG: 'Performance Configuration',
  PERFORMANCE_MONITOR: 'Performance Monitor',
  LOG_VIEWER: 'Log Viewer',
  OTHER_SETTINGS: 'Other Settings',
  DEBUG_TOOLS: 'Debug Tools',
  COMPONENT_SHOWCASE: 'Component Showcase',
  HELP: 'Help',
  ABOUT_AUTHOR: 'About Author',
  
  // API设置
  API_KEY: 'API Key',
  DATABASE_ID: 'Database ID',
  TEST_CONNECTION: 'Test Connection',
  CONNECTION_SUCCESS: 'Connection successful',
  CONNECTION_FAILED: 'Connection failed',
  
  // 同步操作
  AUTO_SYNC: 'Auto Sync',
  SYNC_FREQUENCY: 'Sync Frequency',
  SYNC_IN_PROGRESS: 'Sync in progress...',
  SYNC_COMPLETED: 'Sync completed',
  SYNC_FAILED: 'Sync failed',
  
  // 错误消息
  FIELD_REQUIRED: 'This field is required',
  INVALID_API_KEY: 'Invalid API key format',
  INVALID_DATABASE_ID: 'Invalid database ID format',
  NETWORK_ERROR: 'Network error occurred',
  UNKNOWN_ERROR: 'An unknown error occurred',

  // 同步相关
  SYNC_PROGRESS: 'Sync Progress',
  SYNC_OPERATIONS: 'Sync Operations',
  SYNC_INSTRUCTIONS: 'Sync Instructions',
  SMART_SYNC: 'Smart Sync',
  FULL_SYNC: 'Full Sync',

  // 设置相关
  API_CONFIGURATION: 'API Configuration',
  AUTO_SYNC_SETTINGS: 'Auto Sync Settings',
  SYNC_SCHEDULE: 'Sync Schedule',
  SAVE_API_SETTINGS: 'Save API Settings',
  SAVING: 'Saving...',

  // 频率选项
  MANUAL_SYNC: 'Manual Sync',
  HOURLY: 'Hourly',
  TWICE_DAILY: 'Twice Daily',
  DAILY: 'Daily',
  WEEKLY: 'Weekly',
  BIWEEKLY: 'Biweekly',
  MONTHLY: 'Monthly',

  // 数据库优化
  DATABASE_INDEX_OPTIMIZATION: 'Database Index Optimization',
  TOTAL_INDEXES: 'Total Indexes',
  OPTIMIZED: 'Optimized',
  NEED_OPTIMIZATION: 'Need Optimization',
  REDUNDANT: 'Redundant',
  OPTIMIZE_ALL_INDEXES: 'Optimize All Indexes',
  OPTIMIZING: 'Optimizing...',
  REFRESH_STATUS: 'Refresh Status',
  CHECKING: 'Checking...',
  REMOVE_INDEXES: 'Remove Indexes',
  REMOVING: 'Removing...',
  INDEX_DETAILS: 'Index Details',
  EXISTS: 'Exists',
  CREATED_INDEXES: 'Created indexes',
  ITEMS: 'items',
  EXPECTED_PERFORMANCE_IMPROVEMENT: 'Expected performance improvement',
  EXECUTION_TIME: 'Execution time',
  ONE_CLICK_OPTIMIZATION_GUIDE: 'One-Click Optimization Guide',
  DEDICATED_INDEXES: 'Dedicated Indexes',
  SMART_COVERAGE: 'Smart Coverage',
  ZERO_RISK_OPERATION: 'Zero Risk Operation',
  PERFORMANCE_BOOST: 'Performance Boost',
  LAST_CHECKED: 'Last checked',

  // 智能推荐
  SMART_RECOMMENDATIONS: 'Smart Recommendations',
  AI_POWERED_SMART_RECOMMENDATIONS: 'AI-Powered Smart Recommendations',
  GET_PERSONALIZED_CONFIGURATION: 'Get personalized configuration based on your environment',
  SHOW_SMART_RECOMMENDATIONS: 'Show Smart Recommendations',
  HIDE_SMART_RECOMMENDATIONS: 'Hide Smart Recommendations',
  ANALYZE_AND_RECOMMEND: 'Analyze & Recommend',
  ANALYZING: 'Analyzing...',
  ENVIRONMENT_ANALYSIS: 'Environment Analysis',
  PERFORMANCE: 'Performance',
  MEMORY: 'Memory',
  SERVER_TYPE: 'Server Type',
  RISK_LEVEL: 'Risk Level',
  CONFIGURATION_CONFLICTS: 'Configuration Conflicts',
  AUTO_FIXABLE: 'Auto-fixable',
  AUTO_FIX_CONFLICTS: 'Auto Fix Conflicts',
  RECOMMENDED_CONFIGURATION: 'Recommended Configuration',
  CONFIDENCE: 'Confidence',
  PERFORMANCE_LEVEL: 'Performance Level',
  API_PAGE_SIZE: 'API Page Size',
  CONCURRENT_REQUESTS: 'Concurrent Requests',
  BATCH_SIZE: 'Batch Size',
  SYNC_SCHEDULE: 'Sync Schedule',
  AUTO_SYNC: 'Auto Sync',
  ENABLED: 'Enabled',
  DISABLED: 'Disabled',
  EXPECTED_IMPROVEMENT: 'Expected Improvement',
  APPLY_RECOMMENDATIONS: 'Apply Recommendations',
  VIEW_HISTORY: 'View History',
  RECOMMENDATION_HISTORY: 'Recommendation History',
  APPLIED: 'Applied',
  SUGGESTED: 'Suggested',

  // 快速配置
  QUICK_CONFIGURATION: 'Quick Configuration',
  USE_PRESET_TEMPLATES: 'Use preset templates to quickly configure the plugin, suitable for most users',
  FIELD_MAPPING_TEMPLATE: 'Field Mapping Template',
  CHOOSE_PERFORMANCE_LEVEL: 'Choose the performance level suitable for your server configuration. The system will automatically set optimal API page size, baseline concurrency, and other parameters, and dynamically adjust based on real-time performance.',
  CHOOSE_FIELD_MAPPING_TEMPLATE: 'Choose a field mapping template that matches your Notion database language. Select "Custom" to configure detailed settings in the Field Mapping tab.',
  YOU_HAVE_UNSAVED_QUICK_CONFIGURATION_CHANGES: 'You have unsaved quick configuration changes',

  // 性能监控
  PERFORMANCE_MONITOR: 'Performance Monitor',
  REAL_TIME_MONITORING: 'Real-time monitoring of system performance and sync status',
  LAST_UPDATED: 'Last updated',
  REFRESHING: 'Refreshing...',
  REFRESH: 'Refresh',
  AUTO_REFRESH_ON: 'Auto Refresh On',
  AUTO_REFRESH_OFF: 'Auto Refresh Off',
  SYSTEM_INFORMATION: 'System Information',
  CURRENT_SYSTEM_ENVIRONMENT: 'Current system environment and resource status',
  PHP_VERSION: 'PHP Version',
  MEMORY_LIMIT: 'Memory Limit',
  CURRENT_MEMORY: 'Current Memory',
  PEAK_MEMORY: 'Peak Memory',
  SSE_CONNECTION: 'SSE Connection',
  CONNECTED: 'Connected',
  DISCONNECTED: 'Disconnected',
  WORDPRESS_VERSION: 'WordPress Version',
  CURRENT_CONFIGURATION: 'Current Configuration',
  VIEW_CURRENT_PERFORMANCE_CONFIGURATION: 'View current performance configuration parameters',
  API_PAGE_SIZE: 'API Page Size',
  CONCURRENT_REQUESTS: 'Concurrent Requests',
  BATCH_SIZE: 'Batch Size',
  LOG_BUFFER_SIZE: 'Log Buffer Size',
  PERFORMANCE_MODE: 'Performance Mode',
  ENABLED: 'Enabled',
  DISABLED: 'Disabled',
  PERFORMANCE_LEVEL: 'Performance Level',
  NOT_SET: 'Not Set',
  ASYNC_PROCESSING_STATUS: 'Async Processing Status',
  REAL_TIME_MONITORING_BACKGROUND_TASKS: 'Real-time monitoring of background tasks and queue status',
  ASYNC_ENGINE_STATUS: 'Async Engine Status',
  STATUS: 'Status',
  CURRENT_OPERATION: 'Current Operation',
  PROGRESS: 'Progress',
  QUEUE_STATUS: 'Queue Status',
  PENDING_TASKS: 'Pending Tasks',
  PROCESSING_TASKS: 'Processing Tasks',
  COMPLETED_TASKS: 'Completed Tasks',
  FAILED_TASKS: 'Failed Tasks',
  PERFORMANCE_METRICS: 'Performance Metrics',
  VIEW_DETAILED_PERFORMANCE_STATISTICS: 'View detailed performance statistics',
  PERFORMANCE_STATISTICS: 'Performance Statistics',
  HIDE_ADVANCED: 'Hide Advanced',
  SHOW_ADVANCED: 'Show Advanced',
  RESET_STATS: 'Reset Stats',
  API_RESPONSE_TIME: 'API Response Time',
  API_CALLS_COUNT: 'API Calls Count',
  API_MEMORY_USAGE: 'API Memory Usage',
  EXECUTION_TIME: 'Execution Time',
  UNABLE_TO_RETRIEVE_PERFORMANCE_METRICS: 'Unable to retrieve performance metrics',
  RETRY: 'Retry',
  PERFORMANCE_RECOMMENDATIONS: 'Performance Recommendations',
  AI_GENERATED_SUGGESTIONS: 'AI-generated suggestions to improve system performance',
  MAINTENANCE_TOOLS: 'Maintenance Tools',
  SYSTEM_MAINTENANCE_AND_OPTIMIZATION: 'System maintenance and optimization tools',
  CLEAR_CACHE: 'Clear Cache',
  OPTIMIZE_DATABASE: 'Optimize Database',
  RESET_PERFORMANCE_STATS: 'Reset Performance Stats',
  MAINTENANCE_TIPS: 'Maintenance Tips',
  CLEARING_CACHE_MAY_AFFECT_PERFORMANCE: 'Clearing cache may temporarily affect performance, recommended during off-peak hours',
  DATABASE_OPTIMIZATION_MAY_TAKE_TIME: 'Database optimization may take a long time, please be patient',
  REGULAR_MAINTENANCE_RECOMMENDED: 'Regular maintenance operations are recommended to maintain optimal performance',
  PERFORMANCE_STATS_RESET_WARNING: 'Performance statistics reset will clear all historical data',

  // 系统状态消息
  SYSTEM_INFORMATION_UPDATED: 'System information updated',
  SYSTEM_INFORMATION_REFRESHED: 'System information has been successfully refreshed',
  FAILED_TO_FETCH_SYSTEM_INFORMATION: 'Failed to fetch system information',
  CHECK_NETWORK_CONNECTION: 'Please check your network connection and try again',
  CACHE_CLEARED: 'Cache cleared',
  SYSTEM_CACHE_CLEARED: 'System cache has been successfully cleared',
  FAILED_TO_CLEAR_CACHE: 'Failed to clear cache',
  DATABASE_OPTIMIZATION_COMPLETED: 'Database optimization completed',
  DATABASE_OPTIMIZED: 'Database has been successfully optimized',
  DATABASE_OPTIMIZATION_FAILED: 'Database optimization failed',
  PERFORMANCE_STATISTICS_RESET: 'Performance statistics reset',
  PERFORMANCE_STATISTICS_RESET_SUCCESS: 'Performance statistics have been successfully reset',
  FAILED_TO_RESET_PERFORMANCE_STATISTICS: 'Failed to reset performance statistics',
  PLEASE_TRY_AGAIN_LATER: 'Please try again later',

  // 响应式设计
  TOGGLE_MENU: 'Toggle menu',
  MENU: 'Menu',
  CLOSE_MENU: 'Close menu',
  EXPAND_SIDEBAR: 'Expand sidebar',
  COLLAPSE_SIDEBAR: 'Collapse sidebar',
  LOADING: 'Loading...',
  NO_DATA_AVAILABLE: 'No data available',
  COLLAPSE: 'Collapse',
  EXPAND: 'Expand',
  PREVIOUS: 'Previous',
  NEXT: 'Next',
  SHOWING_ENTRIES: 'Showing {{start}} to {{end}} of {{total}} entries'
} as const

/**
 * 类型安全的翻译Hook
 * 使用预定义的翻译键值
 */
export const useTranslation = () => {
  const { __ } = useI18n()

  return useCallback((key: keyof typeof TRANSLATION_KEYS): string => {
    return __(TRANSLATION_KEYS[key])
  }, [__])
}

/**
 * 获取标签页翻译的辅助函数
 */
export const getTabTranslation = (labelKey: string, __: (text: string) => string): string => {
  const translations: Record<string, string> = {
    'SYNC_SETTINGS': __('Sync Settings'),
    'FIELD_MAPPING': __('Field Mapping'),
    'PERFORMANCE_CONFIG': __('Performance Configuration'),
    'PERFORMANCE_MONITOR': __('Performance Monitor'),
    'LOG_VIEWER': __('Log Viewer'),
    'OTHER_SETTINGS': __('Other Settings'),
    'DEBUG_TOOLS': __('Debug Tools'),
    'COMPONENT_SHOWCASE': __('Component Showcase'),
    'HELP': __('Help'),
    'ABOUT_AUTHOR': __('About Author')
  }

  return translations[labelKey] || labelKey
}
