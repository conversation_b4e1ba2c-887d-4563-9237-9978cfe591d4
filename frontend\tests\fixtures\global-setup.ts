/**
 * Playwright全局设置
 * 
 * 在所有测试运行前执行的设置
 * 
 * @since 2.0.0
 */

import { chromium, FullConfig } from '@playwright/test'
import path from 'path'
import fs from 'fs'

async function globalSetup(config: FullConfig) {
  console.log('🚀 开始E2E测试全局设置...')
  
  const baseURL = config.projects[0].use.baseURL || 'http://frankloong.local'
  const browser = await chromium.launch()
  const context = await browser.newContext()
  const page = await context.newPage()

  try {
    // 1. 检查WordPress站点是否可访问
    console.log('📡 检查WordPress站点连接...')
    await page.goto(baseURL, { waitUntil: 'networkidle' })
    
    // 2. 登录WordPress管理后台
    console.log('🔐 登录WordPress管理后台...')
    await page.goto(`${baseURL}/wp-admin`)
    
    // 检查是否已经登录
    const isLoggedIn = await page.locator('#wpadminbar').isVisible().catch(() => false)
    
    if (!isLoggedIn) {
      // 执行登录
      await page.fill('#user_login', 'frankloong')
      await page.fill('#user_pass', 'KL.123.kl')
      await page.click('#wp-submit')
      
      // 等待登录完成
      await page.waitForSelector('#wpadminbar', { timeout: 10000 })
      console.log('✅ WordPress登录成功')
    } else {
      console.log('✅ WordPress已登录')
    }
    
    // 3. 访问插件页面确保插件已激活
    console.log('🔌 检查插件状态...')
    await page.goto(`${baseURL}/wp-admin/admin.php?page=notion-to-wordpress`)
    
    // 等待React应用加载
    await page.waitForSelector('.notion-wp-admin', { timeout: 15000 })
    console.log('✅ 插件页面加载成功')
    
    // 4. 保存认证状态
    const authFile = path.join(__dirname, 'admin-auth.json')
    await context.storageState({ path: authFile })
    console.log('💾 认证状态已保存')
    
    // 5. 创建测试数据目录
    const testDataDir = path.join(__dirname, '../test-data')
    if (!fs.existsSync(testDataDir)) {
      fs.mkdirSync(testDataDir, { recursive: true })
    }
    
    // 6. 准备测试数据
    await prepareTestData(page, baseURL)
    
    console.log('🎉 全局设置完成')
    
  } catch (error) {
    console.error('❌ 全局设置失败:', error)
    throw error
  } finally {
    await browser.close()
  }
}

/**
 * 准备测试数据
 */
async function prepareTestData(page: any, baseURL: string) {
  console.log('📋 准备测试数据...')
  
  try {
    // 检查当前设置状态
    await page.goto(`${baseURL}/wp-admin/admin.php?page=notion-to-wordpress`)
    await page.waitForSelector('.notion-wp-admin', { timeout: 10000 })
    
    // 获取当前API设置状态
    const hasApiKey = await page.locator('[data-testid="api-key-input"]').inputValue().catch(() => '')
    const hasDatabaseId = await page.locator('[data-testid="database-id-input"]').inputValue().catch(() => '')
    
    // 保存当前状态到测试数据
    const testData = {
      timestamp: new Date().toISOString(),
      baseURL,
      currentSettings: {
        hasApiKey: !!hasApiKey,
        hasDatabaseId: !!hasDatabaseId,
        apiKeyLength: hasApiKey.length,
        databaseIdLength: hasDatabaseId.length
      },
      testCredentials: {
        // 测试用的假数据
        apiKey: 'test_api_key_' + Date.now(),
        databaseId: 'test_database_id_' + Date.now(),
        invalidApiKey: 'invalid_key_123',
        invalidDatabaseId: 'invalid_db_456'
      }
    }
    
    // 保存测试数据
    const testDataFile = path.join(__dirname, '../test-data/setup-data.json')
    fs.writeFileSync(testDataFile, JSON.stringify(testData, null, 2))
    
    console.log('✅ 测试数据准备完成')
    
  } catch (error) {
    console.warn('⚠️ 测试数据准备失败，将使用默认数据:', error.message)
    
    // 创建默认测试数据
    const defaultTestData = {
      timestamp: new Date().toISOString(),
      baseURL,
      currentSettings: {
        hasApiKey: false,
        hasDatabaseId: false,
        apiKeyLength: 0,
        databaseIdLength: 0
      },
      testCredentials: {
        apiKey: 'test_api_key_default',
        databaseId: 'test_database_id_default',
        invalidApiKey: 'invalid_key_123',
        invalidDatabaseId: 'invalid_db_456'
      }
    }
    
    const testDataFile = path.join(__dirname, '../test-data/setup-data.json')
    fs.writeFileSync(testDataFile, JSON.stringify(defaultTestData, null, 2))
  }
}

export default globalSetup
