/**
 * 错误处理配置
 * 
 * 统一管理错误处理的配置参数
 * 
 * @since 2.0.0
 */

import { ErrorType, ErrorSeverity, RecoveryStrategy } from '../utils/errorHandler'

/**
 * 错误类型配置映射
 */
export const ERROR_TYPE_CONFIG = {
  [ErrorType.NETWORK_ERROR]: {
    severity: ErrorSeverity.MEDIUM,
    recoveryStrategy: RecoveryStrategy.RETRY,
    maxRetries: 3,
    retryDelay: 1000,
    userMessage: '网络连接出现问题，请检查网络设置后重试',
    enableAutoRetry: true,
    reportToServer: false
  },
  
  [ErrorType.TIMEOUT_ERROR]: {
    severity: ErrorSeverity.MEDIUM,
    recoveryStrategy: RecoveryStrategy.RETRY,
    maxRetries: 2,
    retryDelay: 2000,
    userMessage: '请求超时，请稍后重试',
    enableAutoRetry: true,
    reportToServer: false
  },
  
  [ErrorType.CONNECTION_ERROR]: {
    severity: ErrorSeverity.HIGH,
    recoveryStrategy: RecoveryStrategy.FALLBACK,
    maxRetries: 1,
    retryDelay: 5000,
    userMessage: '连接失败，请检查网络连接',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.API_ERROR]: {
    severity: ErrorSeverity.HIGH,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 1,
    retryDelay: 3000,
    userMessage: 'API请求失败，请稍后重试',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.AUTHENTICATION_ERROR]: {
    severity: ErrorSeverity.CRITICAL,
    recoveryStrategy: RecoveryStrategy.REDIRECT,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '身份验证失败，请重新登录',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.AUTHORIZATION_ERROR]: {
    severity: ErrorSeverity.CRITICAL,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '权限不足，请联系管理员',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.VALIDATION_ERROR]: {
    severity: ErrorSeverity.LOW,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '输入数据有误，请检查后重新提交',
    enableAutoRetry: false,
    reportToServer: false
  },
  
  [ErrorType.SYNC_ERROR]: {
    severity: ErrorSeverity.HIGH,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 1,
    retryDelay: 10000,
    userMessage: '同步过程中出现错误，请检查API配置',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.CONFIGURATION_ERROR]: {
    severity: ErrorSeverity.HIGH,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '配置错误，请检查设置',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.DATA_ERROR]: {
    severity: ErrorSeverity.MEDIUM,
    recoveryStrategy: RecoveryStrategy.FALLBACK,
    maxRetries: 1,
    retryDelay: 2000,
    userMessage: '数据处理出现错误，正在尝试恢复',
    enableAutoRetry: true,
    reportToServer: true
  },
  
  [ErrorType.SYSTEM_ERROR]: {
    severity: ErrorSeverity.CRITICAL,
    recoveryStrategy: RecoveryStrategy.REFRESH,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '系统错误，请刷新页面或联系技术支持',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.MEMORY_ERROR]: {
    severity: ErrorSeverity.CRITICAL,
    recoveryStrategy: RecoveryStrategy.REFRESH,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '内存不足，请关闭其他应用程序或刷新页面',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.PERFORMANCE_ERROR]: {
    severity: ErrorSeverity.MEDIUM,
    recoveryStrategy: RecoveryStrategy.FALLBACK,
    maxRetries: 1,
    retryDelay: 1000,
    userMessage: '性能问题，正在优化处理',
    enableAutoRetry: true,
    reportToServer: false
  },
  
  [ErrorType.USER_INPUT_ERROR]: {
    severity: ErrorSeverity.LOW,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '输入有误，请检查并重新输入',
    enableAutoRetry: false,
    reportToServer: false
  },
  
  [ErrorType.PERMISSION_ERROR]: {
    severity: ErrorSeverity.HIGH,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 0,
    retryDelay: 0,
    userMessage: '权限不足，请联系管理员获取相应权限',
    enableAutoRetry: false,
    reportToServer: true
  },
  
  [ErrorType.UNKNOWN_ERROR]: {
    severity: ErrorSeverity.MEDIUM,
    recoveryStrategy: RecoveryStrategy.MANUAL,
    maxRetries: 1,
    retryDelay: 3000,
    userMessage: '发生未知错误，请稍后重试',
    enableAutoRetry: false,
    reportToServer: true
  }
}

/**
 * 环境特定配置
 */
export const ENVIRONMENT_CONFIG = {
  development: {
    enableLogging: true,
    enableReporting: false,
    enableUserNotification: true,
    showErrorDetails: true,
    logLevel: 'debug'
  },
  
  production: {
    enableLogging: true,
    enableReporting: true,
    enableUserNotification: true,
    showErrorDetails: false,
    logLevel: 'error'
  },
  
  test: {
    enableLogging: false,
    enableReporting: false,
    enableUserNotification: false,
    showErrorDetails: true,
    logLevel: 'warn'
  }
}

/**
 * 错误报告配置
 */
export const ERROR_REPORTING_CONFIG = {
  endpoint: '/wp-admin/admin-ajax.php',
  action: 'notion_to_wordpress_report_error',
  batchSize: 10,
  batchInterval: 30000, // 30秒
  enableBatching: true,
  includeUserAgent: true,
  includeUrl: true,
  includeTimestamp: true,
  maxReportSize: 10240, // 10KB
  sensitiveFields: ['password', 'token', 'key', 'secret']
}

/**
 * 错误恢复配置
 */
export const ERROR_RECOVERY_CONFIG = {
  globalMaxRetries: 5,
  globalRetryDelay: 1000,
  exponentialBackoff: true,
  maxBackoffDelay: 30000,
  enableCircuitBreaker: true,
  circuitBreakerThreshold: 5,
  circuitBreakerTimeout: 60000
}

/**
 * 用户通知配置
 */
export const NOTIFICATION_CONFIG = {
  [ErrorSeverity.CRITICAL]: {
    type: 'error',
    duration: 0, // 不自动关闭
    showActions: true,
    sound: true
  },
  
  [ErrorSeverity.HIGH]: {
    type: 'error',
    duration: 10000,
    showActions: true,
    sound: false
  },
  
  [ErrorSeverity.MEDIUM]: {
    type: 'warning',
    duration: 5000,
    showActions: false,
    sound: false
  },
  
  [ErrorSeverity.LOW]: {
    type: 'info',
    duration: 3000,
    showActions: false,
    sound: false
  }
}

/**
 * 获取当前环境配置
 */
export function getCurrentEnvironmentConfig() {
  const env = process.env.NODE_ENV || 'development'
  return ENVIRONMENT_CONFIG[env as keyof typeof ENVIRONMENT_CONFIG] || ENVIRONMENT_CONFIG.development
}

/**
 * 获取错误类型配置
 */
export function getErrorTypeConfig(errorType: ErrorType) {
  return ERROR_TYPE_CONFIG[errorType] || ERROR_TYPE_CONFIG[ErrorType.UNKNOWN_ERROR]
}

/**
 * 获取通知配置
 */
export function getNotificationConfig(severity: ErrorSeverity) {
  return NOTIFICATION_CONFIG[severity] || NOTIFICATION_CONFIG[ErrorSeverity.LOW]
}

/**
 * 检查是否应该报告错误
 */
export function shouldReportError(errorType: ErrorType, severity: ErrorSeverity): boolean {
  const envConfig = getCurrentEnvironmentConfig()
  const typeConfig = getErrorTypeConfig(errorType)
  
  return envConfig.enableReporting && typeConfig.reportToServer
}

/**
 * 检查是否应该自动重试
 */
export function shouldAutoRetry(errorType: ErrorType): boolean {
  const typeConfig = getErrorTypeConfig(errorType)
  return typeConfig.enableAutoRetry && typeConfig.maxRetries > 0
}

/**
 * 获取重试延迟时间
 */
export function getRetryDelay(errorType: ErrorType, attemptCount: number): number {
  const typeConfig = getErrorTypeConfig(errorType)
  const baseDelay = typeConfig.retryDelay
  
  if (ERROR_RECOVERY_CONFIG.exponentialBackoff) {
    const delay = baseDelay * Math.pow(2, attemptCount - 1)
    return Math.min(delay, ERROR_RECOVERY_CONFIG.maxBackoffDelay)
  }
  
  return baseDelay
}
