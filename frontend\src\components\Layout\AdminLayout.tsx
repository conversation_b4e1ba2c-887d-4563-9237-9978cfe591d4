import React, { useState, useEffect } from 'react'
import { Sidebar } from './Sidebar'
import { TabContent } from './TabContent'
import { NotificationContainer } from '../Common/Notification'
import { useUIStore } from '../../stores/uiStore'
import { initializeStateBridge, cleanupStateBridge } from '../../utils/StateBridge'
import { useI18n, getTabTranslation } from '../../hooks/useI18n'
import { useResponsive, useResponsiveClasses, useMobileOptimization } from '../../hooks/useResponsive'

export interface TabType {
  id: string
  label: string
  icon: string
}

// 标签页配置将在组件内部使用翻译函数动态生成
const TAB_CONFIGS = [
  { id: 'api-settings', labelKey: 'SYNC_SETTINGS', icon: '🔄' },
  { id: 'field-mapping', labelKey: 'FIELD_MAPPING', icon: '🔗' },
  { id: 'performance-config', labelKey: 'PERFORMANCE_CONFIG', icon: '⚡' },
  { id: 'performance', labelKey: 'PERFORMANCE_MONITOR', icon: '📊' },
  { id: 'logs', labelKey: 'LOG_VIEWER', icon: '📋' },
  { id: 'other-settings', labelKey: 'OTHER_SETTINGS', icon: '⚙️' },
  { id: 'debug', labelKey: 'DEBUG_TOOLS', icon: '🐞' },
  { id: 'components', labelKey: 'COMPONENT_SHOWCASE', icon: '🎨' },
  { id: 'help', labelKey: 'HELP', icon: '📖' },
  { id: 'about-author', labelKey: 'ABOUT_AUTHOR', icon: '👨‍💻' },
] as const

export const AdminLayout: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('api-settings')
  const { setActiveTab: setUIActiveTab } = useUIStore()
  const { __ } = useI18n()
  const { isMobile, isTablet, isDesktop, deviceType } = useResponsive()
  const { allClasses } = useResponsiveClasses()

  // 移动端优化
  useMobileOptimization()

  // 动态生成带翻译的标签页配置
  const TABS: TabType[] = TAB_CONFIGS.map(config => ({
    id: config.id,
    label: `${config.icon} ${getTabTranslation(config.labelKey, __)}`,
    icon: config.icon
  }))

  // 初始化状态桥接器
  useEffect(() => {
    initializeStateBridge({
      enableDebug: process.env.NODE_ENV === 'development',
      syncDelay: 100,
    })

    console.log('🌉 [AdminLayout] 状态桥接器已初始化')

    return () => {
      cleanupStateBridge()
      console.log('🧹 [AdminLayout] 状态桥接器已清理')
    }
  }, [])

  // 同步标签状态到UI Store
  useEffect(() => {
    const tabMap: Record<string, any> = {
      'api-settings': 'settings',
      'sync': 'sync',
      'field-mapping': 'field-mapping',
      'performance': 'performance',
      'monitoring': 'monitoring',
      'debug': 'debug',
      'help': 'help',
      'about': 'about',
    }

    const uiTab = tabMap[activeTab] || 'settings'
    setUIActiveTab(uiTab)
  }, [activeTab, setUIActiveTab])

  // 确保响应式类正确设置
  const responsiveClasses = `notion-wp-${deviceType}`
  const finalClasses = `notion-wp-app ${allClasses} ${responsiveClasses}`

  return (
    <div className={finalClasses} data-device={deviceType}>
      {/* 桌面端和平板端头部 */}
      {!isMobile && (
        <div className="notion-wp-header">
          <div className="notion-wp-header-content">
            <h1 className="wp-heading-inline">
              <span className="notion-wp-logo"></span>
              Notion to WordPress
            </h1>
            <div className="notion-wp-version">
              {window.wpNotionConfig?.version || '2.0.0-beta.1'}
            </div>
          </div>
        </div>
      )}

      <div className={`notion-wp-layout ${isMobile ? 'mobile-layout' : ''} ${isTablet ? 'tablet-layout' : ''}`}>
        <Sidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={TABS}
        />
        <div className={`notion-wp-content ${isMobile ? 'mobile-content' : ''}`}>
          <TabContent activeTab={activeTab} />
        </div>
      </div>

      {/* 通知系统 */}
      <NotificationContainer />
    </div>
  )
}