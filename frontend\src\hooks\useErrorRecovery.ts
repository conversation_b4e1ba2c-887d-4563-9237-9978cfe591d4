/**
 * 错误恢复Hook
 * 
 * 提供错误恢复、重试、降级等功能
 * 
 * @since 2.0.0
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { errorHandler, ErrorType, RecoveryStrategy } from '../utils/errorHandler'

export interface ErrorRecoveryOptions {
  maxRetries?: number
  retryDelay?: number
  enableAutoRetry?: boolean
  fallbackValue?: any
  onError?: (error: Error) => void
  onRecovery?: () => void
}

export interface ErrorRecoveryState {
  error: Error | null
  isRetrying: boolean
  retryCount: number
  hasRecovered: boolean
  lastAttempt: Date | null
}

/**
 * 错误恢复Hook
 */
export function useErrorRecovery(options: ErrorRecoveryOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    enableAutoRetry = false,
    fallbackValue = null,
    onError,
    onRecovery
  } = options

  const [state, setState] = useState<ErrorRecoveryState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
    hasRecovered: false,
    lastAttempt: null
  })

  const retryTimeoutRef = useRef<NodeJS.Timeout>()
  const operationRef = useRef<(() => Promise<any>) | null>(null)

  /**
   * 执行带错误恢复的异步操作
   */
  const executeWithRecovery = useCallback(async <T>(
    operation: () => Promise<T>,
    recoveryOptions?: Partial<ErrorRecoveryOptions>
  ): Promise<T | null> => {
    const opts = { ...options, ...recoveryOptions }
    operationRef.current = operation

    try {
      setState(prev => ({ ...prev, isRetrying: true, lastAttempt: new Date() }))
      
      const result = await operation()
      
      // 操作成功，重置状态
      setState({
        error: null,
        isRetrying: false,
        retryCount: 0,
        hasRecovered: state.error !== null,
        lastAttempt: new Date()
      })

      if (state.error && onRecovery) {
        onRecovery()
      }

      return result
    } catch (error) {
      const appError = error as Error
      
      setState(prev => ({
        ...prev,
        error: appError,
        isRetrying: false,
        lastAttempt: new Date()
      }))

      // 处理错误
      await errorHandler.handleError(appError, {
        retryCount: state.retryCount,
        maxRetries: opts.maxRetries,
        operation: operation.name || 'anonymous'
      })

      if (onError) {
        onError(appError)
      }

      // 自动重试
      if (opts.enableAutoRetry && state.retryCount < (opts.maxRetries || maxRetries)) {
        await scheduleRetry(operation, opts)
        return null
      }

      // 返回降级值
      return opts.fallbackValue || fallbackValue
    }
  }, [state, options, onError, onRecovery, maxRetries])

  /**
   * 手动重试
   */
  const retry = useCallback(async () => {
    if (!operationRef.current || state.retryCount >= maxRetries) {
      return null
    }

    setState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      isRetrying: true
    }))

    return executeWithRecovery(operationRef.current)
  }, [state.retryCount, maxRetries, executeWithRecovery])

  /**
   * 计划重试
   */
  const scheduleRetry = useCallback(async (
    operation: () => Promise<any>,
    opts: ErrorRecoveryOptions
  ) => {
    if (state.retryCount >= (opts.maxRetries || maxRetries)) {
      return
    }

    const delay = (opts.retryDelay || retryDelay) * Math.pow(2, state.retryCount) // 指数退避

    setState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1
    }))

    retryTimeoutRef.current = setTimeout(async () => {
      await executeWithRecovery(operation, opts)
    }, delay)
  }, [state.retryCount, maxRetries, retryDelay, executeWithRecovery])

  /**
   * 重置错误状态
   */
  const reset = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
    }

    setState({
      error: null,
      isRetrying: false,
      retryCount: 0,
      hasRecovered: false,
      lastAttempt: null
    })

    operationRef.current = null
  }, [])

  /**
   * 检查是否可以重试
   */
  const canRetry = state.error !== null && state.retryCount < maxRetries && !state.isRetrying

  /**
   * 获取下次重试时间
   */
  const getNextRetryDelay = () => {
    if (!canRetry) return 0
    return retryDelay * Math.pow(2, state.retryCount)
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  return {
    ...state,
    executeWithRecovery,
    retry,
    reset,
    canRetry,
    nextRetryDelay: getNextRetryDelay()
  }
}

/**
 * 网络请求错误恢复Hook
 */
export function useNetworkErrorRecovery() {
  return useErrorRecovery({
    maxRetries: 3,
    retryDelay: 1000,
    enableAutoRetry: true,
    onError: (error) => {
      console.warn('网络请求失败:', error.message)
    }
  })
}

/**
 * API错误恢复Hook
 */
export function useApiErrorRecovery() {
  return useErrorRecovery({
    maxRetries: 2,
    retryDelay: 2000,
    enableAutoRetry: false,
    onError: (error) => {
      console.error('API请求失败:', error.message)
    }
  })
}

/**
 * 同步操作错误恢复Hook
 */
export function useSyncErrorRecovery() {
  return useErrorRecovery({
    maxRetries: 1,
    retryDelay: 5000,
    enableAutoRetry: false,
    onError: (error) => {
      console.error('同步操作失败:', error.message)
    },
    onRecovery: () => {
      console.info('同步操作已恢复')
    }
  })
}

/**
 * 组件加载错误恢复Hook
 */
export function useComponentErrorRecovery() {
  const [fallbackComponent, setFallbackComponent] = useState<React.ComponentType | null>(null)

  const recovery = useErrorRecovery({
    maxRetries: 2,
    retryDelay: 1000,
    enableAutoRetry: true,
    onError: (error) => {
      console.warn('组件加载失败:', error.message)
    }
  })

  const loadComponentWithRecovery = useCallback(async (
    loader: () => Promise<{ default: React.ComponentType }>
  ) => {
    const result = await recovery.executeWithRecovery(async () => {
      const module = await loader()
      return module.default
    })

    if (result) {
      setFallbackComponent(() => result)
    }

    return result
  }, [recovery])

  return {
    ...recovery,
    loadComponentWithRecovery,
    fallbackComponent
  }
}

/**
 * 批量操作错误恢复Hook
 */
export function useBatchErrorRecovery<T>() {
  const [results, setResults] = useState<Array<{ success: boolean; data?: T; error?: Error }>>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const processBatch = useCallback(async (
    items: any[],
    processor: (item: any, index: number) => Promise<T>,
    options: {
      continueOnError?: boolean
      maxConcurrent?: number
    } = {}
  ) => {
    const { continueOnError = true, maxConcurrent = 3 } = options
    
    setIsProcessing(true)
    setResults([])

    const batchResults: Array<{ success: boolean; data?: T; error?: Error }> = []

    // 分批处理
    for (let i = 0; i < items.length; i += maxConcurrent) {
      const batch = items.slice(i, i + maxConcurrent)
      
      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = i + batchIndex
        
        try {
          const data = await processor(item, globalIndex)
          return { success: true, data }
        } catch (error) {
          const appError = error as Error
          
          await errorHandler.handleError(appError, {
            batchIndex: globalIndex,
            item,
            operation: 'batch_processing'
          })

          if (!continueOnError) {
            throw error
          }

          return { success: false, error: appError }
        }
      })

      try {
        const batchResults_chunk = await Promise.all(batchPromises)
        batchResults.push(...batchResults_chunk)
        setResults([...batchResults])
      } catch (error) {
        if (!continueOnError) {
          setIsProcessing(false)
          throw error
        }
      }
    }

    setIsProcessing(false)
    return batchResults
  }, [])

  const getSuccessCount = () => results.filter(r => r.success).length
  const getErrorCount = () => results.filter(r => !r.success).length
  const getSuccessRate = () => results.length > 0 ? getSuccessCount() / results.length : 0

  return {
    processBatch,
    results,
    isProcessing,
    successCount: getSuccessCount(),
    errorCount: getErrorCount(),
    successRate: getSuccessRate()
  }
}
