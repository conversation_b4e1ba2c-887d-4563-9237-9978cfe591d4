/**
 * 表单输入组件集合
 * 
 * 提供各种类型的表单输入控件
 * 
 * @since 2.0.0
 */

import React, { forwardRef } from 'react'
import { clsx } from 'clsx'

/**
 * 基础输入框属性
 */
export interface BaseInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** 输入框大小 */
  size?: 'sm' | 'md' | 'lg'
  /** 是否全宽 */
  fullWidth?: boolean
  /** 左侧图标 */
  leftIcon?: React.ReactNode
  /** 右侧图标 */
  rightIcon?: React.ReactNode
}

/**
 * 文本输入框
 */
export const TextInput = forwardRef<HTMLInputElement, BaseInputProps>(
  ({ className, size = 'md', fullWidth = true, leftIcon, rightIcon, ...props }, ref) => {
    const sizeClasses = {
      sm: 'px-2 py-1 text-sm',
      md: 'px-3 py-2 text-sm',
      lg: 'px-4 py-3 text-base',
    }

    return (
      <div className={clsx('relative', { 'w-full': fullWidth })}>
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-gray-400">{leftIcon}</div>
          </div>
        )}
        
        <input
          ref={ref}
          type="text"
          className={clsx(
            'block border border-gray-300 rounded-md shadow-sm',
            'focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
            'disabled:bg-gray-50 disabled:text-gray-500',
            sizeClasses[size],
            {
              'w-full': fullWidth,
              'pl-10': leftIcon,
              'pr-10': rightIcon,
            },
            className
          )}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="text-gray-400">{rightIcon}</div>
          </div>
        )}
      </div>
    )
  }
)

TextInput.displayName = 'TextInput'

/**
 * 密码输入框
 */
export const PasswordInput = forwardRef<HTMLInputElement, BaseInputProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)

    const togglePassword = () => setShowPassword(!showPassword)

    return (
      <TextInput
        ref={ref}
        type={showPassword ? 'text' : 'password'}
        className={className}
        rightIcon={
          <button
            type="button"
            onClick={togglePassword}
            className="text-gray-400 hover:text-gray-600 focus:outline-none pointer-events-auto"
            tabIndex={-1}
          >
            {showPassword ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </button>
        }
        {...props}
      />
    )
  }
)

PasswordInput.displayName = 'PasswordInput'

/**
 * 文本域属性
 */
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** 文本域大小 */
  size?: 'sm' | 'md' | 'lg'
  /** 是否全宽 */
  fullWidth?: boolean
  /** 是否可调整大小 */
  resizable?: boolean
}

/**
 * 文本域
 */
export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, size = 'md', fullWidth = true, resizable = true, ...props }, ref) => {
    const sizeClasses = {
      sm: 'px-2 py-1 text-sm',
      md: 'px-3 py-2 text-sm',
      lg: 'px-4 py-3 text-base',
    }

    return (
      <textarea
        ref={ref}
        className={clsx(
          'block border border-gray-300 rounded-md shadow-sm',
          'focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
          'disabled:bg-gray-50 disabled:text-gray-500',
          sizeClasses[size],
          {
            'w-full': fullWidth,
            'resize-none': !resizable,
            'resize-y': resizable,
          },
          className
        )}
        {...props}
      />
    )
  }
)

Textarea.displayName = 'Textarea'

/**
 * 选择框属性
 */
export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  /** 选择框大小 */
  size?: 'sm' | 'md' | 'lg'
  /** 是否全宽 */
  fullWidth?: boolean
  /** 选项列表 */
  options?: Array<{ value: string | number; label: string; disabled?: boolean }>
  /** 占位符 */
  placeholder?: string
}

/**
 * 选择框
 */
export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, size = 'md', fullWidth = true, options = [], placeholder, children, ...props }, ref) => {
    const sizeClasses = {
      sm: 'px-2 py-1 text-sm',
      md: 'px-3 py-2 text-sm',
      lg: 'px-4 py-3 text-base',
    }

    return (
      <select
        ref={ref}
        className={clsx(
          'block border border-gray-300 rounded-md shadow-sm',
          'focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
          'disabled:bg-gray-50 disabled:text-gray-500',
          'bg-white',
          sizeClasses[size],
          {
            'w-full': fullWidth,
          },
          className
        )}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
        
        {children}
      </select>
    )
  }
)

Select.displayName = 'Select'

/**
 * 复选框属性
 */
export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /** 标签文本 */
  label?: string
  /** 描述文本 */
  description?: string
}

/**
 * 复选框
 */
export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, id, ...props }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`

    return (
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            ref={ref}
            id={checkboxId}
            type="checkbox"
            className={clsx(
              'w-4 h-4 text-blue-600 border-gray-300 rounded',
              'focus:ring-blue-500 focus:ring-2',
              'disabled:bg-gray-50 disabled:border-gray-300',
              className
            )}
            {...props}
          />
        </div>
        
        {(label || description) && (
          <div className="ml-3 text-sm">
            {label && (
              <label htmlFor={checkboxId} className="font-medium text-gray-700 cursor-pointer">
                {label}
              </label>
            )}
            {description && (
              <p className="text-gray-500 mt-1">{description}</p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'

/**
 * 单选框组属性
 */
export interface RadioGroupProps {
  /** 组名称 */
  name: string
  /** 当前值 */
  value?: string | number
  /** 值变化回调 */
  onChange?: (value: string | number) => void
  /** 选项列表 */
  options: Array<{
    value: string | number
    label: string
    description?: string
    disabled?: boolean
  }>
  /** 自定义类名 */
  className?: string
}

/**
 * 单选框组
 */
export const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  options,
  className,
}) => {
  return (
    <div className={clsx('space-y-3', className)}>
      {options.map((option) => {
        const radioId = `${name}-${option.value}`
        
        return (
          <div key={option.value} className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id={radioId}
                name={name}
                type="radio"
                value={option.value}
                checked={value === option.value}
                onChange={() => onChange?.(option.value)}
                disabled={option.disabled}
                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2 disabled:bg-gray-50 disabled:border-gray-300"
              />
            </div>
            
            <div className="ml-3 text-sm">
              <label htmlFor={radioId} className="font-medium text-gray-700 cursor-pointer">
                {option.label}
              </label>
              {option.description && (
                <p className="text-gray-500 mt-1">{option.description}</p>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default {
  TextInput,
  PasswordInput,
  Textarea,
  Select,
  Checkbox,
  RadioGroup,
}
