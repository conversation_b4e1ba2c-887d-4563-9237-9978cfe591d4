#!/usr/bin/env node

/**
 * 代码质量检查脚本
 * 
 * 自动化检查TypeScript类型、ESLint规则、性能问题等
 * 
 * @since 2.0.0
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSection(title) {
  log(`\n${'='.repeat(50)}`, 'cyan')
  log(`${title}`, 'cyan')
  log(`${'='.repeat(50)}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

// 检查结果统计
const results = {
  typescript: { passed: false, errors: 0, warnings: 0 },
  eslint: { passed: false, errors: 0, warnings: 0 },
  prettier: { passed: false, errors: 0 },
  performance: { passed: false, issues: 0 },
  dependencies: { passed: false, vulnerabilities: 0 }
}

/**
 * 运行TypeScript类型检查
 */
function checkTypeScript() {
  logSection('TypeScript 类型检查')
  
  try {
    const output = execSync('npx tsc --noEmit --pretty', { 
      encoding: 'utf8',
      cwd: process.cwd()
    })
    
    logSuccess('TypeScript类型检查通过')
    results.typescript.passed = true
  } catch (error) {
    const output = error.stdout || error.message
    const lines = output.split('\n').filter(line => line.trim())
    
    // 解析错误和警告
    lines.forEach(line => {
      if (line.includes('error TS')) {
        results.typescript.errors++
      }
    })
    
    logError(`TypeScript类型检查失败: ${results.typescript.errors} 个错误`)
    log(output, 'red')
  }
}

/**
 * 运行ESLint检查
 */
function checkESLint() {
  logSection('ESLint 代码规范检查')
  
  try {
    const output = execSync('npx eslint src --ext .ts,.tsx --format=json', { 
      encoding: 'utf8',
      cwd: process.cwd()
    })
    
    const eslintResults = JSON.parse(output)
    let totalErrors = 0
    let totalWarnings = 0
    
    eslintResults.forEach(result => {
      totalErrors += result.errorCount
      totalWarnings += result.warningCount
    })
    
    results.eslint.errors = totalErrors
    results.eslint.warnings = totalWarnings
    
    if (totalErrors === 0) {
      results.eslint.passed = true
      logSuccess(`ESLint检查通过 (${totalWarnings} 个警告)`)
    } else {
      logError(`ESLint检查失败: ${totalErrors} 个错误, ${totalWarnings} 个警告`)
    }
    
    // 显示详细错误
    if (totalErrors > 0 || totalWarnings > 0) {
      eslintResults.forEach(result => {
        if (result.messages.length > 0) {
          log(`\n文件: ${result.filePath}`, 'yellow')
          result.messages.forEach(message => {
            const level = message.severity === 2 ? 'error' : 'warning'
            const color = message.severity === 2 ? 'red' : 'yellow'
            log(`  ${level}: ${message.message} (${message.ruleId})`, color)
          })
        }
      })
    }
  } catch (error) {
    logError('ESLint检查失败')
    log(error.message, 'red')
  }
}

/**
 * 运行Prettier格式检查
 */
function checkPrettier() {
  logSection('Prettier 代码格式检查')
  
  try {
    execSync('npx prettier --check src', { 
      encoding: 'utf8',
      cwd: process.cwd()
    })
    
    logSuccess('Prettier格式检查通过')
    results.prettier.passed = true
  } catch (error) {
    const output = error.stdout || error.message
    logError('Prettier格式检查失败')
    log(output, 'red')
    logInfo('运行 "npm run format" 来自动修复格式问题')
  }
}

/**
 * 检查性能问题
 */
function checkPerformance() {
  logSection('性能问题检查')
  
  const srcDir = path.join(process.cwd(), 'src')
  const issues = []
  
  function checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    const relativePath = path.relative(srcDir, filePath)
    
    // 检查常见性能问题
    const checks = [
      {
        pattern: /console\.log/g,
        message: '包含console.log语句，可能影响生产环境性能'
      },
      {
        pattern: /useEffect\(\s*\(\)\s*=>\s*{[\s\S]*?}\s*,\s*\[\s*\]\s*\)/g,
        message: '空依赖数组的useEffect可能导致性能问题'
      },
      {
        pattern: /useState\(\s*{[\s\S]*?}\s*\)/g,
        message: '使用对象作为useState初始值可能导致不必要的重渲染'
      },
      {
        pattern: /\.map\(\s*\([^)]*\)\s*=>\s*<[^>]*key=\{[^}]*index[^}]*\}/g,
        message: '使用数组索引作为key可能导致性能问题'
      }
    ]
    
    checks.forEach(check => {
      const matches = content.match(check.pattern)
      if (matches) {
        issues.push({
          file: relativePath,
          message: check.message,
          count: matches.length
        })
      }
    })
  }
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        walkDir(filePath)
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        checkFile(filePath)
      }
    })
  }
  
  walkDir(srcDir)
  
  results.performance.issues = issues.length
  
  if (issues.length === 0) {
    results.performance.passed = true
    logSuccess('性能检查通过')
  } else {
    logWarning(`发现 ${issues.length} 个潜在性能问题:`)
    issues.forEach(issue => {
      log(`  ${issue.file}: ${issue.message} (${issue.count} 处)`, 'yellow')
    })
  }
}

/**
 * 检查依赖安全性
 */
function checkDependencies() {
  logSection('依赖安全性检查')
  
  try {
    const output = execSync('npm audit --json', { 
      encoding: 'utf8',
      cwd: process.cwd()
    })
    
    const auditResult = JSON.parse(output)
    const vulnerabilities = auditResult.metadata?.vulnerabilities || {}
    const totalVulnerabilities = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0)
    
    results.dependencies.vulnerabilities = totalVulnerabilities
    
    if (totalVulnerabilities === 0) {
      results.dependencies.passed = true
      logSuccess('依赖安全检查通过')
    } else {
      logWarning(`发现 ${totalVulnerabilities} 个安全漏洞`)
      Object.entries(vulnerabilities).forEach(([severity, count]) => {
        if (count > 0) {
          log(`  ${severity}: ${count} 个`, 'yellow')
        }
      })
      logInfo('运行 "npm audit fix" 来修复可自动修复的漏洞')
    }
  } catch (error) {
    logError('依赖安全检查失败')
    log(error.message, 'red')
  }
}

/**
 * 生成质量报告
 */
function generateReport() {
  logSection('代码质量报告')
  
  const totalChecks = Object.keys(results).length
  const passedChecks = Object.values(results).filter(r => r.passed).length
  const score = Math.round((passedChecks / totalChecks) * 100)
  
  log(`\n总体评分: ${score}/100`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red')
  log(`通过检查: ${passedChecks}/${totalChecks}`)
  
  // 详细结果
  log('\n详细结果:')
  log(`  TypeScript: ${results.typescript.passed ? '✅' : '❌'} (${results.typescript.errors} 错误)`)
  log(`  ESLint: ${results.eslint.passed ? '✅' : '❌'} (${results.eslint.errors} 错误, ${results.eslint.warnings} 警告)`)
  log(`  Prettier: ${results.prettier.passed ? '✅' : '❌'}`)
  log(`  性能检查: ${results.performance.passed ? '✅' : '⚠️'} (${results.performance.issues} 问题)`)
  log(`  依赖安全: ${results.dependencies.passed ? '✅' : '⚠️'} (${results.dependencies.vulnerabilities} 漏洞)`)
  
  // 建议
  log('\n改进建议:')
  if (!results.typescript.passed) {
    log('  • 修复TypeScript类型错误以提高代码安全性', 'yellow')
  }
  if (!results.eslint.passed) {
    log('  • 修复ESLint错误以符合代码规范', 'yellow')
  }
  if (!results.prettier.passed) {
    log('  • 运行代码格式化以保持一致的代码风格', 'yellow')
  }
  if (results.performance.issues > 0) {
    log('  • 优化性能问题以提升用户体验', 'yellow')
  }
  if (results.dependencies.vulnerabilities > 0) {
    log('  • 更新依赖以修复安全漏洞', 'yellow')
  }
  
  return score >= 80
}

/**
 * 主函数
 */
function main() {
  log('🔍 开始代码质量检查...', 'cyan')
  
  checkTypeScript()
  checkESLint()
  checkPrettier()
  checkPerformance()
  checkDependencies()
  
  const passed = generateReport()
  
  log('\n' + '='.repeat(50), 'cyan')
  if (passed) {
    logSuccess('代码质量检查通过！')
    process.exit(0)
  } else {
    logError('代码质量检查未通过，请修复上述问题')
    process.exit(1)
  }
}

// 运行检查
if (require.main === module) {
  main()
}

module.exports = {
  checkTypeScript,
  checkESLint,
  checkPrettier,
  checkPerformance,
  checkDependencies,
  generateReport
}
