/**
 * 代码质量检查工具
 * 
 * 提供运行时代码质量检查和性能监控
 * 
 * @since 2.0.0
 */

/**
 * 组件性能监控器
 */
export class ComponentPerformanceMonitor {
  private static instance: ComponentPerformanceMonitor
  private metrics: Map<string, ComponentMetrics> = new Map()
  private observers: PerformanceObserver[] = []

  private constructor() {
    this.initializeObservers()
  }

  static getInstance(): ComponentPerformanceMonitor {
    if (!ComponentPerformanceMonitor.instance) {
      ComponentPerformanceMonitor.instance = new ComponentPerformanceMonitor()
    }
    return ComponentPerformanceMonitor.instance
  }

  private initializeObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    try {
      // 监控用户交互性能
      const interactionObserver = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'event') {
            this.recordInteraction(entry as PerformanceEventTiming)
          }
        })
      })

      interactionObserver.observe({ entryTypes: ['event'] })
      this.observers.push(interactionObserver)

      // 监控长任务
      const longTaskObserver = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          this.recordLongTask(entry)
        })
      })

      longTaskObserver.observe({ entryTypes: ['longtask'] })
      this.observers.push(longTaskObserver)
    } catch (error) {
      console.warn('Performance monitoring not supported:', error)
    }
  }

  recordComponentRender(componentName: string, renderTime: number): void {
    const metrics = this.getOrCreateMetrics(componentName)
    metrics.renderCount++
    metrics.totalRenderTime += renderTime
    metrics.averageRenderTime = metrics.totalRenderTime / metrics.renderCount
    metrics.lastRenderTime = renderTime

    // 检查性能问题
    this.checkPerformanceIssues(componentName, metrics)
  }

  recordComponentMount(componentName: string): void {
    const metrics = this.getOrCreateMetrics(componentName)
    metrics.mountCount++
    metrics.lastMountTime = Date.now()
  }

  recordComponentUnmount(componentName: string): void {
    const metrics = this.getOrCreateMetrics(componentName)
    metrics.unmountCount++
    
    if (metrics.lastMountTime) {
      const lifeTime = Date.now() - metrics.lastMountTime
      metrics.totalLifeTime += lifeTime
      metrics.averageLifeTime = metrics.totalLifeTime / metrics.unmountCount
    }
  }

  private recordInteraction(entry: PerformanceEventTiming): void {
    const duration = entry.processingEnd - entry.processingStart
    
    if (duration > 100) { // 超过100ms的交互被认为是慢的
      console.warn(`🐌 Slow interaction detected: ${entry.name} took ${duration.toFixed(2)}ms`)
    }
  }

  private recordLongTask(entry: PerformanceEntry): void {
    console.warn(`🐌 Long task detected: ${entry.duration.toFixed(2)}ms`)
  }

  private getOrCreateMetrics(componentName: string): ComponentMetrics {
    if (!this.metrics.has(componentName)) {
      this.metrics.set(componentName, {
        componentName,
        renderCount: 0,
        mountCount: 0,
        unmountCount: 0,
        totalRenderTime: 0,
        averageRenderTime: 0,
        lastRenderTime: 0,
        totalLifeTime: 0,
        averageLifeTime: 0,
        lastMountTime: null,
        issues: []
      })
    }
    return this.metrics.get(componentName)!
  }

  private checkPerformanceIssues(componentName: string, metrics: ComponentMetrics): void {
    const issues: string[] = []

    // 检查渲染时间过长
    if (metrics.lastRenderTime > 16) {
      issues.push(`Render time too long: ${metrics.lastRenderTime.toFixed(2)}ms`)
    }

    // 检查平均渲染时间
    if (metrics.averageRenderTime > 10) {
      issues.push(`Average render time too long: ${metrics.averageRenderTime.toFixed(2)}ms`)
    }

    // 检查频繁重渲染
    if (metrics.renderCount > 100) {
      issues.push(`Too many renders: ${metrics.renderCount}`)
    }

    // 检查内存泄漏迹象
    if (metrics.mountCount > metrics.unmountCount + 10) {
      issues.push(`Potential memory leak: ${metrics.mountCount} mounts vs ${metrics.unmountCount} unmounts`)
    }

    if (issues.length > 0) {
      metrics.issues = issues
      console.warn(`⚠️ Performance issues in ${componentName}:`, issues)
    }
  }

  getMetrics(componentName?: string): ComponentMetrics | ComponentMetrics[] {
    if (componentName) {
      return this.metrics.get(componentName) || this.getOrCreateMetrics(componentName)
    }
    return Array.from(this.metrics.values())
  }

  generateReport(): PerformanceReport {
    const allMetrics = Array.from(this.metrics.values())
    const totalComponents = allMetrics.length
    const componentsWithIssues = allMetrics.filter(m => m.issues.length > 0).length
    const averageRenderTime = allMetrics.reduce((sum, m) => sum + m.averageRenderTime, 0) / totalComponents
    const totalRenders = allMetrics.reduce((sum, m) => sum + m.renderCount, 0)

    return {
      timestamp: new Date().toISOString(),
      totalComponents,
      componentsWithIssues,
      averageRenderTime,
      totalRenders,
      topIssues: this.getTopIssues(allMetrics),
      recommendations: this.generateRecommendations(allMetrics)
    }
  }

  private getTopIssues(metrics: ComponentMetrics[]): Array<{ component: string; issues: string[] }> {
    return metrics
      .filter(m => m.issues.length > 0)
      .sort((a, b) => b.issues.length - a.issues.length)
      .slice(0, 10)
      .map(m => ({ component: m.componentName, issues: m.issues }))
  }

  private generateRecommendations(metrics: ComponentMetrics[]): string[] {
    const recommendations: string[] = []

    const slowComponents = metrics.filter(m => m.averageRenderTime > 10)
    if (slowComponents.length > 0) {
      recommendations.push(
        `Consider optimizing ${slowComponents.length} slow components: ${slowComponents.map(c => c.componentName).join(', ')}`
      )
    }

    const frequentlyRenderingComponents = metrics.filter(m => m.renderCount > 50)
    if (frequentlyRenderingComponents.length > 0) {
      recommendations.push(
        `Consider memoizing ${frequentlyRenderingComponents.length} frequently rendering components`
      )
    }

    const potentialLeaks = metrics.filter(m => m.mountCount > m.unmountCount + 5)
    if (potentialLeaks.length > 0) {
      recommendations.push(
        `Check for memory leaks in: ${potentialLeaks.map(c => c.componentName).join(', ')}`
      )
    }

    return recommendations
  }

  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

/**
 * 类型安全检查器
 */
export class TypeSafetyChecker {
  static checkApiResponse<T>(response: unknown, validator: (data: unknown) => data is T): T {
    if (!validator(response)) {
      throw new Error('API response does not match expected type')
    }
    return response
  }

  static createValidator<T>(schema: Record<string, string>): (data: unknown) => data is T {
    return (data: unknown): data is T => {
      if (typeof data !== 'object' || data === null) {
        return false
      }

      const obj = data as Record<string, unknown>
      
      return Object.entries(schema).every(([key, expectedType]) => {
        const value = obj[key]
        
        switch (expectedType) {
          case 'string':
            return typeof value === 'string'
          case 'number':
            return typeof value === 'number'
          case 'boolean':
            return typeof value === 'boolean'
          case 'array':
            return Array.isArray(value)
          case 'object':
            return typeof value === 'object' && value !== null
          case 'optional-string':
            return value === undefined || typeof value === 'string'
          case 'optional-number':
            return value === undefined || typeof value === 'number'
          case 'optional-boolean':
            return value === undefined || typeof value === 'boolean'
          default:
            return true
        }
      })
    }
  }
}

/**
 * 代码质量度量器
 */
export class CodeQualityMetrics {
  static measureComplexity(fn: Function): number {
    const fnString = fn.toString()
    let complexity = 1 // 基础复杂度

    // 计算圈复杂度
    const patterns = [
      /if\s*\(/g,
      /else\s+if\s*\(/g,
      /else\s*{/g,
      /switch\s*\(/g,
      /case\s+/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /do\s*{/g,
      /catch\s*\(/g,
      /\?\s*.*\s*:/g, // 三元操作符
      /&&/g,
      /\|\|/g
    ]

    patterns.forEach(pattern => {
      const matches = fnString.match(pattern)
      if (matches) {
        complexity += matches.length
      }
    })

    return complexity
  }

  static measureCohesion(componentCode: string): number {
    // 简单的内聚性度量：计算组件内部方法和状态的相关性
    const methods = componentCode.match(/const\s+\w+\s*=/g) || []
    const states = componentCode.match(/useState|useRef|useMemo|useCallback/g) || []
    const effects = componentCode.match(/useEffect/g) || []

    const totalElements = methods.length + states.length + effects.length
    if (totalElements === 0) return 1

    // 简化的内聚性计算
    const cohesionScore = Math.min(1, (states.length + effects.length) / totalElements)
    return cohesionScore
  }

  static measureCoupling(imports: string[]): number {
    // 计算耦合度：外部依赖数量
    const externalDependencies = imports.filter(imp => 
      !imp.startsWith('./') && !imp.startsWith('../') && !imp.startsWith('@/')
    )

    // 耦合度评分：依赖越少越好
    return Math.max(0, 1 - (externalDependencies.length / 20))
  }
}

// 类型定义
interface ComponentMetrics {
  componentName: string
  renderCount: number
  mountCount: number
  unmountCount: number
  totalRenderTime: number
  averageRenderTime: number
  lastRenderTime: number
  totalLifeTime: number
  averageLifeTime: number
  lastMountTime: number | null
  issues: string[]
}

interface PerformanceReport {
  timestamp: string
  totalComponents: number
  componentsWithIssues: number
  averageRenderTime: number
  totalRenders: number
  topIssues: Array<{ component: string; issues: string[] }>
  recommendations: string[]
}

// 全局实例
export const performanceMonitor = ComponentPerformanceMonitor.getInstance()

// 开发环境下自动启用性能监控
if (process.env.NODE_ENV === 'development') {
  // 定期生成性能报告
  setInterval(() => {
    const report = performanceMonitor.generateReport()
    if (report.componentsWithIssues > 0) {
      console.group('📊 Performance Report')
      console.log('Components with issues:', report.componentsWithIssues)
      console.log('Average render time:', report.averageRenderTime.toFixed(2) + 'ms')
      console.log('Recommendations:', report.recommendations)
      console.groupEnd()
    }
  }, 30000) // 每30秒检查一次
}
