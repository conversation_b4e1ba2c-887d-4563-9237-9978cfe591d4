/**
 * TextArea多行文本输入组件
 * 
 * 提供多行文本输入功能，支持自定义行数和样式
 * 
 * @since 2.0.0
 */

import React from 'react'
import { cn } from '../../utils/cn'

export interface TextAreaProps {
  label?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  placeholder?: string
  rows?: number
  disabled?: boolean
  required?: boolean
  helperText?: string
  error?: string
  className?: string
  maxLength?: number
}

export const TextArea: React.FC<TextAreaProps> = ({
  label,
  value,
  onChange,
  placeholder,
  rows = 3,
  disabled = false,
  required = false,
  helperText,
  error,
  className,
  maxLength
}) => {
  const textAreaId = React.useId()

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label 
          htmlFor={textAreaId}
          className="block text-sm font-medium text-gray-700"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        id={textAreaId}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        disabled={disabled}
        required={required}
        maxLength={maxLength}
        className={cn(
          'block w-full rounded-md border-gray-300 shadow-sm',
          'focus:border-blue-500 focus:ring-blue-500',
          'disabled:bg-gray-100 disabled:cursor-not-allowed',
          error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
          'sm:text-sm resize-vertical'
        )}
      />

      {/* 字符计数 */}
      {maxLength && (
        <div className="flex justify-end">
          <span className="text-xs text-gray-400">
            {value.length}/{maxLength}
          </span>
        </div>
      )}

      {(helperText || error) && (
        <div className="mt-1">
          {error ? (
            <p className="text-sm text-red-600">{error}</p>
          ) : (
            <p className="text-sm text-gray-500">{helperText}</p>
          )}
        </div>
      )}
    </div>
  )
}