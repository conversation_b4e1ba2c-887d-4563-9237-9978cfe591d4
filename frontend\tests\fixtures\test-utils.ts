/**
 * E2E测试工具函数
 * 
 * 提供通用的测试辅助功能
 * 
 * @since 2.0.0
 */

import { Page, Locator, expect } from '@playwright/test'
import fs from 'fs'
import path from 'path'

/**
 * 测试数据接口
 */
export interface TestData {
  timestamp: string
  baseURL: string
  currentSettings: {
    hasApiKey: boolean
    hasDatabaseId: boolean
    apiKeyLength: number
    databaseIdLength: number
  }
  testCredentials: {
    apiKey: string
    databaseId: string
    invalidApiKey: string
    invalidDatabaseId: string
  }
}

/**
 * 加载测试数据
 */
export function loadTestData(): TestData {
  const testDataFile = path.join(__dirname, '../test-data/setup-data.json')
  
  if (!fs.existsSync(testDataFile)) {
    throw new Error('测试数据文件不存在，请先运行全局设置')
  }
  
  return JSON.parse(fs.readFileSync(testDataFile, 'utf8'))
}

/**
 * 等待React应用加载完成
 */
export async function waitForReactApp(page: Page, timeout = 15000) {
  await page.waitForSelector('.notion-wp-admin', { timeout })
  
  // 等待加载状态消失
  await page.waitForFunction(
    () => !document.querySelector('.loading') && !document.querySelector('[data-loading="true"]'),
    { timeout: 10000 }
  ).catch(() => {
    // 忽略超时，继续执行
  })
  
  // 等待一小段时间确保所有异步操作完成
  await page.waitForTimeout(1000)
}

/**
 * 等待通知消息出现
 */
export async function waitForNotification(page: Page, type: 'success' | 'error' | 'warning' = 'success', timeout = 10000) {
  const selector = `.notification.${type}, .toast.${type}, .alert.${type}`
  await page.waitForSelector(selector, { timeout })
  
  // 获取通知内容
  const notification = page.locator(selector).first()
  const text = await notification.textContent()
  
  return text?.trim() || ''
}

/**
 * 清除所有通知
 */
export async function clearNotifications(page: Page) {
  const closeButtons = page.locator('.notification .close, .toast .close, .alert .close')
  const count = await closeButtons.count()
  
  for (let i = 0; i < count; i++) {
    await closeButtons.nth(i).click().catch(() => {})
  }
  
  // 等待通知消失
  await page.waitForTimeout(500)
}

/**
 * 填写表单字段
 */
export async function fillForm(page: Page, fields: Record<string, string>) {
  for (const [selector, value] of Object.entries(fields)) {
    const field = page.locator(selector)
    await field.clear()
    await field.fill(value)
  }
}

/**
 * 等待API请求完成
 */
export async function waitForApiRequest(page: Page, urlPattern: string | RegExp, timeout = 30000) {
  return page.waitForResponse(
    response => {
      const url = response.url()
      const matches = typeof urlPattern === 'string' 
        ? url.includes(urlPattern)
        : urlPattern.test(url)
      return matches && response.status() < 400
    },
    { timeout }
  )
}

/**
 * 模拟API响应
 */
export async function mockApiResponse(page: Page, urlPattern: string | RegExp, response: any) {
  await page.route(urlPattern, route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(response)
    })
  })
}

/**
 * 截图并保存
 */
export async function takeScreenshot(page: Page, name: string, fullPage = false) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const filename = `${name}-${timestamp}.png`
  const screenshotPath = path.join(process.cwd(), 'test-results/screenshots', filename)
  
  // 确保目录存在
  const dir = path.dirname(screenshotPath)
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
  
  await page.screenshot({ 
    path: screenshotPath, 
    fullPage 
  })
  
  return screenshotPath
}

/**
 * 检查元素是否可见且可交互
 */
export async function isElementInteractable(locator: Locator): Promise<boolean> {
  try {
    await expect(locator).toBeVisible()
    await expect(locator).toBeEnabled()
    return true
  } catch {
    return false
  }
}

/**
 * 等待元素状态变化
 */
export async function waitForElementState(
  locator: Locator, 
  state: 'visible' | 'hidden' | 'enabled' | 'disabled',
  timeout = 10000
) {
  switch (state) {
    case 'visible':
      await expect(locator).toBeVisible({ timeout })
      break
    case 'hidden':
      await expect(locator).toBeHidden({ timeout })
      break
    case 'enabled':
      await expect(locator).toBeEnabled({ timeout })
      break
    case 'disabled':
      await expect(locator).toBeDisabled({ timeout })
      break
  }
}

/**
 * 滚动到元素位置
 */
export async function scrollToElement(locator: Locator) {
  await locator.scrollIntoViewIfNeeded()
  await locator.page().waitForTimeout(500) // 等待滚动完成
}

/**
 * 重试操作
 */
export async function retry<T>(
  operation: () => Promise<T>,
  maxAttempts = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxAttempts) {
        throw lastError
      }
      
      console.log(`操作失败，第${attempt}次重试 (${maxAttempts}次中)...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError!
}

/**
 * 生成随机测试数据
 */
export function generateTestData() {
  const timestamp = Date.now()
  
  return {
    apiKey: `test_api_key_${timestamp}`,
    databaseId: `test_database_${timestamp}`,
    pageTitle: `测试页面_${timestamp}`,
    pageContent: `这是测试内容，生成时间：${new Date().toLocaleString('zh-CN')}`,
    email: `test${timestamp}@example.com`,
    randomString: Math.random().toString(36).substring(2, 15)
  }
}

/**
 * 验证页面性能
 */
export async function checkPagePerformance(page: Page) {
  const performanceMetrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
    }
  })
  
  // 性能断言
  expect(performanceMetrics.totalLoadTime).toBeLessThan(10000) // 总加载时间小于10秒
  expect(performanceMetrics.firstContentfulPaint).toBeLessThan(3000) // FCP小于3秒
  
  return performanceMetrics
}

/**
 * 检查控制台错误
 */
export function setupConsoleErrorTracking(page: Page) {
  const errors: string[] = []
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text())
    }
  })
  
  page.on('pageerror', error => {
    errors.push(error.message)
  })
  
  return {
    getErrors: () => errors,
    hasErrors: () => errors.length > 0,
    clearErrors: () => errors.splice(0, errors.length)
  }
}
