/**
 * 响应式表格组件
 * 
 * 在不同屏幕尺寸下提供优化的表格显示体验
 * 
 * @since 2.0.0
 */

import React, { useState, useMemo } from 'react'
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowsUpDownIcon
} from '@heroicons/react/24/outline'
import { useResponsive } from '../../hooks/useResponsive'
import { useI18n } from '../../hooks/useI18n'

export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex: string
  width?: string | number
  minWidth?: string | number
  render?: (value: any, record: T, index: number) => React.ReactNode
  sortable?: boolean
  hiddenOnMobile?: boolean
  priority?: 'high' | 'medium' | 'low' // 移动端显示优先级
}

export interface ResponsiveTableProps<T = any> {
  columns: TableColumn<T>[]
  dataSource: T[]
  loading?: boolean
  pagination?: boolean
  pageSize?: number
  className?: string
  emptyText?: string
  onRowClick?: (record: T, index: number) => void
  expandable?: {
    expandedRowRender: (record: T, index: number) => React.ReactNode
    rowExpandable?: (record: T) => boolean
  }
}

export const ResponsiveTable = <T extends Record<string, any>>({
  columns,
  dataSource,
  loading = false,
  pagination = false,
  pageSize = 10,
  className = '',
  emptyText,
  onRowClick,
  expandable
}: ResponsiveTableProps<T>) => {
  const { isMobile, isTablet } = useResponsive()
  const { __ } = useI18n()
  
  const [currentPage, setCurrentPage] = useState(1)
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set())

  // 根据设备类型过滤列
  const visibleColumns = useMemo(() => {
    if (isMobile) {
      // 移动端只显示高优先级列
      return columns.filter(col => 
        !col.hiddenOnMobile && 
        (!col.priority || col.priority === 'high')
      )
    }
    if (isTablet) {
      // 平板端显示高和中优先级列
      return columns.filter(col => 
        !hiddenColumns.has(col.key) &&
        (!col.priority || ['high', 'medium'].includes(col.priority))
      )
    }
    // 桌面端显示所有未隐藏的列
    return columns.filter(col => !hiddenColumns.has(col.key))
  }, [columns, isMobile, isTablet, hiddenColumns])

  // 排序数据
  const sortedData = useMemo(() => {
    if (!sortConfig) return dataSource

    return [...dataSource].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [dataSource, sortConfig])

  // 分页数据
  const paginatedData = useMemo(() => {
    if (!pagination) return sortedData
    
    const startIndex = (currentPage - 1) * pageSize
    return sortedData.slice(startIndex, startIndex + pageSize)
  }, [sortedData, pagination, currentPage, pageSize])

  // 处理排序
  const handleSort = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey)
    if (!column?.sortable) return

    setSortConfig(prev => {
      if (prev?.key === columnKey) {
        if (prev.direction === 'asc') {
          return { key: columnKey, direction: 'desc' }
        } else {
          return null // 取消排序
        }
      }
      return { key: columnKey, direction: 'asc' }
    })
  }

  // 切换行展开
  const toggleRowExpansion = (index: number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }

  // 切换列显示
  const toggleColumnVisibility = (columnKey: string) => {
    setHiddenColumns(prev => {
      const newSet = new Set(prev)
      if (newSet.has(columnKey)) {
        newSet.delete(columnKey)
      } else {
        newSet.add(columnKey)
      }
      return newSet
    })
  }

  // 渲染单元格内容
  const renderCell = (column: TableColumn<T>, record: T, index: number) => {
    const value = record[column.dataIndex]
    if (column.render) {
      return column.render(value, record, index)
    }
    return value
  }

  // 移动端卡片式布局
  if (isMobile) {
    return (
      <div className={`responsive-table mobile-cards ${className}`}>
        {loading && (
          <div className="table-loading">
            <div className="loading-spinner" />
            <span>{__('Loading...')}</span>
          </div>
        )}
        
        {!loading && paginatedData.length === 0 && (
          <div className="table-empty">
            <p>{emptyText || __('No data available')}</p>
          </div>
        )}

        {!loading && paginatedData.map((record, index) => (
          <div 
            key={index} 
            className={`mobile-card ${onRowClick ? 'clickable' : ''}`}
            onClick={() => onRowClick?.(record, index)}
          >
            {visibleColumns.map(column => (
              <div key={column.key} className="mobile-card-row">
                <div className="mobile-card-label">{column.title}:</div>
                <div className="mobile-card-value">
                  {renderCell(column, record, index)}
                </div>
              </div>
            ))}
            
            {expandable && expandable.rowExpandable?.(record) !== false && (
              <button
                className="mobile-card-expand"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleRowExpansion(index)
                }}
              >
                {expandedRows.has(index) ? (
                  <>
                    <ChevronUpIcon className="h-4 w-4" />
                    {__('Collapse')}
                  </>
                ) : (
                  <>
                    <ChevronDownIcon className="h-4 w-4" />
                    {__('Expand')}
                  </>
                )}
              </button>
            )}
            
            {expandable && expandedRows.has(index) && (
              <div className="mobile-card-expanded">
                {expandable.expandedRowRender(record, index)}
              </div>
            )}
          </div>
        ))}

        {pagination && (
          <div className="table-pagination mobile-pagination">
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => prev - 1)}
            >
              {__('Previous')}
            </button>
            <span>{currentPage} / {Math.ceil(sortedData.length / pageSize)}</span>
            <button
              disabled={currentPage >= Math.ceil(sortedData.length / pageSize)}
              onClick={() => setCurrentPage(prev => prev + 1)}
            >
              {__('Next')}
            </button>
          </div>
        )}
      </div>
    )
  }

  // 桌面端和平板端表格布局
  return (
    <div className={`responsive-table ${className}`}>
      {/* 列控制器（仅平板端显示） */}
      {isTablet && (
        <div className="table-controls">
          <div className="column-toggles">
            {columns.map(column => (
              <button
                key={column.key}
                className={`column-toggle ${hiddenColumns.has(column.key) ? 'hidden' : ''}`}
                onClick={() => toggleColumnVisibility(column.key)}
              >
                {hiddenColumns.has(column.key) ? (
                  <EyeSlashIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
                {column.title}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="table-container">
        <table className="responsive-table-element">
          <thead>
            <tr>
              {visibleColumns.map(column => (
                <th 
                  key={column.key}
                  style={{ 
                    width: column.width,
                    minWidth: column.minWidth 
                  }}
                  className={column.sortable ? 'sortable' : ''}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="th-content">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span className="sort-icon">
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )
                        ) : (
                          <ArrowsUpDownIcon className="h-4 w-4" />
                        )}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {expandable && <th className="expand-column"></th>}
            </tr>
          </thead>
          <tbody>
            {loading && (
              <tr>
                <td colSpan={visibleColumns.length + (expandable ? 1 : 0)} className="table-loading">
                  <div className="loading-spinner" />
                  <span>{__('Loading...')}</span>
                </td>
              </tr>
            )}
            
            {!loading && paginatedData.length === 0 && (
              <tr>
                <td colSpan={visibleColumns.length + (expandable ? 1 : 0)} className="table-empty">
                  {emptyText || __('No data available')}
                </td>
              </tr>
            )}

            {!loading && paginatedData.map((record, index) => (
              <React.Fragment key={index}>
                <tr 
                  className={onRowClick ? 'clickable' : ''}
                  onClick={() => onRowClick?.(record, index)}
                >
                  {visibleColumns.map(column => (
                    <td key={column.key}>
                      {renderCell(column, record, index)}
                    </td>
                  ))}
                  {expandable && (
                    <td className="expand-cell">
                      {expandable.rowExpandable?.(record) !== false && (
                        <button
                          className="expand-button"
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleRowExpansion(index)
                          }}
                        >
                          {expandedRows.has(index) ? (
                            <ChevronUpIcon className="h-4 w-4" />
                          ) : (
                            <ChevronDownIcon className="h-4 w-4" />
                          )}
                        </button>
                      )}
                    </td>
                  )}
                </tr>
                {expandable && expandedRows.has(index) && (
                  <tr className="expanded-row">
                    <td colSpan={visibleColumns.length + 1}>
                      {expandable.expandedRowRender(record, index)}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="table-pagination">
          <div className="pagination-info">
            {__('Showing {{start}} to {{end}} of {{total}} entries', {
              start: (currentPage - 1) * pageSize + 1,
              end: Math.min(currentPage * pageSize, sortedData.length),
              total: sortedData.length
            })}
          </div>
          <div className="pagination-controls">
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => prev - 1)}
            >
              {__('Previous')}
            </button>
            <span>{currentPage} / {Math.ceil(sortedData.length / pageSize)}</span>
            <button
              disabled={currentPage >= Math.ceil(sortedData.length / pageSize)}
              onClick={() => setCurrentPage(prev => prev + 1)}
            >
              {__('Next')}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
