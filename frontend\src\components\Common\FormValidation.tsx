/**
 * 表单验证组件 - 现代化的实时验证系统
 * 
 * 替代jQuery的实时验证逻辑，提供更好的用户体验
 * 支持实时验证、视觉反馈、可访问性等功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { cn } from '../../utils/cn';
import { useSettingsStore } from '../../stores/settingsStore';

// 验证状态类型
export type ValidationStatus = 'idle' | 'validating' | 'success' | 'warning' | 'error';

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  level: 'success' | 'warning' | 'error';
  message: string;
}

// 验证规则接口
export interface ValidationRule {
  type: 'api-key' | 'database-id' | 'page-id' | 'custom';
  required?: boolean;
  customValidator?: (value: string) => ValidationResult;
}

// 验证输入框Props
interface ValidatedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  validationRule: ValidationRule;
  onValidationChange?: (result: ValidationResult) => void;
  showValidationIcon?: boolean;
  debounceMs?: number;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
}

// 内置验证器
const validators = {
  'api-key': (value: string): ValidationResult => {
    if (!value) {
      return {
        isValid: false,
        level: 'error',
        message: 'API密钥不能为空',
      };
    }
    
    if (value.length < 30 || value.length > 80) {
      return {
        isValid: false,
        level: 'warning',
        message: 'API密钥长度可能不正确，请检查是否完整',
      };
    }
    
    if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
      return {
        isValid: false,
        level: 'warning',
        message: 'API密钥格式可能不正确，应只包含字母、数字、下划线和连字符',
      };
    }
    
    return {
      isValid: true,
      level: 'success',
      message: 'API密钥格式正确',
    };
  },

  'database-id': (value: string): ValidationResult => {
    if (!value) {
      return {
        isValid: false,
        level: 'error',
        message: '数据库ID不能为空',
      };
    }
    
    // 移除连字符
    const cleanValue = value.replace(/-/g, '');
    
    if (cleanValue.length !== 32) {
      return {
        isValid: false,
        level: 'error',
        message: '数据库ID长度应为32位字符',
      };
    }
    
    if (!/^[a-f0-9]{32}$/i.test(cleanValue)) {
      return {
        isValid: false,
        level: 'error',
        message: '数据库ID格式不正确，应为32位十六进制字符',
      };
    }
    
    return {
      isValid: true,
      level: 'success',
      message: '数据库ID格式正确',
    };
  },

  'page-id': (value: string): ValidationResult => {
    if (!value) {
      return {
        isValid: false,
        level: 'error',
        message: '页面ID不能为空',
      };
    }
    
    // 移除连字符
    const cleanValue = value.replace(/-/g, '');
    
    if (cleanValue.length !== 32) {
      return {
        isValid: false,
        level: 'error',
        message: '页面ID长度应为32位字符',
      };
    }
    
    if (!/^[a-f0-9]{32}$/i.test(cleanValue)) {
      return {
        isValid: false,
        level: 'error',
        message: '页面ID格式不正确，应为32位十六进制字符',
      };
    }
    
    return {
      isValid: true,
      level: 'success',
      message: '页面ID格式正确',
    };
  },
};

// 验证输入框组件
export const ValidatedInput: React.FC<ValidatedInputProps> = ({
  validationRule,
  onValidationChange,
  showValidationIcon = true,
  debounceMs = 500,
  validateOnBlur = true,
  validateOnChange = true,
  className,
  ...inputProps
}) => {
  const [value, setValue] = useState(inputProps.value || '');
  const [validationStatus, setValidationStatus] = useState<ValidationStatus>('idle');
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const settingsStore = useSettingsStore();

  // 执行验证
  const performValidation = useCallback((inputValue: string) => {
    if (!inputValue && !validationRule.required) {
      const result: ValidationResult = {
        isValid: true,
        level: 'success',
        message: '',
      };
      setValidationResult(result);
      setValidationStatus('idle');
      onValidationChange?.(result);
      return;
    }

    setValidationStatus('validating');

    let result: ValidationResult;

    if (validationRule.type === 'custom' && validationRule.customValidator) {
      result = validationRule.customValidator(inputValue);
    } else if (validators[validationRule.type]) {
      result = validators[validationRule.type](inputValue);
    } else {
      result = {
        isValid: true,
        level: 'success',
        message: '',
      };
    }

    setValidationResult(result);
    setValidationStatus(result.isValid ? 'success' : result.level);
    onValidationChange?.(result);

    // 同步到Zustand store
    if (inputProps.name) {
      settingsStore.setFieldValidation(inputProps.name, {
        isValid: result.isValid,
        level: result.level,
        message: result.message,
      });
    }
  }, [validationRule, onValidationChange, settingsStore, inputProps.name]);

  // 防抖验证
  const debouncedValidation = useCallback(
    debounce(performValidation, debounceMs),
    [performValidation, debounceMs]
  );

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    
    if (validateOnChange) {
      debouncedValidation(newValue);
    }
  };

  // 处理失焦
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (validateOnBlur) {
      debouncedValidation.cancel();
      performValidation(e.target.value);
    }
    inputProps.onBlur?.(e);
  };

  // 清理防抖
  useEffect(() => {
    return () => {
      debouncedValidation.cancel();
    };
  }, [debouncedValidation]);

  // 获取验证图标
  const getValidationIcon = () => {
    if (!showValidationIcon || validationStatus === 'idle') return null;

    switch (validationStatus) {
      case 'validating':
        return (
          <div className="animate-spin w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full" />
        );
      case 'success':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  // 获取输入框样式
  const getInputStyles = () => {
    const baseStyles = 'block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors';
    
    switch (validationStatus) {
      case 'success':
        return cn(baseStyles, 'border-green-300 focus:border-green-500 focus:ring-green-500');
      case 'warning':
        return cn(baseStyles, 'border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500');
      case 'error':
        return cn(baseStyles, 'border-red-300 focus:border-red-500 focus:ring-red-500');
      default:
        return cn(baseStyles, 'border-gray-300 focus:border-blue-500 focus:ring-blue-500');
    }
  };

  return (
    <div className="notion-wp-validated-input">
      <div className="relative">
        <input
          {...inputProps}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(getInputStyles(), showValidationIcon && 'pr-10', className)}
          aria-invalid={validationStatus === 'error'}
          aria-describedby={validationResult?.message ? `${inputProps.id}-feedback` : undefined}
        />
        
        {showValidationIcon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {getValidationIcon()}
          </div>
        )}
      </div>
      
      {validationResult?.message && (
        <div
          id={`${inputProps.id}-feedback`}
          className={cn(
            'mt-1 text-sm',
            validationStatus === 'success' && 'text-green-600',
            validationStatus === 'warning' && 'text-yellow-600',
            validationStatus === 'error' && 'text-red-600'
          )}
        >
          {validationResult.message}
        </div>
      )}
    </div>
  );
};

// 表单验证容器组件
interface FormValidationContainerProps {
  children: React.ReactNode;
  onValidationChange?: (isValid: boolean) => void;
}

export const FormValidationContainer: React.FC<FormValidationContainerProps> = ({
  children,
  onValidationChange,
}) => {
  const settingsStore = useSettingsStore();
  const { isFormValid } = settingsStore.formValidation;

  useEffect(() => {
    onValidationChange?.(isFormValid);
  }, [isFormValid, onValidationChange]);

  return <div className="notion-wp-form-validation">{children}</div>;
};

export default ValidatedInput;
