/**
 * React错误边界组件
 * 集成统一错误处理系统，提供优雅的错误恢复和用户友好的错误界面
 *
 * @since 2.0.0
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from './Button'
import { Card, CardContent, CardHeader } from './Card'
import { errorHandler, ErrorType, ErrorSeverity } from '../../utils/errorHandler'

// 错误边界状态接口
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
  isReporting: boolean
  reportSuccess: boolean
}

// 错误边界属性接口
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showErrorDetails?: boolean
  enableErrorReporting?: boolean
  level?: 'page' | 'component' | 'section'
  maxRetries?: number
  enableAutoRetry?: boolean
}

// 错误报告接口
interface ErrorReport {
  message: string
  stack: string
  componentStack: string
  userAgent: string
  url: string
  timestamp: number
  context: Record<string, any>
}

/**
 * React错误边界组件
 * 捕获子组件中的JavaScript错误，并提供优雅的错误处理
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      isReporting: false,
      reportSuccess: false
    }
  }

  /**
   * 捕获错误并更新状态
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    }
  }

  /**
   * 处理组件错误
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo })

    // 使用统一错误处理系统
    this.handleErrorWithSystem(error, errorInfo)

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // 自动报告错误（如果启用）
    if (this.props.enableErrorReporting !== false) {
      this.reportError(error, errorInfo)
    }

    // 自动重试（如果启用）
    if (this.props.enableAutoRetry && this.retryCount < (this.props.maxRetries || 3)) {
      setTimeout(() => {
        this.handleRetry()
      }, 2000) // 2秒后自动重试
    }
  }

  /**
   * 使用统一错误处理系统处理错误
   */
  private async handleErrorWithSystem(error: Error, errorInfo: ErrorInfo) {
    const context = {
      componentStack: errorInfo.componentStack,
      level: this.props.level || 'component',
      retryCount: this.retryCount,
      maxRetries: this.props.maxRetries || 3,
      errorBoundaryId: this.state.errorId,
      timestamp: new Date().toISOString()
    }

    await errorHandler.handleError(error, context)
  }

  /**
   * 报告错误到WordPress后端
   */
  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    if (this.state.isReporting) return
    
    this.setState({ isReporting: true })
    
    try {
      const errorReport: ErrorReport = {
        message: error.message,
        stack: error.stack || '',
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now(),
        context: {
          errorId: this.state.errorId,
          props: this.props,
          state: this.state
        }
      }
      
      // 调用WordPress AJAX API报告错误
      const formData = new FormData()
      formData.append('action', 'notion_report_error')
      formData.append('nonce', window.wpNotionConfig?.nonce || '')
      formData.append('error_data', JSON.stringify(errorReport))
      
      const response = await fetch(window.wpNotionConfig?.ajaxUrl || '/wp-admin/admin-ajax.php', {
        method: 'POST',
        body: formData
      })
      
      const result = await response.json()
      
      if (result.success) {
        this.setState({ reportSuccess: true })
        console.log('错误报告成功发送到WordPress后端')
      } else {
        console.error('错误报告发送失败:', result.data?.message)
      }
    } catch (reportError) {
      console.error('发送错误报告时出错:', reportError)
    } finally {
      this.setState({ isReporting: false })
    }
  }

  /**
   * 重置错误状态
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      isReporting: false,
      reportSuccess: false
    })
  }

  /**
   * 刷新页面
   */
  private handleRefresh = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }
      
      // 默认错误界面
      return (
        <Card className="max-w-2xl mx-auto mt-8 border-red-200 bg-red-50">
          <CardHeader className="text-center">
            <div className="text-red-600 text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-red-800">
              哎呀，出现了一个错误
            </h2>
            <p className="text-red-600 mt-2">
              很抱歉，应用程序遇到了意外错误。我们已经记录了这个问题。
            </p>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* 错误ID */}
            {this.state.errorId && (
              <div className="text-sm text-gray-600 bg-gray-100 p-2 rounded">
                <strong>错误ID:</strong> {this.state.errorId}
              </div>
            )}
            
            {/* 错误详情（开发模式） */}
            {this.props.showErrorDetails && this.state.error && (
              <details className="text-sm">
                <summary className="cursor-pointer font-medium text-red-700 mb-2">
                  查看错误详情
                </summary>
                <div className="bg-red-100 p-3 rounded border border-red-200">
                  <div className="mb-2">
                    <strong>错误消息:</strong> {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div className="mb-2">
                      <strong>堆栈跟踪:</strong>
                      <pre className="text-xs mt-1 overflow-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>组件堆栈:</strong>
                      <pre className="text-xs mt-1 overflow-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}
            
            {/* 错误报告状态 */}
            {this.state.isReporting && (
              <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                正在发送错误报告...
              </div>
            )}
            
            {this.state.reportSuccess && (
              <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                ✅ 错误报告已发送，我们会尽快修复这个问题。
              </div>
            )}
            
            {/* 操作按钮 */}
            <div className="flex gap-3 justify-center">
              <Button
                variant="outline"
                onClick={this.handleReset}
                className="border-red-300 text-red-700 hover:bg-red-50"
              >
                重试
              </Button>
              <Button
                variant="primary"
                onClick={this.handleRefresh}
                className="bg-red-600 hover:bg-red-700"
              >
                刷新页面
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    }
    
    return this.props.children
  }
}
