{"name": "notion-to-wordpress-frontend", "version": "2.0.0-beta.1", "description": "React frontend for Notion-to-WordPress plugin", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:analyze": "ANALYZE=true npm run build", "build:production": "NODE_ENV=production npm run build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src", "format:check": "prettier --check src", "quality:check": "node scripts/quality-check.js", "quality:fix": "npm run lint:fix && npm run format", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:performance": "npm run build && npx playwright test tests/e2e/performance.spec.ts", "analyze:bundle": "npm run build:analyze", "pre-commit": "npm run quality:check"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.0", "@tanstack/react-query": "^4.32.0", "@types/lodash": "^4.17.20", "@types/lodash-es": "^4.17.12", "axios": "^1.4.0", "clsx": "^2.1.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "tailwind-merge": "^3.3.1", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.22.0", "@types/node": "^20.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.24", "@playwright/test": "^1.40.0", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.3.0", "typescript": "^5.0.4", "vite": "^4.4.0"}, "engines": {"node": ">=16.0.0"}}