#!/usr/bin/env node

/**
 * E2E测试运行脚本
 * 
 * 自动化运行端到端测试并生成报告
 * 
 * @since 2.0.0
 */

const { execSync, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan')
  log(`${title}`, 'cyan')
  log(`${'='.repeat(60)}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

/**
 * 检查环境准备
 */
function checkEnvironment() {
  logSection('环境检查')
  
  try {
    // 检查Node.js版本
    const nodeVersion = process.version
    logInfo(`Node.js版本: ${nodeVersion}`)
    
    // 检查npm版本
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
    logInfo(`npm版本: ${npmVersion}`)
    
    // 检查Playwright是否安装
    try {
      execSync('npx playwright --version', { encoding: 'utf8' })
      logSuccess('Playwright已安装')
    } catch (error) {
      logWarning('Playwright未安装，正在安装...')
      execSync('npm install @playwright/test', { stdio: 'inherit' })
      logSuccess('Playwright安装完成')
    }
    
    // 检查浏览器是否安装
    try {
      execSync('npx playwright install --dry-run', { encoding: 'utf8' })
      logSuccess('浏览器已安装')
    } catch (error) {
      logWarning('浏览器未安装，正在安装...')
      execSync('npx playwright install', { stdio: 'inherit' })
      logSuccess('浏览器安装完成')
    }
    
    // 检查WordPress站点
    const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://frankloong.local'
    logInfo(`测试站点: ${baseURL}`)
    
    return true
  } catch (error) {
    logError(`环境检查失败: ${error.message}`)
    return false
  }
}

/**
 * 运行测试
 */
function runTests(options = {}) {
  logSection('运行E2E测试')
  
  const {
    project = 'chromium',
    headed = false,
    debug = false,
    ui = false,
    grep = '',
    workers = undefined
  } = options
  
  const args = ['playwright', 'test']
  
  // 添加项目选择
  if (project !== 'all') {
    args.push('--project', project)
  }
  
  // 添加其他选项
  if (headed) args.push('--headed')
  if (debug) args.push('--debug')
  if (ui) args.push('--ui')
  if (grep) args.push('--grep', grep)
  if (workers) args.push('--workers', workers.toString())
  
  try {
    logInfo(`运行命令: npx ${args.join(' ')}`)
    execSync(`npx ${args.join(' ')}`, { 
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    logSuccess('测试运行完成')
    return true
  } catch (error) {
    logError(`测试运行失败: ${error.message}`)
    return false
  }
}

/**
 * 生成测试报告
 */
function generateReport() {
  logSection('生成测试报告')
  
  try {
    // 检查测试结果文件
    const resultsFile = path.join(process.cwd(), 'test-results/results.json')
    const htmlReportDir = path.join(process.cwd(), 'test-results/html-report')
    
    if (fs.existsSync(resultsFile)) {
      const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'))
      
      logInfo('测试结果摘要:')
      log(`  总测试数: ${results.stats?.total || 0}`)
      log(`  通过: ${results.stats?.passed || 0}`, 'green')
      log(`  失败: ${results.stats?.failed || 0}`, results.stats?.failed > 0 ? 'red' : 'reset')
      log(`  跳过: ${results.stats?.skipped || 0}`, 'yellow')
      log(`  执行时长: ${Math.round((results.stats?.duration || 0) / 1000)}秒`)
      
      const passRate = results.stats?.total > 0 
        ? Math.round((results.stats.passed / results.stats.total) * 100)
        : 0
      
      log(`  通过率: ${passRate}%`, passRate >= 80 ? 'green' : passRate >= 60 ? 'yellow' : 'red')
    }
    
    if (fs.existsSync(htmlReportDir)) {
      logSuccess(`HTML报告已生成: ${htmlReportDir}/index.html`)
      
      // 尝试打开报告
      if (process.platform === 'win32') {
        try {
          execSync(`start ${path.join(htmlReportDir, 'index.html')}`)
          logInfo('已在浏览器中打开测试报告')
        } catch (error) {
          logInfo('请手动打开测试报告')
        }
      }
    }
    
    return true
  } catch (error) {
    logError(`生成报告失败: ${error.message}`)
    return false
  }
}

/**
 * 清理测试环境
 */
function cleanup() {
  logSection('清理测试环境')
  
  try {
    // 清理临时文件
    const tempDirs = [
      'test-results/artifacts',
      'test-results/screenshots'
    ]
    
    tempDirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir)
      if (fs.existsSync(fullPath)) {
        const files = fs.readdirSync(fullPath)
        files.forEach(file => {
          if (file.startsWith('temp-') || file.includes('debug-')) {
            fs.unlinkSync(path.join(fullPath, file))
          }
        })
      }
    })
    
    logSuccess('清理完成')
    return true
  } catch (error) {
    logWarning(`清理失败: ${error.message}`)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2)
  const options = {}
  
  // 解析命令行参数
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--project':
        options.project = args[++i]
        break
      case '--headed':
        options.headed = true
        break
      case '--debug':
        options.debug = true
        break
      case '--ui':
        options.ui = true
        break
      case '--grep':
        options.grep = args[++i]
        break
      case '--workers':
        options.workers = parseInt(args[++i])
        break
      case '--help':
        showHelp()
        return
      default:
        if (arg.startsWith('--')) {
          logWarning(`未知参数: ${arg}`)
        }
    }
  }
  
  log('🎭 Notion to WordPress E2E测试运行器', 'cyan')
  log('================================================', 'cyan')
  
  // 执行测试流程
  const steps = [
    { name: '环境检查', fn: () => checkEnvironment() },
    { name: '运行测试', fn: () => runTests(options) },
    { name: '生成报告', fn: () => generateReport() },
    { name: '清理环境', fn: () => cleanup() }
  ]
  
  let allPassed = true
  
  for (const step of steps) {
    try {
      const result = step.fn()
      if (!result) {
        allPassed = false
        if (step.name === '运行测试') {
          // 测试失败时仍然生成报告
          continue
        } else {
          break
        }
      }
    } catch (error) {
      logError(`${step.name}失败: ${error.message}`)
      allPassed = false
      break
    }
  }
  
  // 最终结果
  log('\n' + '='.repeat(60), 'cyan')
  if (allPassed) {
    logSuccess('🎉 E2E测试完成！所有步骤都成功执行。')
    process.exit(0)
  } else {
    logError('💥 E2E测试过程中出现问题，请检查上述错误信息。')
    process.exit(1)
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('Notion to WordPress E2E测试运行器', 'cyan')
  log('')
  log('用法: node run-e2e-tests.js [选项]', 'yellow')
  log('')
  log('选项:')
  log('  --project <name>    指定测试项目 (chromium, firefox, webkit, mobile-chrome, mobile-safari, all)')
  log('  --headed           在有头模式下运行浏览器')
  log('  --debug            在调试模式下运行')
  log('  --ui               在UI模式下运行')
  log('  --grep <pattern>   只运行匹配模式的测试')
  log('  --workers <num>    指定并行工作进程数')
  log('  --help             显示此帮助信息')
  log('')
  log('示例:')
  log('  node run-e2e-tests.js                    # 运行所有测试')
  log('  node run-e2e-tests.js --project firefox  # 只在Firefox中运行')
  log('  node run-e2e-tests.js --headed --debug   # 有头模式调试')
  log('  node run-e2e-tests.js --grep "API设置"    # 只运行API设置相关测试')
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  checkEnvironment,
  runTests,
  generateReport,
  cleanup
}
