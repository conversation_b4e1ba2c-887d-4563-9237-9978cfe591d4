/**
 * 基础UI组件库导出
 * 统一导出所有基础组件和类型
 */

// 组件导出
export { Button } from './Button'
export type { ButtonProps } from './Button'

export { Input } from './Input'
export type { InputProps } from './Input'

export { Select } from './Select'
export type { SelectProps, SelectOption } from './Select'

export { TextArea } from './TextArea'
export type { TextAreaProps } from './TextArea'

export { Checkbox } from './Checkbox'
export type { CheckboxProps } from './Checkbox'

export { Card, CardHeader, CardContent, CardFooter } from './Card'
export type { CardProps } from './Card'

export { Loading, InlineLoading } from './Loading'
export type { LoadingProps } from './Loading'

export { Modal, ConfirmModal } from './Modal'
export type { ModalProps } from './Modal'

export { NotificationContainer, useNotification, showNotification } from './Notification'

export { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from './Tabs'

export { ValidatedInput, FormValidationContainer } from './FormValidation'
export type { ValidationResult, ValidationRule } from './FormValidation'

export { ResponsiveTable } from './ResponsiveTable'
export type { TableColumn, ResponsiveTableProps } from './ResponsiveTable'

export { ErrorBoundary, withErrorBoundary, useErrorHandler } from './ErrorBoundary'

// 类型导出
export type {
  Size,
  Variant,
  Status,
  BaseComponentProps,
  ButtonVariant,
  InputType,
  NotificationType,
  ModalSize,
  Position,
  Alignment
} from './types'

// 工具函数导出
export { cn, notionClass, notionCn } from '../../utils/cn'
