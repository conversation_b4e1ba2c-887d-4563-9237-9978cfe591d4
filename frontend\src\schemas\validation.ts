/**
 * Zod验证模式定义
 * 
 * 基于现有PHP验证规则创建类型安全的前端验证
 * 
 * @since 2.0.0
 */

import { z } from 'zod'

/**
 * 验证规则常量 - 与PHP Validation_Rules类保持一致
 */
export const VALIDATION_RULES = {
  API_KEY: {
    MIN_LENGTH: 30,
    MAX_LENGTH: 80,
    PATTERN: /^[a-zA-Z0-9_-]+$/,
  },
  DATABASE_ID: {
    LENGTH: 32,
    PATTERN: /^[a-f0-9]{32}$/i,
  },
  PAGE_ID: {
    LENGTH: 32,
    PATTERN: /^[a-f0-9]{32}$/i,
  },
  DEBUG_LEVELS: [0, 1, 2, 3, 4] as const,
  SYNC_SCHEDULES: [
    'manual',
    'hourly', 
    'twicedaily',
    'daily',
    'weekly',
    'biweekly',
    'monthly'
  ] as const,
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ] as const,
} as const

/**
 * 自定义验证消息
 */
export const VALIDATION_MESSAGES = {
  API_KEY: {
    REQUIRED: 'API密钥不能为空',
    LENGTH: `API密钥长度必须在${VALIDATION_RULES.API_KEY.MIN_LENGTH}到${VALIDATION_RULES.API_KEY.MAX_LENGTH}个字符之间`,
    FORMAT: 'API密钥只能包含字母、数字、下划线和连字符',
  },
  DATABASE_ID: {
    REQUIRED: '数据库ID不能为空',
    LENGTH: `数据库ID必须是${VALIDATION_RULES.DATABASE_ID.LENGTH}位十六进制字符串`,
    FORMAT: '数据库ID格式不正确，应为32位十六进制字符串',
  },
  PAGE_ID: {
    REQUIRED: 'Page ID不能为空',
    LENGTH: `Page ID必须是${VALIDATION_RULES.PAGE_ID.LENGTH}位十六进制字符串`,
    FORMAT: 'Page ID格式不正确，应为32位十六进制字符串',
  },
  DEBUG_LEVEL: {
    INVALID: '调试级别无效，必须在0-4之间',
  },
  SYNC_SCHEDULE: {
    INVALID: '同步计划选项无效',
  },
  IFRAME_WHITELIST: {
    INVALID_DOMAIN: '域名格式可能不正确',
  },
  IMAGE_TYPES: {
    UNSUPPORTED: '图片类型可能不支持',
  },
} as const

/**
 * Notion API Key验证模式
 */
export const notionApiKeySchema = z
  .string()
  .min(1, VALIDATION_MESSAGES.API_KEY.REQUIRED)
  .min(VALIDATION_RULES.API_KEY.MIN_LENGTH, VALIDATION_MESSAGES.API_KEY.LENGTH)
  .max(VALIDATION_RULES.API_KEY.MAX_LENGTH, VALIDATION_MESSAGES.API_KEY.LENGTH)
  .regex(VALIDATION_RULES.API_KEY.PATTERN, VALIDATION_MESSAGES.API_KEY.FORMAT)

/**
 * Database ID验证模式
 */
export const databaseIdSchema = z
  .string()
  .min(1, VALIDATION_MESSAGES.DATABASE_ID.REQUIRED)
  .transform((val) => val.replace(/-/g, '')) // 移除连字符
  .refine(
    (val) => val.length === VALIDATION_RULES.DATABASE_ID.LENGTH,
    VALIDATION_MESSAGES.DATABASE_ID.LENGTH
  )
  .refine(
    (val) => VALIDATION_RULES.DATABASE_ID.PATTERN.test(val),
    VALIDATION_MESSAGES.DATABASE_ID.FORMAT
  )

/**
 * Page ID验证模式
 */
export const pageIdSchema = z
  .string()
  .min(1, VALIDATION_MESSAGES.PAGE_ID.REQUIRED)
  .transform((val) => val.replace(/-/g, '')) // 移除连字符
  .refine(
    (val) => val.length === VALIDATION_RULES.PAGE_ID.LENGTH,
    VALIDATION_MESSAGES.PAGE_ID.LENGTH
  )
  .refine(
    (val) => VALIDATION_RULES.PAGE_ID.PATTERN.test(val),
    VALIDATION_MESSAGES.PAGE_ID.FORMAT
  )

/**
 * 调试级别验证模式
 */
export const debugLevelSchema = z
  .number()
  .int()
  .refine(
    (val) => VALIDATION_RULES.DEBUG_LEVELS.includes(val as any),
    VALIDATION_MESSAGES.DEBUG_LEVEL.INVALID
  )

/**
 * 同步计划验证模式
 */
export const syncScheduleSchema = z.enum(VALIDATION_RULES.SYNC_SCHEDULES, {
  errorMap: () => ({ message: VALIDATION_MESSAGES.SYNC_SCHEDULE.INVALID })
})

/**
 * iframe白名单验证模式
 */
export const iframeWhitelistSchema = z
  .string()
  .optional()
  .refine((val) => {
    if (!val || val === '*') return true
    
    const domains = val.split(',').map(d => d.trim()).filter(Boolean)
    return domains.every(domain => {
      try {
        new URL(`http://${domain}`)
        return true
      } catch {
        return false
      }
    })
  }, VALIDATION_MESSAGES.IFRAME_WHITELIST.INVALID_DOMAIN)

/**
 * 图片类型验证模式
 */
export const imageTypesSchema = z
  .string()
  .optional()
  .refine((val) => {
    if (!val) return true
    
    const types = val.split(',').map(t => t.trim()).filter(Boolean)
    return types.every(type => 
      VALIDATION_RULES.ALLOWED_IMAGE_TYPES.includes(type as any)
    )
  }, VALIDATION_MESSAGES.IMAGE_TYPES.UNSUPPORTED)

/**
 * 插件设置表单验证模式
 */
export const pluginSettingsSchema = z.object({
  // 基础设置
  notion_api_key: notionApiKeySchema.optional(),
  notion_database_id: databaseIdSchema.optional(),
  
  // 同步设置
  sync_schedule: syncScheduleSchema.optional(),
  auto_sync_enabled: z.boolean().optional(),
  incremental_sync: z.boolean().optional(),
  check_deletions: z.boolean().optional(),
  
  // 内容设置
  post_status: z.enum(['draft', 'publish', 'private']).optional(),
  post_author: z.number().int().positive().optional(),
  post_category: z.number().int().positive().optional(),
  
  // 高级设置
  debug_level: debugLevelSchema.optional(),
  enable_logging: z.boolean().optional(),
  log_retention_days: z.number().int().min(1).max(365).optional(),
  
  // 安全设置
  iframe_whitelist: iframeWhitelistSchema,
  allowed_image_types: imageTypesSchema,
  
  // 性能设置
  batch_size: z.number().int().min(1).max(100).optional(),
  request_timeout: z.number().int().min(5).max(300).optional(),
  max_retries: z.number().int().min(0).max(10).optional(),
  
  // Webhook设置
  webhook_enabled: z.boolean().optional(),
  webhook_url: z.string().url().optional(),
  webhook_secret: z.string().min(8).optional(),
})

/**
 * 同步操作验证模式
 */
export const syncOperationSchema = z.object({
  type: z.enum(['manual', 'scheduled', 'webhook']),
  incremental: z.boolean().optional(),
  check_deletions: z.boolean().optional(),
  force: z.boolean().optional(),
})

/**
 * 日志查询验证模式
 */
export const logQuerySchema = z.object({
  level: z.enum(['debug', 'info', 'warning', 'error']).optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

/**
 * 导出所有验证模式的类型
 */
export type PluginSettingsFormData = z.infer<typeof pluginSettingsSchema>
export type SyncOperationData = z.infer<typeof syncOperationSchema>
export type LogQueryData = z.infer<typeof logQuerySchema>

/**
 * 验证结果类型
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * 字段验证结果类型
 */
export interface FieldValidationResult {
  isValid: boolean
  message: string
  level: 'success' | 'warning' | 'error'
}
