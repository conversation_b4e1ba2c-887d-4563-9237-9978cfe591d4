/**
 * 通知组件 - 现代化的Toast通知系统
 * 
 * 替代jQuery的showModal函数，提供更好的用户体验
 * 支持多种类型、自动消失、手动关闭等功能
 */

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '../../utils/cn';
import { useUIStore } from '../../stores/uiStore';
import type { Notification as NotificationType } from '../../stores/uiStore';

// 通知项组件
interface NotificationItemProps {
  notification: NotificationType;
  onClose: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  // 进入动画
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // 自动消失
  useEffect(() => {
    if (notification.duration && notification.duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, notification.duration);
      return () => clearTimeout(timer);
    }
  }, [notification.duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(notification.id);
    }, 300); // 等待退出动画完成
  };

  // 图标映射
  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  // 样式映射
  const getStyles = () => {
    const baseStyles = 'notion-wp-toast flex items-start gap-3 p-4 rounded-lg shadow-lg border max-w-md transition-all duration-300 transform';
    
    const typeStyles = {
      success: 'bg-green-50 border-green-200 text-green-800',
      error: 'bg-red-50 border-red-200 text-red-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      info: 'bg-blue-50 border-blue-200 text-blue-800',
    };

    const animationStyles = isLeaving 
      ? 'opacity-0 translate-x-full scale-95'
      : isVisible 
        ? 'opacity-100 translate-x-0 scale-100'
        : 'opacity-0 translate-x-full scale-95';

    return cn(baseStyles, typeStyles[notification.type], animationStyles);
  };

  return (
    <div className={getStyles()}>
      {/* 图标 */}
      <div className="notion-wp-toast-icon flex-shrink-0 text-lg">
        {getIcon()}
      </div>

      {/* 内容 */}
      <div className="notion-wp-toast-content flex-1 min-w-0">
        {notification.title && (
          <div className="font-semibold text-sm mb-1">
            {notification.title}
          </div>
        )}
        <div className="text-sm">
          {notification.message}
        </div>
      </div>

      {/* 关闭按钮 */}
      <button
        onClick={handleClose}
        className="notion-wp-toast-close flex-shrink-0 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
        aria-label="关闭通知"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
};

// 通知容器组件
export const NotificationContainer: React.FC = () => {
  const { notifications, hideNotification } = useUIStore();

  // 如果没有通知，不渲染容器
  if (notifications.length === 0) {
    return null;
  }

  // 创建Portal到body
  return createPortal(
    <div className="notion-wp-notifications fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onClose={hideNotification}
        />
      ))}
    </div>,
    document.body
  );
};

// 通知Hook - 提供便捷的通知方法
export const useNotification = () => {
  const { showSuccess, showError, showWarning, showInfo, hideNotification, clearNotifications } = useUIStore();

  return {
    success: showSuccess,
    error: showError,
    warning: showWarning,
    info: showInfo,
    hide: hideNotification,
    clear: clearNotifications,
  };
};

// 全局通知函数 - 兼容jQuery的showModal
export const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', title?: string) => {
  const uiStore = useUIStore.getState();
  
  switch (type) {
    case 'success':
      return uiStore.showSuccess(title || '成功', message);
    case 'error':
      return uiStore.showError(title || '错误', message);
    case 'warning':
      return uiStore.showWarning(title || '警告', message);
    case 'info':
    default:
      return uiStore.showInfo(title || '信息', message);
  }
};

// 替代jQuery showModal的全局函数
if (typeof window !== 'undefined') {
  (window as any).showNotification = showNotification;
  
  // 保持向后兼容
  (window as any).showModal = (message: string, status: string) => {
    const typeMap: Record<string, 'success' | 'error' | 'warning' | 'info'> = {
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
    };
    
    const type = typeMap[status] || 'info';
    return showNotification(message, type);
  };
}

export default NotificationContainer;
