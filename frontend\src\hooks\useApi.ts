/**
 * React Query API Hooks
 * 
 * 基于现有ApiService的React Query封装
 * 提供类型安全的API调用和状态管理
 * 
 * @since 2.0.0
 */

import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
  QueryKey,
} from '@tanstack/react-query'
import { getApiService } from '@/services/api'
import type {
  BaseResponse,
  SyncRequest,
  SyncResponse,
  SyncStatus,
  StatsData,
  SettingsData,
  ConnectionTestRequest,
  ConnectionTestResponse,
  LogsRequest,
  LogsResponse,
  LogFilesResponse,
  ClearLogsResponse,
  SystemInfo,
  IndexStatus,
  QueueStatus,
  SmartRecommendation,
} from '@/types'

// ==================== Query Keys ====================

/**
 * 查询键常量
 * 用于缓存管理和失效控制
 */
export const queryKeys = {
  // 同步相关
  syncStatus: ['sync', 'status'] as const,
  
  // 统计数据
  stats: ['stats'] as const,
  performanceMetrics: ['stats', 'performance'] as const,
  
  // 设置
  settings: ['settings'] as const,
  
  // 日志
  logs: (params?: LogsRequest) => ['logs', params] as const,
  logFiles: ['logs', 'files'] as const,
  
  // 系统信息
  systemInfo: ['system', 'info'] as const,
  
  // 数据库
  indexStatus: ['database', 'indexes'] as const,
  
  // 队列
  queueStatus: ['queue', 'status'] as const,
  
  // 智能推荐
  smartRecommendations: ['recommendations'] as const,
} as const

// ==================== Query Hooks ====================

/**
 * 获取同步状态
 */
export const useSyncStatus = (
  options?: Omit<UseQueryOptions<SyncStatus, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.syncStatus,
    queryFn: () => apiService.getSyncStatus(),
    // 同步状态需要频繁更新
    staleTime: 30 * 1000, // 30秒
    refetchInterval: 5 * 1000, // 5秒轮询
    ...options,
  })
}

/**
 * 获取统计数据
 */
export const useStats = (
  options?: Omit<UseQueryOptions<StatsData, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.stats,
    queryFn: () => apiService.getStats(),
    staleTime: 2 * 60 * 1000, // 2分钟
    ...options,
  })
}

/**
 * 获取设置数据
 */
export const useSettings = (
  options?: Omit<UseQueryOptions<SettingsData, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.settings,
    queryFn: () => apiService.getSettings(),
    staleTime: 5 * 60 * 1000, // 5分钟
    ...options,
  })
}

/**
 * 获取日志数据
 */
export const useLogs = (
  params?: LogsRequest,
  options?: Omit<UseQueryOptions<LogsResponse, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.logs(params),
    queryFn: () => apiService.getLogs(params),
    staleTime: 1 * 60 * 1000, // 1分钟
    ...options,
  })
}

/**
 * 获取日志文件列表
 */
export const useLogFiles = (
  options?: Omit<UseQueryOptions<LogFilesResponse, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.logFiles,
    queryFn: () => apiService.getLogFiles(),
    staleTime: 2 * 60 * 1000, // 2分钟
    ...options,
  })
}

/**
 * 获取系统信息
 */
export const useSystemInfo = (
  options?: Omit<UseQueryOptions<SystemInfo, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.systemInfo,
    queryFn: () => apiService.getSystemInfo(),
    staleTime: 10 * 60 * 1000, // 10分钟
    ...options,
  })
}

/**
 * 获取索引状态
 */
export const useIndexStatus = (
  options?: Omit<UseQueryOptions<IndexStatus, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.indexStatus,
    queryFn: () => apiService.getIndexStatus(),
    staleTime: 5 * 60 * 1000, // 5分钟
    ...options,
  })
}

/**
 * 获取队列状态
 */
export const useQueueStatus = (
  options?: Omit<UseQueryOptions<QueueStatus, Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()
  
  return useQuery({
    queryKey: queryKeys.queueStatus,
    queryFn: () => apiService.getQueueStatus(),
    staleTime: 30 * 1000, // 30秒
    refetchInterval: 10 * 1000, // 10秒轮询
    ...options,
  })
}

/**
 * 获取智能推荐
 */
export const useSmartRecommendations = (
  options?: Omit<UseQueryOptions<SmartRecommendation[], Error>, 'queryKey' | 'queryFn'>
) => {
  const apiService = getApiService()

  return useQuery({
    queryKey: queryKeys.smartRecommendations,
    queryFn: () => apiService.getSmartRecommendations(),
    staleTime: 10 * 60 * 1000, // 10分钟
    ...options,
  })
}

// ==================== Mutation Hooks ====================

/**
 * 开始手动同步
 */
export const useStartSync = (
  options?: UseMutationOptions<SyncResponse, Error, SyncRequest>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: SyncRequest) => apiService.startSync(request),
    onSuccess: () => {
      // 同步开始后，立即刷新同步状态
      queryClient.invalidateQueries({ queryKey: queryKeys.syncStatus })
      queryClient.invalidateQueries({ queryKey: queryKeys.queueStatus })
    },
    ...options,
  })
}

/**
 * 取消同步
 */
export const useCancelSync = (
  options?: UseMutationOptions<BaseResponse, Error, string | undefined>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (taskId?: string) => apiService.cancelSync(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.syncStatus })
      queryClient.invalidateQueries({ queryKey: queryKeys.queueStatus })
    },
    ...options,
  })
}

/**
 * 重试失败任务
 */
export const useRetryFailed = (
  options?: UseMutationOptions<BaseResponse, Error, void>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => apiService.retryFailed(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.syncStatus })
      queryClient.invalidateQueries({ queryKey: queryKeys.queueStatus })
    },
    ...options,
  })
}

/**
 * 测试连接
 */
export const useTestConnection = (
  options?: UseMutationOptions<ConnectionTestResponse, Error, ConnectionTestRequest>
) => {
  const apiService = getApiService()

  return useMutation({
    mutationFn: (request: ConnectionTestRequest) => apiService.testConnection(request),
    ...options,
  })
}

/**
 * 保存设置
 */
export const useSaveSettings = (
  options?: UseMutationOptions<BaseResponse, Error, Partial<SettingsData>>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (settings: Partial<SettingsData>) => apiService.saveSettings(settings),
    onSuccess: () => {
      // 设置保存后，刷新设置数据
      queryClient.invalidateQueries({ queryKey: queryKeys.settings })
    },
    ...options,
  })
}

/**
 * 清除所有日志
 */
export const useClearAllLogs = (
  options?: UseMutationOptions<ClearLogsResponse, Error, void>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => apiService.clearAllLogs(),
    onSuccess: () => {
      // 清除日志后，刷新日志相关数据
      queryClient.invalidateQueries({ queryKey: ['logs'] })
    },
    ...options,
  })
}

/**
 * 刷新性能统计
 */
export const useRefreshPerformanceStats = (
  options?: UseMutationOptions<BaseResponse, Error, void>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => apiService.refreshPerformanceStats(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.stats })
      queryClient.invalidateQueries({ queryKey: queryKeys.performanceMetrics })
    },
    ...options,
  })
}

/**
 * 优化数据库
 */
export const useOptimizeDatabase = (
  options?: UseMutationOptions<BaseResponse, Error, void>
) => {
  const apiService = getApiService()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => apiService.optimizeDatabase(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.indexStatus })
      queryClient.invalidateQueries({ queryKey: queryKeys.stats })
    },
    ...options,
  })
}
