import React, { useState, useEffect, useRef } from 'react'
import {
  Bars3Icon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { useResponsive, useResponsiveClasses } from '../../hooks/useResponsive'
import { useI18n } from '../../hooks/useI18n'
import type { TabType } from './AdminLayout'

interface SidebarProps {
  activeTab: string
  onTabChange: (tabId: string) => void
  tabs: TabType[]
}

export const Sidebar: React.FC<SidebarProps> = ({
  activeTab,
  onTabChange,
  tabs
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { isMobile, isTablet, isDesktop } = useResponsive()
  const { allClasses } = useResponsiveClasses()
  const { __ } = useI18n()
  const sidebarRef = useRef<HTMLDivElement>(null)

  // 移动端自动关闭菜单
  useEffect(() => {
    if (isMobile && isMobileMenuOpen) {
      setIsMobileMenuOpen(false)
    }
  }, [activeTab, isMobile])

  // 点击外部关闭移动端菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobile &&
        isMobileMenuOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isMobile, isMobileMenuOpen])

  // 处理标签页切换
  const handleTabChange = (tabId: string) => {
    onTabChange(tabId)
    if (isMobile) {
      setIsMobileMenuOpen(false)
    }
  }

  // 移动端菜单切换
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  // 桌面端侧边栏折叠
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed)
  }

  // 移动端渲染
  if (isMobile) {
    return (
      <>
        {/* 移动端菜单按钮 */}
        <div className="notion-wp-mobile-header">
          <button
            className="notion-wp-mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label={__('Toggle menu')}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          <div className="notion-wp-mobile-title">
            {tabs.find(tab => tab.id === activeTab)?.label || __('Notion to WordPress')}
          </div>
        </div>

        {/* 移动端侧边栏覆盖层 */}
        {isMobileMenuOpen && (
          <div className="notion-wp-mobile-overlay" onClick={() => setIsMobileMenuOpen(false)} />
        )}

        {/* 移动端侧边栏 */}
        <div
          ref={sidebarRef}
          className={`notion-wp-sidebar notion-wp-sidebar-mobile ${allClasses} ${
            isMobileMenuOpen ? 'open' : ''
          }`}
        >
          <div className="notion-wp-sidebar-header">
            <h2 className="notion-wp-sidebar-title">{__('Menu')}</h2>
            <button
              className="notion-wp-sidebar-close"
              onClick={() => setIsMobileMenuOpen(false)}
              aria-label={__('Close menu')}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="notion-wp-menu notion-wp-menu-mobile">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`notion-wp-menu-item notion-wp-menu-item-mobile ${
                  activeTab === tab.id ? 'active' : ''
                }`}
                onClick={() => handleTabChange(tab.id)}
              >
                <span className="notion-wp-menu-icon">{tab.icon}</span>
                <span className="notion-wp-menu-text">{tab.label.replace(tab.icon, '').trim()}</span>
              </button>
            ))}
          </div>
        </div>
      </>
    )
  }

  // 桌面端和平板端渲染
  return (
    <div
      className={`notion-wp-sidebar ${allClasses} ${
        isCollapsed ? 'collapsed' : ''
      } ${isTablet ? 'tablet' : ''}`}
    >
      {/* 桌面端折叠按钮 */}
      {isDesktop && (
        <button
          className="notion-wp-sidebar-toggle"
          onClick={toggleCollapse}
          aria-label={isCollapsed ? __('Expand sidebar') : __('Collapse sidebar')}
        >
          {isCollapsed ? (
            <ChevronRightIcon className="h-5 w-5" />
          ) : (
            <ChevronLeftIcon className="h-5 w-5" />
          )}
        </button>
      )}

      <div className="notion-wp-menu">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`notion-wp-menu-item ${activeTab === tab.id ? 'active' : ''} ${
              isCollapsed ? 'collapsed' : ''
            }`}
            onClick={() => handleTabChange(tab.id)}
            title={isCollapsed ? tab.label : undefined}
          >
            {isCollapsed ? (
              <span className="notion-wp-menu-icon-only">{tab.icon}</span>
            ) : (
              <>
                <span className="notion-wp-menu-icon">{tab.icon}</span>
                <span className="notion-wp-menu-text">{tab.label.replace(tab.icon, '').trim()}</span>
              </>
            )}
          </button>
        ))}
      </div>
    </div>
  )
}