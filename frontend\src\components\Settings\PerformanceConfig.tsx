import React from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Button } from '../Common'

export const PerformanceConfig: React.FC = () => {
  const {
    settings,
    updateSettings,
    saveSettings,
    hasUnsavedChanges,
    isSaving
  } = useSettingsStore()

  // 使用原版的配置字段名
  const performanceConfig = settings || {
    api_page_size: 100,
    concurrent_requests: 5,
    batch_size: 20,
    log_buffer_size: 50,
    enable_performance_mode: true,
    enable_asset_compression: true,
    enhanced_lazy_loading: true,
    performance_monitoring: true,
    enable_cdn: false,
    cdn_provider: 'jsdelivr',
    custom_cdn_url: ''
  }

  const handleConfigChange = (field: string, value: string | number | boolean) => {
    updateSettings({
      [field]: value
    })
  }

  const handleSave = async () => {
    const success = await saveSettings()
    if (success) {
      console.log('性能配置保存成功')
    }
  }

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          性能配置
        </h2>
        <p className="text-sm text-gray-600">
          这里是高级性能选项的配置页面。请根据您的服务器配置和网络环境进行设置，以获得最佳的同步性能。
        </p>
      </div>

      <div className="space-y-6">
        <div className="notion-wp-form-table">
          <div className="notion-wp-form-row">
            <label className="notion-wp-label">API分页大小</label>
            <div className="notion-wp-form-field">
              <input
                type="number"
                className="notion-wp-input small-text"
                value={performanceConfig.api_page_size || 100}
                onChange={(e) => handleConfigChange('api_page_size', parseInt(e.target.value) || 100)}
                min="50"
                max="200"
                step="10"
              />
              <p className="notion-wp-description">
                每次API请求获取的页面数量。较大的值可以减少API调用次数，但会增加单次请求的处理时间。推荐值：100-200。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">并发请求数</label>
            <div className="notion-wp-form-field">
              <input
                type="number"
                className="notion-wp-input small-text"
                value={performanceConfig.concurrent_requests || 5}
                onChange={(e) => handleConfigChange('concurrent_requests', parseInt(e.target.value) || 5)}
                min="3"
                max="15"
                step="1"
              />
              <p className="notion-wp-description">
                基准并发请求数。系统会根据服务器性能（CPU负载、内存使用、网络延迟）动态调整实际并发数，以获得最佳性能。推荐值：5-10。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">批量处理大小</label>
            <div className="notion-wp-form-field">
              <input
                type="number"
                className="notion-wp-input small-text"
                value={performanceConfig.batch_size || 20}
                onChange={(e) => handleConfigChange('batch_size', parseInt(e.target.value) || 20)}
                min="10"
                max="100"
                step="5"
              />
              <p className="notion-wp-description">
                批量处理页面的数量。较大的值可以提升数据库操作效率，但会增加内存使用。推荐值：20-50。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">日志缓冲大小</label>
            <div className="notion-wp-form-field">
              <input
                type="number"
                className="notion-wp-input small-text"
                value={performanceConfig.log_buffer_size || 50}
                onChange={(e) => handleConfigChange('log_buffer_size', parseInt(e.target.value) || 50)}
                min="10"
                max="200"
                step="10"
              />
              <p className="notion-wp-description">
                日志批量写入的缓冲区大小。较大的值可以减少磁盘I/O，但会增加内存使用。推荐值：50-100。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">性能优化模式</label>
            <div className="notion-wp-form-field">
              <label className="notion-wp-checkbox-label">
                <input
                  type="checkbox"
                  className="notion-wp-checkbox"
                  checked={performanceConfig.enable_performance_mode || false}
                  onChange={(e) => handleConfigChange('enable_performance_mode', e.target.checked)}
                />
                启用性能优化模式
              </label>
              <p className="notion-wp-description">
                启用后将使用批量操作、动态并发调优等功能来提升同步性能。系统会自动根据服务器负载调整并发数。日志记录由调试工具中的日志等级单独控制。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">前端资源优化</label>
            <div className="notion-wp-form-field">
              <div className="space-y-2">
                <label className="notion-wp-checkbox-label">
                  <input
                    type="checkbox"
                    className="notion-wp-checkbox"
                    checked={performanceConfig.enable_asset_compression || false}
                    onChange={(e) => handleConfigChange('enable_asset_compression', e.target.checked)}
                  />
                  启用资源压缩
                </label>
                <br />
                <label className="notion-wp-checkbox-label">
                  <input
                    type="checkbox"
                    className="notion-wp-checkbox"
                    checked={performanceConfig.enhanced_lazy_loading || false}
                    onChange={(e) => handleConfigChange('enhanced_lazy_loading', e.target.checked)}
                  />
                  增强懒加载
                </label>
                <br />
                <label className="notion-wp-checkbox-label">
                  <input
                    type="checkbox"
                    className="notion-wp-checkbox"
                    checked={performanceConfig.performance_monitoring || false}
                    onChange={(e) => handleConfigChange('performance_monitoring', e.target.checked)}
                  />
                  性能监控
                </label>
              </div>
              <p className="notion-wp-description">
                启用前端资源优化可以提升页面加载速度20-40%。包括JavaScript/CSS压缩、增强懒加载和性能监控。
              </p>
            </div>
          </div>

          <div className="notion-wp-form-row">
            <label className="notion-wp-label">CDN配置</label>
            <div className="notion-wp-form-field">
              <label className="notion-wp-checkbox-label">
                <input
                  type="checkbox"
                  className="notion-wp-checkbox"
                  checked={performanceConfig.enable_cdn || false}
                  onChange={(e) => handleConfigChange('enable_cdn', e.target.checked)}
                />
                启用CDN加速
              </label>
              <br /><br />
              <select
                className="notion-wp-select"
                value={performanceConfig.cdn_provider || 'jsdelivr'}
                onChange={(e) => handleConfigChange('cdn_provider', e.target.value)}
              >
                <option value="jsdelivr">jsDelivr</option>
                <option value="unpkg">UNPKG</option>
                <option value="cdnjs">CDNJS</option>
                <option value="custom">自定义</option>
              </select>
              <br /><br />
              {(performanceConfig.cdn_provider || 'jsdelivr') === 'custom' && (
                <input
                  type="url"
                  className="notion-wp-input regular-text"
                  value={performanceConfig.custom_cdn_url || ''}
                  onChange={(e) => handleConfigChange('custom_cdn_url', e.target.value)}
                  placeholder="https://your-cdn.com"
                />
              )}
              <p className="notion-wp-description">
                CDN可以加速静态资源加载。选择合适的CDN提供商或配置自定义CDN地址。
              </p>
            </div>
          </div>
        </div>
      </div>

      {hasUnsavedChanges && (
        <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">您有未保存的更改</p>
          <Button
            variant="primary"
            onClick={handleSave}
            loading={isSaving}
            disabled={isSaving}
          >
            {isSaving ? '保存中...' : '保存配置'}
          </Button>
        </div>
      )}
    </div>
  )
}