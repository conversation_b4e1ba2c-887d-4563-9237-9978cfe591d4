/**
 * 核心数据类型定义
 * 
 * @since 2.0.0
 */

// ==================== 基础类型 ====================

/**
 * 严格的API响应基础类型
 * 使用泛型确保类型安全
 */
export interface StrictApiResponse<TData = unknown> {
  readonly success: boolean;
  readonly data: TData;
  readonly message?: string;
  readonly timestamp?: string;
  readonly requestId?: string;
}

/**
 * 错误响应类型
 * 确保错误信息的结构化
 */
export interface ErrorResponse {
  readonly success: false;
  readonly data: null;
  readonly message: string;
  readonly errors?: readonly ValidationError[];
  readonly code?: string;
  readonly timestamp: string;
}

/**
 * 成功响应类型
 * 泛型确保数据类型安全
 */
export interface SuccessResponse<TData> {
  readonly success: true;
  readonly data: TData;
  readonly message?: string;
  readonly timestamp: string;
}

/**
 * 验证错误类型
 */
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value?: unknown;
}

/**
 * 分页响应类型
 */
export interface PaginatedResponse<TData> extends SuccessResponse<TData> {
  readonly pagination: {
    readonly page: number;
    readonly pageSize: number;
    readonly total: number;
    readonly totalPages: number;
    readonly hasNext: boolean;
    readonly hasPrev: boolean;
  };
}

/**
 * 向后兼容的基础响应类型
 * @deprecated 使用 StrictApiResponse 替代
 */
export interface BaseResponse {
  success: boolean;
  data?: any;
  message?: string;
}

// ==================== 同步相关类型 ====================

/**
 * 同步类型枚举
 */
export const SYNC_TYPES = {
  SMART: 'smart',
  FULL: 'full',
  INCREMENTAL: 'incremental'
} as const;

export type SyncType = typeof SYNC_TYPES[keyof typeof SYNC_TYPES];

/**
 * 同步状态枚举
 */
export const SYNC_STATUS = {
  IDLE: 'idle',
  STARTED: 'started',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

export type SyncStatus = typeof SYNC_STATUS[keyof typeof SYNC_STATUS];

/**
 * 严格的同步请求类型
 */
export interface StrictSyncRequest {
  readonly taskId?: string;
  readonly type: SyncType;
  readonly options: {
    readonly checkDeletions: boolean;
    readonly incremental: boolean;
    readonly forceRefresh: boolean;
    readonly batchSize?: number;
    readonly concurrentRequests?: number;
  };
  readonly filters?: {
    readonly pageIds?: readonly string[];
    readonly dateRange?: {
      readonly from: string;
      readonly to: string;
    };
    readonly tags?: readonly string[];
  };
}

/**
 * 同步响应数据类型
 */
export interface SyncResponseData {
  readonly taskId: string;
  readonly status: SyncStatus;
  readonly stats: SyncStats;
  readonly startTime: string;
  readonly endTime?: string;
  readonly duration?: number;
  readonly errors?: readonly SyncError[];
}

/**
 * 严格的同步响应类型
 */
export type StrictSyncResponse = StrictApiResponse<SyncResponseData>;

/**
 * 向后兼容的同步请求类型
 * @deprecated 使用 StrictSyncRequest 替代
 */
export interface SyncRequest {
  task_id?: string;
  type?: 'smart' | 'full';
  check_deletions?: boolean;
  incremental?: boolean;
  force_refresh?: boolean;
}

/**
 * 向后兼容的同步响应类型
 * @deprecated 使用 StrictSyncResponse 替代
 */
export interface SyncResponse extends BaseResponse {
  data?: {
    taskId: string;
    task_id: string;
    status: 'started' | 'running' | 'completed' | 'failed';
    stats?: SyncStats;
  };
}

/**
 * 同步错误类型
 */
export interface SyncError {
  readonly code: string;
  readonly message: string;
  readonly pageId?: string;
  readonly pageTitle?: string;
  readonly timestamp: string;
  readonly details?: Record<string, unknown>;
}

/**
 * 严格的同步统计类型
 */
export interface StrictSyncStats {
  readonly totalPages: number;
  readonly importedPages: number;
  readonly updatedPages: number;
  readonly failedPages: number;
  readonly skippedPages: number;
  readonly deletedPages: number;
  readonly executionTime: number;
  readonly memoryUsage: {
    readonly peak: number;
    readonly current: number;
    readonly average: number;
  };
  readonly apiCalls: {
    readonly total: number;
    readonly successful: number;
    readonly failed: number;
    readonly averageResponseTime: number;
  };
  readonly lastSyncTime: string;
  readonly performance: {
    readonly pagesPerSecond: number;
    readonly averagePageSize: number;
    readonly totalDataTransferred: number;
  };
}

/**
 * 同步状态详情类型
 */
export const SYNC_STEP_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SKIPPED: 'skipped'
} as const;

export type SyncStepStatus = typeof SYNC_STEP_STATUS[keyof typeof SYNC_STEP_STATUS];

/**
 * 严格的同步状态详情类型
 */
export interface StrictSyncStatusDetail {
  readonly step: string;
  readonly status: SyncStepStatus;
  readonly message: string;
  readonly timestamp: string;
  readonly progress: number;
  readonly duration?: number;
  readonly metadata?: Record<string, unknown>;
}

/**
 * 严格的同步状态类型
 */
export interface StrictSyncStatus {
  readonly status: SyncStatus;
  readonly operation: string;
  readonly startedAt: string;
  readonly updatedAt: string;
  readonly dataCount: number;
  readonly progress: number;
  readonly details: readonly StrictSyncStatusDetail[];
  readonly estimatedTimeRemaining?: number;
  readonly currentStep?: string;
}

/**
 * 向后兼容的同步统计类型
 * @deprecated 使用 StrictSyncStats 替代
 */
export interface SyncStats {
  total_pages: number;
  imported_pages: number;
  updated_pages: number;
  failed_pages: number;
  execution_time: number;
  memory_usage: number;
  last_sync_time?: string;
}

/**
 * 向后兼容的同步状态类型
 * @deprecated 使用 SyncStatus 枚举替代
 */
export type SyncStatusType = 'idle' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';

/**
 * 向后兼容的同步状态接口
 * @deprecated 使用 StrictSyncStatus 替代
 */
export interface SyncStatus {
  status: SyncStatusType;
  operation: string;
  started_at: string;
  updated_at: string;
  data_count: number;
  progress: number;
  details: SyncStatusDetail[];
}

/**
 * 向后兼容的同步状态详情
 * @deprecated 使用 StrictSyncStatusDetail 替代
 */
export interface SyncStatusDetail {
  step: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  message: string;
  timestamp: string;
  progress?: number;
}

// ==================== 设置相关类型 ====================

export interface SettingsData {
  api_key: string;
  database_id: string;
  post_type: string;
  post_status: string;
  author_id: number;
  enable_auto_sync: boolean;
  sync_interval: number;
  sync_schedule?: string;
  auto_sync_enabled?: boolean;
  enable_webhook: boolean;
  webhook_url?: string;
  webhook_verify_token?: string;
  field_mapping: FieldMapping;
  custom_field_mapping: CustomFieldMapping[];
  performance_config: PerformanceConfig;
  language_settings: LanguageSettings;
  enable_debug: boolean;
  log_level: string;
  // 其他设置项
  delete_on_uninstall?: boolean;
  iframe_whitelist?: string;
  allowed_image_types?: string;
  max_image_size?: number;
  plugin_language?: string;
  // 性能配置项
  api_page_size?: number;
  concurrent_requests?: number;
  batch_size?: number;
  log_buffer_size?: number;
  enable_performance_mode?: boolean;
  enable_asset_compression?: boolean;
  enhanced_lazy_loading?: boolean;
  performance_monitoring?: boolean;
  enable_cdn?: boolean;
  cdn_provider?: string;
  custom_cdn_url?: string;
}

export interface FieldMapping {
  title_field: string;
  content_field: string;
  excerpt_field?: string;
  featured_image_field?: string;
  category_field?: string;
  tag_field?: string;
  status_field?: string;
  post_type_field?: string;
  date_field?: string;
  password_field?: string;
  custom_fields: CustomFieldMapping[];
}

export interface CustomFieldMapping {
  notion_field: string;
  wordpress_field: string;
  field_type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multi_select';
}

export interface PerformanceConfig {
  enable_cache: boolean;
  cache_duration: number;
  batch_size: number;
  max_execution_time: number;
  memory_limit: string;
  enable_async_processing: boolean;
  enable_image_optimization: boolean;
  max_retries: number;
  timeout: number;
  request_delay: number;
}

export interface LanguageSettings {
  default_language: string;
  enable_multilingual: boolean;
  language_mapping: Record<string, string>;
  fallback_language: string;
  auto_detect: boolean;
}

// ==================== 统计相关类型 ====================

export interface StatsData {
  imported_count: number;
  published_count: number;
  last_update: string;
  next_run: string;
  total_posts: number;
  status_breakdown: Record<string, number>;
  performance_metrics?: PerformanceMetrics;
  smart_cache?: CacheStats;
}

export interface PerformanceMetrics {
  enhanced_fetch_time: number;
  enhanced_fetch_count: number;
  enhanced_fetch_memory: number;
  memory_usage: number;
  peak_memory: number;
  execution_time: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  hit_ratio: number;
  cache_size: number;
  status: 'enabled' | 'disabled';
}

// ==================== 日志相关类型 ====================

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  context?: Record<string, any>;
}

export interface LogFilter {
  level: string;
  search: string;
  dateFrom: string;
  dateTo: string;
}

export interface LogsRequest {
  level?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  per_page?: number;
}

export interface LogsResponse {
  logs: LogEntry[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
  filters_applied: {
    level: string;
    search: string;
    date_from: string;
    date_to: string;
  };
}

export interface LogFileInfo {
  name: string;
  size: number;
  size_formatted: string;
  modified: number;
  modified_formatted: string;
  readable: boolean;
}

export interface LogFilesResponse {
  files: LogFileInfo[];
  total_files: number;
}

export interface ClearLogsResponse {
  cleared_count: number;
  files_before: number;
  files_after: number;
}

// 保持向后兼容的旧类型
export interface LogViewRequest {
  file_name: string;
  lines?: number;
  offset?: number;
}

export interface LogViewResponse {
  content: string;
  file_size: number;
  total_lines: number;
  file_name: string;
}

// ==================== 系统信息类型 ====================

export interface SystemInfo {
  php_version: string;
  wp_version: string;
  memory_limit: string;
  max_execution_time: string;
  plugin_version: string;
  current_time: string;
  options_exist: 'yes' | 'no';
  ajax_url: string;
}

// ==================== 连接测试类型 ====================

export interface ConnectionTestRequest {
  api_key: string;
  database_id: string;
}

export interface ConnectionTestResponse extends BaseResponse {
  data?: {
    connected: boolean;
    database_info?: {
      id: string;
      title: string;
      created_time: string;
      last_edited_time: string;
    };
  };
}

// ==================== 数据库优化类型 ====================

export interface IndexStatus {
  existing_indexes: DatabaseIndex[];
  missing_indexes: DatabaseIndex[];
  optimization_suggestions: OptimizationSuggestion[];
}

export interface DatabaseIndex {
  table: string;
  column: string;
  type: 'primary' | 'unique' | 'index' | 'fulltext';
  status: 'exists' | 'missing' | 'recommended';
}

export interface OptimizationSuggestion {
  type: 'index' | 'query' | 'configuration';
  priority: 'high' | 'medium' | 'low';
  description: string;
  action: string;
  estimated_impact: string;
}

// ==================== 队列管理类型 ====================

export interface QueueStatus {
  total_tasks: number;
  pending_tasks: number;
  running_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  queue_health: 'healthy' | 'warning' | 'critical';
}

export interface QueueTask {
  id: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  progress: number;
  data: any;
  error_message?: string;
}

// ==================== SSE事件类型 ====================

export interface SSEEvent {
  type: 'progress' | 'completed' | 'failed' | 'status_update' | 'status' | 'stats' | 'error' | 'complete';
  data: SSEEventData;
  timestamp: string;
}

export interface SSEEventData {
  task_id?: string;
  progress?: number;
  message?: string;
  status?: string;
  stats?: Partial<StatsData>;
  details?: any;
  error?: string;
}

// ==================== API端点类型 ====================

export interface APIEndpoints {
  manual_sync: string;
  test_connection: string;
  get_stats: string;
  get_settings: string;
  save_settings: string;
  view_log: string;
  clear_logs: string;
  get_system_info: string;
  sse_progress: string;
  get_index_status: string;
  create_database_indexes: string;
  get_async_status: string;
  get_queue_status: string;
  control_async_operation: string;
  cleanup_queue: string;
  cancel_queue_task: string;
  refresh_performance_stats: string;
  reset_performance_stats: string;
  cancel_sync: string;
  retry_failed: string;
  get_smart_recommendations: string;
}

// ==================== 用户信息类型 ====================

export interface CurrentUser {
  id: number;
  name: string;
  email: string;
}

// ==================== 验证相关类型 ====================

export interface ValidationResult {
  is_valid: boolean;
  isValid: boolean; // 兼容性别名
  message: string;
  error_message?: string;
  http_code?: number;
}

// ==================== 智能推荐类型 ====================

export interface SmartRecommendation {
  id: string;
  type: 'performance' | 'configuration' | 'maintenance';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  action_required: boolean;
  estimated_impact: string;
  implementation_steps: string[];
}

// ==================== 导出所有类型 ====================
// 注意：使用export interface而不是export type来避免冲突