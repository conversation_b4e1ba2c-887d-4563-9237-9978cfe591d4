/**
 * API设置功能E2E测试
 * 
 * 测试API密钥和数据库ID的设置、验证、保存等功能
 * 
 * @since 2.0.0
 */

import { test, expect } from '@playwright/test'
import { ApiSettingsPage } from '../page-objects/ApiSettingsPage'
import { loadTestData, generateTestData, setupConsoleErrorTracking } from '../fixtures/test-utils'

test.describe('API设置功能', () => {
  let apiSettingsPage: ApiSettingsPage
  let testData: ReturnType<typeof generateTestData>
  let consoleErrors: ReturnType<typeof setupConsoleErrorTracking>

  test.beforeEach(async ({ page }) => {
    // 设置控制台错误跟踪
    consoleErrors = setupConsoleErrorTracking(page)
    
    // 初始化页面对象
    apiSettingsPage = new ApiSettingsPage(page)
    
    // 生成测试数据
    testData = generateTestData()
    
    // 导航到API设置页面
    await apiSettingsPage.goto()
  })

  test.afterEach(async () => {
    // 检查控制台错误
    if (consoleErrors.hasErrors()) {
      console.warn('发现控制台错误:', consoleErrors.getErrors())
    }
  })

  test('应该正确显示API设置页面', async () => {
    // 验证页面基本元素
    await expect(apiSettingsPage.container).toBeVisible()
    await expect(apiSettingsPage.apiKeyInput).toBeVisible()
    await expect(apiSettingsPage.databaseIdInput).toBeVisible()
    await expect(apiSettingsPage.saveButton).toBeVisible()
    await expect(apiSettingsPage.testConnectionButton).toBeVisible()
    
    // 验证页面标题
    const title = await apiSettingsPage.getPageTitle()
    expect(title).toContain('Notion to WordPress')
    
    // 验证当前标签页
    const activeTab = await apiSettingsPage.getActiveTab()
    expect(activeTab).toBe('api-settings')
  })

  test('应该能够填写和保存API设置', async () => {
    // 填写API设置
    await apiSettingsPage.fillApiKey(testData.apiKey)
    await apiSettingsPage.fillDatabaseId(testData.databaseId)
    
    // 保存设置
    await apiSettingsPage.saveSettings()
    
    // 验证保存成功
    expect(await apiSettingsPage.hasSuccessNotification()).toBe(true)
    
    // 验证值已保存
    expect(await apiSettingsPage.getApiKeyValue()).toBe(testData.apiKey)
    expect(await apiSettingsPage.getDatabaseIdValue()).toBe(testData.databaseId)
  })

  test('应该验证API密钥格式', async () => {
    // 测试无效的API密钥格式
    const invalidApiKeys = [
      '', // 空值
      'invalid-key', // 无效格式
      'secret_', // 不完整
      'secret_' + 'a'.repeat(50), // 过长
      'wrong_' + 'a'.repeat(43) // 错误前缀
    ]

    for (const invalidKey of invalidApiKeys) {
      await apiSettingsPage.fillApiKey(invalidKey)
      await apiSettingsPage.fillDatabaseId(testData.databaseId)
      await apiSettingsPage.saveSettings()
      
      // 应该显示验证错误
      expect(await apiSettingsPage.hasValidationErrors()).toBe(true)
      
      // 清除通知
      await apiSettingsPage.clearNotifications()
    }
  })

  test('应该验证数据库ID格式', async () => {
    // 测试无效的数据库ID格式
    const invalidDatabaseIds = [
      '', // 空值
      'invalid-id', // 无效格式
      '123', // 太短
      'g'.repeat(32), // 包含无效字符
      'a'.repeat(50) // 过长
    ]

    for (const invalidId of invalidDatabaseIds) {
      await apiSettingsPage.fillApiKey(testData.apiKey)
      await apiSettingsPage.fillDatabaseId(invalidId)
      await apiSettingsPage.saveSettings()
      
      // 应该显示验证错误
      expect(await apiSettingsPage.hasValidationErrors()).toBe(true)
      
      // 清除通知
      await apiSettingsPage.clearNotifications()
    }
  })

  test('应该能够测试连接', async () => {
    // 使用有效的测试数据
    const validTestData = loadTestData()
    
    await apiSettingsPage.fillApiKey(validTestData.testCredentials.apiKey)
    await apiSettingsPage.fillDatabaseId(validTestData.testCredentials.databaseId)
    
    // 模拟成功的API响应
    await apiSettingsPage.page.route('**/notion.com/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          object: 'database',
          id: validTestData.testCredentials.databaseId,
          title: [{ plain_text: '测试数据库' }]
        })
      })
    })
    
    // 测试连接
    await apiSettingsPage.testConnection()
    
    // 验证连接状态
    const isSuccessful = await apiSettingsPage.isConnectionSuccessful()
    expect(isSuccessful).toBe(true)
  })

  test('应该处理连接失败', async () => {
    await apiSettingsPage.fillApiKey(testData.apiKey)
    await apiSettingsPage.fillDatabaseId(testData.databaseId)
    
    // 模拟失败的API响应
    await apiSettingsPage.page.route('**/notion.com/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          object: 'error',
          status: 401,
          code: 'unauthorized',
          message: 'API token is invalid.'
        })
      })
    })
    
    // 测试连接
    await apiSettingsPage.testConnection()
    
    // 验证错误处理
    expect(await apiSettingsPage.hasErrorNotification()).toBe(true)
    const status = await apiSettingsPage.getConnectionStatus()
    expect(status).toContain('失败')
  })

  test('应该支持高级设置', async () => {
    // 展开高级设置
    await apiSettingsPage.expandAdvancedSettings()
    
    // 验证高级设置元素可见
    await expect(apiSettingsPage.apiVersionSelect).toBeVisible()
    await expect(apiSettingsPage.timeoutInput).toBeVisible()
    await expect(apiSettingsPage.retryCountInput).toBeVisible()
    
    // 设置高级选项
    await apiSettingsPage.setApiVersion('2022-06-28')
    await apiSettingsPage.setTimeout(30)
    await apiSettingsPage.setRetryCount(3)
    
    // 保存设置
    await apiSettingsPage.saveSettings()
    
    // 验证保存成功
    expect(await apiSettingsPage.hasSuccessNotification()).toBe(true)
  })

  test('应该能够重置设置', async () => {
    // 先设置一些值
    await apiSettingsPage.fillApiKey(testData.apiKey)
    await apiSettingsPage.fillDatabaseId(testData.databaseId)
    await apiSettingsPage.saveSettings()
    
    // 重置设置
    await apiSettingsPage.resetSettings()
    
    // 验证值已清空
    expect(await apiSettingsPage.getApiKeyValue()).toBe('')
    expect(await apiSettingsPage.getDatabaseIdValue()).toBe('')
    
    // 验证重置成功通知
    expect(await apiSettingsPage.hasSuccessNotification()).toBe(true)
  })

  test('应该检查敏感信息遮蔽', async () => {
    await apiSettingsPage.checkSensitiveDataMasking()
  })

  test('应该支持响应式布局', async () => {
    // 测试桌面端布局
    await apiSettingsPage.page.setViewportSize({ width: 1280, height: 720 })
    await apiSettingsPage.checkResponsiveLayout()
    
    // 测试平板端布局
    await apiSettingsPage.page.setViewportSize({ width: 768, height: 1024 })
    await apiSettingsPage.checkResponsiveLayout()
    
    // 测试移动端布局
    await apiSettingsPage.page.setViewportSize({ width: 375, height: 667 })
    await apiSettingsPage.checkResponsiveLayout()
  })

  test('应该处理网络错误', async () => {
    await apiSettingsPage.fillApiKey(testData.apiKey)
    await apiSettingsPage.fillDatabaseId(testData.databaseId)
    
    // 模拟网络错误
    await apiSettingsPage.simulateNetworkError()
    
    // 尝试保存设置
    await apiSettingsPage.saveButton.click()
    
    // 应该显示网络错误
    expect(await apiSettingsPage.hasErrorNotification()).toBe(true)
    
    // 恢复网络
    await apiSettingsPage.restoreNetwork()
  })

  test('应该检查页面性能', async () => {
    const metrics = await apiSettingsPage.checkPerformance()
    
    // 验证性能指标
    expect(metrics.totalLoadTime).toBeLessThan(10000)
    expect(metrics.domContentLoaded).toBeLessThan(5000)
  })

  test('应该支持无障碍访问', async () => {
    await apiSettingsPage.checkAccessibility()
  })

  test('应该处理并发操作', async () => {
    // 同时执行多个操作
    const promises = [
      apiSettingsPage.fillApiKey(testData.apiKey),
      apiSettingsPage.fillDatabaseId(testData.databaseId),
      apiSettingsPage.expandAdvancedSettings()
    ]
    
    await Promise.all(promises)
    
    // 验证所有操作都成功
    expect(await apiSettingsPage.getApiKeyValue()).toBe(testData.apiKey)
    expect(await apiSettingsPage.getDatabaseIdValue()).toBe(testData.databaseId)
    await expect(apiSettingsPage.apiVersionSelect).toBeVisible()
  })
})
