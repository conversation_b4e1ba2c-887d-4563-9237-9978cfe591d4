/**
 * 插件设置表单组件
 * 
 * 使用React Hook Form + Zod的现代化表单处理
 * 
 * @since 2.0.0
 */

import React from 'react'
import { FormField } from '../Common/Form/FormField'
import { TextInput, PasswordInput, Select, Checkbox, RadioGroup } from '../Common/Form/FormInput'
import { usePluginSettingsForm, useFieldValidation } from '../../hooks/useForm'
import { VALIDATION_RULES } from '../../schemas/validation'

/**
 * 基础设置部分
 */
const BasicSettings: React.FC = () => {
  const { register, watch, formState: { errors } } = usePluginSettingsForm()
  
  const apiKeyValue = watch('notion_api_key')
  const databaseIdValue = watch('notion_database_id')
  
  const apiKeyValidation = useFieldValidation('notion_api_key', apiKeyValue, 'API_KEY')
  const databaseIdValidation = useFieldValidation('notion_database_id', databaseIdValue, 'DATABASE_ID')

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">基础设置</h3>
      
      {/* Notion API Key */}
      <FormField
        label="Notion API Key"
        description="从Notion开发者页面获取的集成API密钥"
        required
        error={errors.notion_api_key}
        success={apiKeyValidation.isValid ? apiKeyValidation.message : undefined}
        warning={apiKeyValidation.level === 'warning' ? apiKeyValidation.message : undefined}
      >
        <PasswordInput
          {...register('notion_api_key')}
          placeholder="secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
        />
      </FormField>

      {/* Database ID */}
      <FormField
        label="Notion数据库ID"
        description="要同步的Notion数据库的32位十六进制ID"
        required
        error={errors.notion_database_id}
        success={databaseIdValidation.isValid ? databaseIdValidation.message : undefined}
      >
        <TextInput
          {...register('notion_database_id')}
          placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
        />
      </FormField>
    </div>
  )
}

/**
 * 同步设置部分
 */
const SyncSettings: React.FC = () => {
  const { register, watch, setValue } = usePluginSettingsForm()
  
  const syncSchedule = watch('sync_schedule')
  const autoSyncEnabled = watch('auto_sync_enabled')

  const scheduleOptions = VALIDATION_RULES.SYNC_SCHEDULES.map(schedule => ({
    value: schedule,
    label: {
      manual: '手动同步',
      hourly: '每小时',
      twicedaily: '每天两次',
      daily: '每天',
      weekly: '每周',
      biweekly: '每两周',
      monthly: '每月'
    }[schedule] || schedule
  }))

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">同步设置</h3>
      
      {/* 自动同步开关 */}
      <FormField
        label="自动同步"
        description="启用后将根据设定的计划自动同步内容"
      >
        <Checkbox
          {...register('auto_sync_enabled')}
          label="启用自动同步"
        />
      </FormField>

      {/* 同步计划 */}
      {autoSyncEnabled && (
        <FormField
          label="同步频率"
          description="设置自动同步的执行频率"
        >
          <Select
            {...register('sync_schedule')}
            options={scheduleOptions}
            placeholder="选择同步频率"
          />
        </FormField>
      )}

      {/* 增量同步 */}
      <FormField
        label="同步模式"
        description="选择同步时的处理方式"
      >
        <div className="space-y-3">
          <Checkbox
            {...register('incremental_sync')}
            label="启用增量同步"
            description="只同步自上次同步以来发生变化的内容"
          />
          
          <Checkbox
            {...register('check_deletions')}
            label="检查删除的内容"
            description="同步时检查并处理在Notion中删除的内容"
          />
        </div>
      </FormField>
    </div>
  )
}

/**
 * 内容设置部分
 */
const ContentSettings: React.FC = () => {
  const { register, watch, setValue } = usePluginSettingsForm()
  
  const postStatus = watch('post_status')

  const postStatusOptions = [
    { value: 'draft', label: '草稿', description: '导入的文章保存为草稿状态' },
    { value: 'publish', label: '发布', description: '导入的文章直接发布' },
    { value: 'private', label: '私有', description: '导入的文章设为私有' },
  ]

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">内容设置</h3>
      
      {/* 文章状态 */}
      <FormField
        label="默认文章状态"
        description="设置从Notion导入的文章的默认发布状态"
      >
        <RadioGroup
          name="post_status"
          value={postStatus}
          onChange={(value) => setValue('post_status', value as any)}
          options={postStatusOptions}
        />
      </FormField>

      {/* 文章作者 */}
      <FormField
        label="默认作者ID"
        description="设置导入文章的默认作者（WordPress用户ID）"
      >
        <TextInput
          {...register('post_author', { valueAsNumber: true })}
          type="number"
          placeholder="1"
          min="1"
        />
      </FormField>

      {/* 文章分类 */}
      <FormField
        label="默认分类ID"
        description="设置导入文章的默认分类（WordPress分类ID）"
      >
        <TextInput
          {...register('post_category', { valueAsNumber: true })}
          type="number"
          placeholder="1"
          min="1"
        />
      </FormField>
    </div>
  )
}

/**
 * 高级设置部分
 */
const AdvancedSettings: React.FC = () => {
  const { register, watch } = usePluginSettingsForm()
  
  const debugLevel = watch('debug_level')
  const enableLogging = watch('enable_logging')

  const debugLevelOptions = VALIDATION_RULES.DEBUG_LEVELS.map(level => ({
    value: level,
    label: {
      0: '关闭',
      1: '错误',
      2: '警告',
      3: '信息',
      4: '调试'
    }[level] || `级别 ${level}`
  }))

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">高级设置</h3>
      
      {/* 调试级别 */}
      <FormField
        label="调试级别"
        description="设置插件的调试信息详细程度"
      >
        <Select
          {...register('debug_level', { valueAsNumber: true })}
          options={debugLevelOptions}
        />
      </FormField>

      {/* 启用日志 */}
      <FormField
        label="日志记录"
        description="启用详细的操作日志记录"
      >
        <Checkbox
          {...register('enable_logging')}
          label="启用日志记录"
        />
      </FormField>

      {/* 日志保留天数 */}
      {enableLogging && (
        <FormField
          label="日志保留天数"
          description="设置日志文件的保留时间（1-365天）"
        >
          <TextInput
            {...register('log_retention_days', { valueAsNumber: true })}
            type="number"
            min="1"
            max="365"
            placeholder="30"
          />
        </FormField>
      )}
    </div>
  )
}

/**
 * 主设置表单组件
 */
export const SettingsForm: React.FC = () => {
  const { handleSubmit, submitForm, isSaving, saveError, formState } = usePluginSettingsForm()
  const { isValid, isDirty } = formState

  const onSubmit = async () => {
    try {
      await submitForm()
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl mx-auto space-y-8">
      {/* 表单内容 */}
      <div className="bg-white shadow rounded-lg divide-y divide-gray-200">
        <div className="p-6">
          <BasicSettings />
        </div>
        
        <div className="p-6">
          <SyncSettings />
        </div>
        
        <div className="p-6">
          <ContentSettings />
        </div>
        
        <div className="p-6">
          <AdvancedSettings />
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          取消
        </button>
        
        <button
          type="submit"
          disabled={!isValid || !isDirty || isSaving}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? '保存中...' : '保存设置'}
        </button>
      </div>

      {/* 错误提示 */}
      {saveError && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">
            保存失败: {saveError.message}
          </div>
        </div>
      )}
    </form>
  )
}

export default SettingsForm
