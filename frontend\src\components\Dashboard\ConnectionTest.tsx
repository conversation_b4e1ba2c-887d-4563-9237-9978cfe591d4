/**
 * 连接测试组件
 * 
 * 提供Notion API连接测试功能，验证API密钥和数据库ID的有效性
 * 
 * @since 2.0.0
 */

import React, { useState } from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Button } from '../Common'
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon } from '@heroicons/react/24/outline'

interface ConnectionTestResult {
  success: boolean
  message: string
  details?: {
    database_title?: string
    database_url?: string
    accessible_pages?: number
    integration_name?: string
  }
}

export const ConnectionTest: React.FC = () => {
  const { settings } = useSettingsStore()
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ConnectionTestResult | null>(null)

  const handleConnectionTest = async () => {
    if (!settings?.api_key || !settings?.database_id) {
      setResult({
        success: false,
        message: '请先配置API密钥和数据库ID'
      })
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      // 调用WordPress后端的连接测试API
      const response = await fetch(`${window.wpNotionConfig?.apiUrl}/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.wpNotionConfig?.nonce || ''
        },
        body: JSON.stringify({
          api_key: settings.api_key,
          database_id: settings.database_id
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setResult({
          success: true,
          message: '连接测试成功！',
          details: data.data
        })
      } else {
        setResult({
          success: false,
          message: data.message || '连接测试失败'
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: '连接测试出现错误，请检查网络连接'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const renderResult = () => {
    if (!result) return null

    const Icon = result.success ? CheckCircleIcon : XCircleIcon
    const bgColor = result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
    const textColor = result.success ? 'text-green-800' : 'text-red-800'
    const iconColor = result.success ? 'text-green-600' : 'text-red-600'

    return (
      <div className={`mt-4 p-4 rounded-lg border ${bgColor}`}>
        <div className="flex items-start">
          <Icon className={`h-5 w-5 ${iconColor} mt-0.5 flex-shrink-0`} />
          <div className="ml-3 flex-1">
            <p className={`text-sm font-medium ${textColor}`}>
              {result.message}
            </p>
            
            {result.success && result.details && (
              <div className="mt-2 space-y-1">
                <p className="text-sm text-green-700">
                  <strong>数据库标题:</strong> {result.details.database_title}
                </p>
                <p className="text-sm text-green-700">
                  <strong>可访问页面:</strong> {result.details.accessible_pages} 个
                </p>
                <p className="text-sm text-green-700">
                  <strong>集成名称:</strong> {result.details.integration_name}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <InformationCircleIcon className="h-5 w-5 text-blue-500" />
          <span className="text-sm font-medium text-gray-700">连接测试</span>
        </div>
        <Button
          variant="secondary"
          size="sm"
          onClick={handleConnectionTest}
          loading={isLoading}
          disabled={isLoading || !settings?.api_key || !settings?.database_id}
        >
          {isLoading ? '测试中...' : '测试连接'}
        </Button>
      </div>
      
      <p className="text-xs text-gray-500">
        验证API密钥和数据库ID的有效性，确保可以正常访问Notion数据库
      </p>

      {renderResult()}
    </div>
  )
}