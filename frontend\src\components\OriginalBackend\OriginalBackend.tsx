/**
 * 原版后台完全复刻组件
 * 
 * 这个组件完全复刻原版PHP后台的所有界面、功能、样式
 * 确保视觉效果和功能完全一致
 */

import React, { useState, useEffect } from 'react'
import { useResponsive } from '../../hooks/useResponsive'

// 菜单项定义（与原版完全一致）
const MENU_ITEMS = [
  { id: 'api-settings', label: '🔄 同步设置', emoji: '🔄', text: '同步设置' },
  { id: 'field-mapping', label: '🔗 字段映射', emoji: '🔗', text: '字段映射' },
  { id: 'performance-config', label: '⚡ 性能配置', emoji: '⚡', text: '性能配置' },
  { id: 'performance', label: '📊 性能监控', emoji: '📊', text: '性能监控' },
  { id: 'other-settings', label: '⚙️ 其他设置', emoji: '⚙️', text: '其他设置' },
  { id: 'debug', label: '🐞 调试工具', emoji: '🐞', text: '调试工具' },
  { id: 'help', label: '📖 使用帮助', emoji: '📖', text: '使用帮助' },
  { id: 'about-author', label: '👨‍💻 关于作者', emoji: '👨‍💻', text: '关于作者' }
] as const

type TabId = typeof MENU_ITEMS[number]['id']

interface OriginalBackendProps {
  className?: string
}

export const OriginalBackend: React.FC<OriginalBackendProps> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<TabId>('api-settings')
  const { deviceType, isMobile } = useResponsive()

  // 获取WordPress配置
  const wpConfig = (window as any).wpNotionConfig || {}
  const version = wpConfig.version || '2.0.0-beta.1'

  return (
    <div className={`notion-wp-app ${className}`} data-device={deviceType}>
      {/* 桌面端和平板端头部 */}
      {!isMobile && (
        <div className="notion-wp-header">
          <div className="notion-wp-header-content">
            <h1 className="wp-heading-inline">
              <span className="notion-wp-logo"></span>
              Notion to WordPress
            </h1>
            <div className="notion-wp-version">{version}</div>
          </div>
        </div>
      )}

      <div className="notion-wp-layout">
        {/* 侧边栏菜单 */}
        <div className="notion-wp-sidebar">
          <div className="notion-wp-menu">
            {MENU_ITEMS.map((item) => (
              <button
                key={item.id}
                className={`notion-wp-menu-item ${activeTab === item.id ? 'active' : ''}`}
                data-tab={item.id}
                onClick={() => setActiveTab(item.id)}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="notion-wp-content">
          {/* 同步设置页面 */}
          {activeTab === 'api-settings' && (
            <div className="notion-wp-tab-content active" id="api-settings">
              <div className="notion-wp-settings-section">
                <h2>Notion API 设置</h2>
                
                {/* 统计卡片 */}
                <div className="notion-stats-grid">
                  <div className="stat-card">
                    <h3 className="stat-imported-count">0</h3>
                    <span>已导入页面</span>
                  </div>
                  <div className="stat-card">
                    <h3 className="stat-published-count">0</h3>
                    <span>已发布页面</span>
                  </div>
                  <div className="stat-card">
                    <h3 className="stat-last-update">从未</h3>
                    <span>最后同步</span>
                  </div>
                  <div className="stat-card">
                    <h3 className="stat-next-run">未计划</h3>
                    <span>下次同步</span>
                  </div>
                </div>
                
                <p className="description">
                  连接到您的Notion数据库所需的设置。
                  <a href="https://developers.notion.com/docs/getting-started" target="_blank">了解如何获取API密钥</a>
                </p>
                
                {/* API设置表单 */}
                <table className="form-table">
                  <tbody>
                    <tr>
                      <th scope="row">
                        <label htmlFor="notion_to_wordpress_api_key">API密钥</label>
                      </th>
                      <td>
                        <div className="input-with-validation">
                          <div className="input-with-button">
                            <input
                              type="password"
                              id="notion_to_wordpress_api_key"
                              name="notion_to_wordpress_api_key"
                              className="regular-text notion-wp-validated-input"
                              autoComplete="off"
                              placeholder="输入您的Notion API密钥"
                              data-validation="api-key"
                            />
                            <button
                              type="button"
                              className="button button-secondary show-hide-password"
                              title="显示/隐藏密钥"
                            >
                              <span className="dashicons dashicons-visibility"></span>
                            </button>
                          </div>
                          <div className="validation-feedback" id="api-key-feedback"></div>
                        </div>
                        <p className="description">在Notion的"我的集成"页面创建并获取API密钥。</p>
                      </td>
                    </tr>
                    <tr>
                      <th scope="row">
                        <label htmlFor="notion_to_wordpress_database_id">数据库ID</label>
                      </th>
                      <td>
                        <div className="input-with-validation">
                          <input
                            type="text"
                            id="notion_to_wordpress_database_id"
                            name="notion_to_wordpress_database_id"
                            className="regular-text notion-wp-validated-input"
                            placeholder="输入您的Notion数据库ID"
                            data-validation="database-id"
                          />
                          <div className="validation-feedback" id="database-id-feedback"></div>
                        </div>
                        <p className="description">
                          可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/<strong>数据库ID</strong>?v=xxx
                        </p>
                      </td>
                    </tr>
                  </tbody>
                </table>

                {/* 测试连接按钮 */}
                <div className="notion-wp-button-row">
                  <button type="button" id="notion-test-connection" className="button button-secondary">
                    <span className="dashicons dashicons-admin-network"></span> 测试连接
                  </button>
                </div>

                {/* 同步操作按钮 */}
                <div className="notion-wp-sync-actions">
                  <h3>同步操作</h3>
                  <div className="sync-buttons">
                    <button
                      type="button"
                      className="button button-primary notion-wp-sync-btn"
                      id="notion-manual-import"
                      data-sync-type="manual"
                    >
                      <span className="dashicons dashicons-lightbulb"></span>
                      <span className="button-text">智能同步</span>
                    </button>
                    <button
                      type="button"
                      className="button button-secondary notion-wp-sync-btn"
                      id="notion-full-import"
                      data-sync-type="full"
                    >
                      <span className="dashicons dashicons-update"></span>
                      <span className="button-text">完全同步</span>
                    </button>
                  </div>

                  {/* 同步进度指示器 */}
                  <div className="sync-progress notion-wp-hidden" id="sync-progress">
                    <div className="progress-bar">
                      <div className="progress-fill"></div>
                    </div>
                    <div className="progress-text">
                      <span className="current-step">准备同步...</span>
                      <span className="progress-percentage">0%</span>
                    </div>
                  </div>

                  <div className="sync-info">
                    <p><strong>智能同步</strong>: 只同步有变化的页面，速度更快</p>
                    <p><strong>完全同步</strong>: 同步所有页面，确保数据一致性</p>
                  </div>
                </div>
              </div>

              {/* 快速配置部分 */}
              <div className="notion-wp-settings-section">
                <h2>🚀 快速配置</h2>
                <p className="description">使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。</p>

                <table className="form-table">
                  <tbody>
                    <tr>
                      <th scope="row">
                        <label htmlFor="performance_level">性能级别</label>
                      </th>
                      <td>
                        <select id="performance_level" name="performance_level" className="regular-text">
                          <option value="conservative">保守模式 - 适合配置较低的服务器</option>
                          <option value="balanced" selected>平衡模式 - 推荐的默认配置</option>
                          <option value="aggressive">激进模式 - 适合高性能服务器</option>
                        </select>
                        <p className="description">选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。</p>
                      </td>
                    </tr>
                    <tr>
                      <th scope="row">
                        <label htmlFor="field_template">字段映射模板</label>
                      </th>
                      <td>
                        <select id="field_template" name="field_template" className="regular-text">
                          <option value="english">英文模板 - 适合英文Notion数据库</option>
                          <option value="chinese">中文模板 - 适合中文Notion数据库</option>
                          <option value="mixed" selected>混合模板 - 中英文兼容</option>
                          <option value="custom">自定义 - 手动配置所有字段</option>
                        </select>
                        <p className="description">选择与您的Notion数据库语言匹配的字段映射模板。选择"自定义"可在字段映射标签页进行详细配置。</p>
                      </td>
                    </tr>
                  </tbody>
                </table>

                {/* 智能推荐 */}
                <div className="notion-wp-smart-recommendations">
                  <h3>💡 智能推荐</h3>
                  <div id="config-recommendations">
                    <button type="button" className="button button-secondary" id="get-smart-recommendations">
                      获取配置建议
                    </button>
                    <div id="recommendations-result" className="notion-wp-hidden"></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 其他标签页内容 - 暂时显示占位符 */}
          {activeTab !== 'api-settings' && (
            <div className="notion-wp-tab-content active">
              <div className="notion-wp-settings-section">
                <h2>{MENU_ITEMS.find(item => item.id === activeTab)?.text}</h2>
                <p>此页面内容正在开发中...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default OriginalBackend
