/**
 * 智能推荐组件
 * 
 * 提供基于用户环境和使用模式的个性化配置推荐
 * 
 * @since 2.0.0
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, Button } from '../Common'
import {
  SparklesIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ChartBarIcon,
  CogIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'
import { useI18n } from '../../hooks/useI18n'
import { useSettingsStore } from '../../stores/settingsStore'
import { smartRecommendationEngine, type RecommendationConfig, type ConfigConflict, type EnvironmentAnalysis } from '../../utils/SmartRecommendationEngine'
import { ApiService } from '../../services/api'
import type { SystemInfo } from '../../types'

interface RecommendationResult {
  config: RecommendationConfig
  explanation: string
  expected_improvement: string
  confidence: number
  conflicts: ConfigConflict[]
  environment: EnvironmentAnalysis
}

export const SmartRecommendations: React.FC = () => {
  const { __ } = useI18n()
  const { settings, updateSettings, saveSettings, hasUnsavedChanges, isSaving } = useSettingsStore()
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [recommendation, setRecommendation] = useState<RecommendationResult | null>(null)
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [showHistory, setShowHistory] = useState(false)
  const [autoFixEnabled, setAutoFixEnabled] = useState(true)
  
  const apiService = new ApiService()

  // 获取系统信息
  useEffect(() => {
    const fetchSystemInfo = async () => {
      try {
        const info = await apiService.getSystemInfo()
        setSystemInfo(info)
      } catch (error) {
        console.error(__('Failed to fetch system info'), error)
      }
    }
    
    fetchSystemInfo()
  }, [])

  // 生成智能推荐
  const generateRecommendations = async () => {
    setIsAnalyzing(true)
    setRecommendation(null)

    try {
      // 分析环境
      const environment = smartRecommendationEngine.analyzeEnvironment(systemInfo || undefined)
      
      // 生成推荐
      const result = smartRecommendationEngine.generateRecommendations(
        settings || {},
        environment,
        detectUsagePattern()
      )

      setRecommendation({
        ...result,
        environment
      })
    } catch (error) {
      console.error(__('Failed to generate recommendations'), error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 检测使用模式
  const detectUsagePattern = (): 'light' | 'moderate' | 'heavy' => {
    if (!settings) return 'light'
    
    let score = 0
    if (settings.enable_auto_sync) score += 1
    if (settings.sync_schedule && ['hourly', 'twicedaily'].includes(settings.sync_schedule)) score += 2
    if (settings.concurrent_requests && settings.concurrent_requests > 3) score += 1
    if (settings.api_page_size && settings.api_page_size > 50) score += 1
    
    if (score >= 4) return 'heavy'
    if (score >= 2) return 'moderate'
    return 'light'
  }

  // 应用推荐配置
  const applyRecommendations = async () => {
    if (!recommendation) return

    const newSettings = {
      ...settings,
      performance_level: recommendation.config.performance_level,
      api_page_size: recommendation.config.api_page_size,
      concurrent_requests: recommendation.config.concurrent_requests,
      batch_size: recommendation.config.batch_size,
      log_buffer_size: recommendation.config.log_buffer_size,
      sync_schedule: recommendation.config.sync_schedule,
      enable_auto_sync: recommendation.config.enable_auto_sync
    }

    updateSettings(newSettings)
    
    // 标记为已应用
    smartRecommendationEngine.markAsApplied(
      new Date().toISOString(),
      recommendation.expected_improvement
    )
  }

  // 自动修复冲突
  const autoFixConflicts = () => {
    if (!recommendation || !autoFixEnabled) return

    const fixableConflicts = recommendation.conflicts.filter(c => c.auto_fix)
    if (fixableConflicts.length === 0) return

    let updatedConfig = { ...recommendation.config }

    fixableConflicts.forEach(conflict => {
      switch (conflict.type) {
        case 'resource':
          if (conflict.description.includes('并发请求数')) {
            updatedConfig.concurrent_requests = Math.min(updatedConfig.concurrent_requests, 3)
          }
          break
        case 'performance':
          if (conflict.description.includes('API分页大小')) {
            updatedConfig.api_page_size = Math.min(updatedConfig.api_page_size, 30)
          }
          break
        case 'compatibility':
          if (conflict.description.includes('自动同步')) {
            updatedConfig.sync_schedule = 'daily'
            updatedConfig.enable_auto_sync = false
          }
          break
      }
    })

    setRecommendation(prev => prev ? { ...prev, config: updatedConfig } : null)
  }

  // 渲染环境分析
  const renderEnvironmentAnalysis = () => {
    if (!recommendation) return null

    const { environment } = recommendation
    const getStatusColor = (tier: string) => {
      switch (tier) {
        case 'high': return 'text-green-600 bg-green-50'
        case 'medium': return 'text-yellow-600 bg-yellow-50'
        case 'low': return 'text-red-600 bg-red-50'
        default: return 'text-gray-600 bg-gray-50'
      }
    }

    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <ChartBarIcon className="h-4 w-4 mr-2" />
          {__('Environment Analysis')}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
          <div className="text-center">
            <div className={`px-2 py-1 rounded ${getStatusColor(environment.performance_tier)}`}>
              {environment.performance_tier.toUpperCase()}
            </div>
            <div className="text-gray-600 mt-1">{__('Performance')}</div>
          </div>
          <div className="text-center">
            <div className="px-2 py-1 rounded bg-blue-50 text-blue-600">
              {environment.memory_available}MB
            </div>
            <div className="text-gray-600 mt-1">{__('Memory')}</div>
          </div>
          <div className="text-center">
            <div className="px-2 py-1 rounded bg-purple-50 text-purple-600">
              {environment.server_type.toUpperCase()}
            </div>
            <div className="text-gray-600 mt-1">{__('Server Type')}</div>
          </div>
          <div className="text-center">
            <div className={`px-2 py-1 rounded ${getStatusColor(environment.risk_level)}`}>
              {environment.risk_level.toUpperCase()}
            </div>
            <div className="text-gray-600 mt-1">{__('Risk Level')}</div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染配置冲突
  const renderConflicts = () => {
    if (!recommendation || recommendation.conflicts.length === 0) return null

    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <h4 className="text-sm font-medium text-yellow-800 mb-3 flex items-center">
          <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
          {__('Configuration Conflicts')} ({recommendation.conflicts.length})
        </h4>
        <div className="space-y-2">
          {recommendation.conflicts.map((conflict, index) => (
            <div key={index} className="text-sm">
              <div className="flex items-start space-x-2">
                <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                  conflict.severity === 'critical' ? 'bg-red-500' :
                  conflict.severity === 'error' ? 'bg-orange-500' : 'bg-yellow-500'
                }`} />
                <div className="flex-1">
                  <p className="text-yellow-800">{conflict.description}</p>
                  <p className="text-yellow-700 mt-1">{conflict.suggestion}</p>
                  {conflict.auto_fix && (
                    <span className="inline-block mt-1 px-2 py-1 bg-yellow-200 text-yellow-800 rounded text-xs">
                      {__('Auto-fixable')}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        {recommendation.conflicts.some(c => c.auto_fix) && autoFixEnabled && (
          <Button
            variant="secondary"
            size="sm"
            onClick={autoFixConflicts}
            className="mt-3"
          >
            <CogIcon className="h-4 w-4 mr-1" />
            {__('Auto Fix Conflicts')}
          </Button>
        )}
      </div>
    )
  }

  // 渲染推荐配置
  const renderRecommendationConfig = () => {
    if (!recommendation) return null

    const { config, confidence } = recommendation

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start justify-between mb-3">
          <h4 className="text-sm font-medium text-blue-800 flex items-center">
            <LightBulbIcon className="h-4 w-4 mr-2" />
            {__('Recommended Configuration')}
          </h4>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-blue-600">{__('Confidence')}: {confidence}%</span>
            <div className={`w-2 h-2 rounded-full ${
              confidence >= 85 ? 'bg-green-500' :
              confidence >= 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
          </div>
        </div>
        
        <p className="text-sm text-blue-700 mb-4">{recommendation.explanation}</p>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-xs mb-4">
          <div>
            <span className="text-blue-600 font-medium">{__('Performance Level')}:</span>
            <div className="text-blue-800 mt-1">{config.performance_level}</div>
          </div>
          <div>
            <span className="text-blue-600 font-medium">{__('API Page Size')}:</span>
            <div className="text-blue-800 mt-1">{config.api_page_size}</div>
          </div>
          <div>
            <span className="text-blue-600 font-medium">{__('Concurrent Requests')}:</span>
            <div className="text-blue-800 mt-1">{config.concurrent_requests}</div>
          </div>
          <div>
            <span className="text-blue-600 font-medium">{__('Batch Size')}:</span>
            <div className="text-blue-800 mt-1">{config.batch_size}</div>
          </div>
          <div>
            <span className="text-blue-600 font-medium">{__('Sync Schedule')}:</span>
            <div className="text-blue-800 mt-1">{config.sync_schedule}</div>
          </div>
          <div>
            <span className="text-blue-600 font-medium">{__('Auto Sync')}:</span>
            <div className="text-blue-800 mt-1">{config.enable_auto_sync ? __('Enabled') : __('Disabled')}</div>
          </div>
        </div>
        
        <div className="bg-blue-100 rounded p-3 mb-4">
          <p className="text-sm text-blue-800">
            <strong>{__('Expected Improvement')}:</strong> {recommendation.expected_improvement}
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button
            variant="primary"
            size="sm"
            onClick={applyRecommendations}
          >
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            {__('Apply Recommendations')}
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
          >
            <ClockIcon className="h-4 w-4 mr-1" />
            {__('View History')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card
      title={`✨ ${__('Smart Recommendations')}`}
      subtitle={__('AI-powered configuration optimization based on your environment and usage patterns')}
      shadow="md"
    >
      <CardContent className="space-y-4">
        {/* 分析按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-blue-500" />
            <span className="text-sm text-gray-600">
              {__('Get personalized configuration recommendations')}
            </span>
          </div>
          <Button
            variant="primary"
            onClick={generateRecommendations}
            loading={isAnalyzing}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? __('Analyzing...') : __('Analyze & Recommend')}
          </Button>
        </div>

        {/* 环境分析 */}
        {renderEnvironmentAnalysis()}

        {/* 配置冲突 */}
        {renderConflicts()}

        {/* 推荐配置 */}
        {renderRecommendationConfig()}

        {/* 推荐历史 */}
        {showHistory && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              {__('Recommendation History')}
            </h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {smartRecommendationEngine.getHistory().slice(0, 5).map((item, index) => (
                <div key={index} className="text-xs bg-white rounded p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">
                      {new Date(item.timestamp).toLocaleString()}
                    </span>
                    <span className={`px-2 py-1 rounded ${
                      item.applied ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {item.applied ? __('Applied') : __('Suggested')}
                    </span>
                  </div>
                  <p className="text-gray-700 mt-1">{item.reason}</p>
                  {item.performance_impact && (
                    <p className="text-green-600 mt-1">{item.performance_impact}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 保存提示 */}
        {hasUnsavedChanges && (
          <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">{__('You have unsaved configuration changes')}</p>
            <Button
              variant="primary"
              onClick={saveSettings}
              loading={isSaving}
              disabled={isSaving}
            >
              {isSaving ? __('Saving...') : __('Save Configuration')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
