/**
 * 性能优化工具函数
 * 
 * 提供组件性能优化、内存管理、渲染优化等功能
 * 
 * @since 2.0.0
 */

import { useCallback, useRef, useMemo, useEffect, useState } from 'react'

/**
 * 防抖Hook
 * 用于优化频繁触发的事件处理
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>()
  
  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args)
      }, delay)
    }) as T,
    [callback, delay]
  )
}

/**
 * 节流Hook
 * 用于限制函数执行频率
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0)
  const timeoutRef = useRef<NodeJS.Timeout>()
  
  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now()
      
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now
        callback(...args)
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now()
          callback(...args)
        }, delay - (now - lastCallRef.current))
      }
    }) as T,
    [callback, delay]
  )
}

/**
 * 深度比较Hook
 * 用于避免对象引用变化导致的不必要重渲染
 */
export function useDeepMemo<T>(value: T, deps?: React.DependencyList): T {
  const ref = useRef<T>(value)
  const depsRef = useRef<React.DependencyList | undefined>(deps)
  
  return useMemo(() => {
    // 深度比较依赖项
    if (deps && depsRef.current) {
      const hasChanged = deps.some((dep, index) => {
        return !Object.is(dep, depsRef.current?.[index])
      })
      
      if (!hasChanged) {
        return ref.current
      }
    }
    
    // 深度比较值
    if (deepEqual(value, ref.current)) {
      return ref.current
    }
    
    ref.current = value
    depsRef.current = deps
    return value
  }, deps)
}

/**
 * 深度相等比较
 */
function deepEqual(a: any, b: any): boolean {
  if (a === b) return true
  
  if (a == null || b == null) return a === b
  
  if (typeof a !== typeof b) return false
  
  if (typeof a !== 'object') return a === b
  
  if (Array.isArray(a) !== Array.isArray(b)) return false
  
  if (Array.isArray(a)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => deepEqual(item, b[index]))
  }
  
  const keysA = Object.keys(a)
  const keysB = Object.keys(b)
  
  if (keysA.length !== keysB.length) return false
  
  return keysA.every(key => deepEqual(a[key], b[key]))
}

/**
 * 虚拟化列表Hook
 * 用于大数据量列表的性能优化
 */
export function useVirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )
    
    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])
  
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => ({
      item,
      index: visibleRange.start + index
    }))
  }, [items, visibleRange])
  
  const totalHeight = items.length * itemHeight
  const offsetY = visibleRange.start * itemHeight
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop
  }
}

/**
 * 内存泄漏检测Hook
 * 用于开发环境下检测潜在的内存泄漏
 */
export function useMemoryLeakDetection(componentName: string) {
  const mountTimeRef = useRef<number>(Date.now())
  const timersRef = useRef<Set<NodeJS.Timeout>>(new Set())
  const intervalsRef = useRef<Set<NodeJS.Timeout>>(new Set())
  const listenersRef = useRef<Map<string, EventListener>>(new Map())
  
  // 包装setTimeout
  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    const timer = setTimeout(() => {
      timersRef.current.delete(timer)
      callback()
    }, delay)
    
    timersRef.current.add(timer)
    return timer
  }, [])
  
  // 包装setInterval
  const safeSetInterval = useCallback((callback: () => void, delay: number) => {
    const interval = setInterval(callback, delay)
    intervalsRef.current.add(interval)
    return interval
  }, [])
  
  // 包装addEventListener
  const safeAddEventListener = useCallback((
    target: EventTarget,
    event: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    target.addEventListener(event, listener, options)
    listenersRef.current.set(`${event}_${Date.now()}`, listener)
  }, [])
  
  // 清理函数
  const cleanup = useCallback(() => {
    // 清理定时器
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current.clear()
    
    // 清理间隔器
    intervalsRef.current.forEach(interval => clearInterval(interval))
    intervalsRef.current.clear()
    
    // 清理事件监听器
    listenersRef.current.clear()
  }, [])
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 [Memory] ${componentName} mounted`)
    }
    
    return () => {
      cleanup()
      
      if (process.env.NODE_ENV === 'development') {
        const lifeTime = Date.now() - mountTimeRef.current
        console.log(`🧹 [Memory] ${componentName} unmounted after ${lifeTime}ms`)
        
        // 检查是否有未清理的资源
        if (timersRef.current.size > 0) {
          console.warn(`⚠️ [Memory] ${componentName} has ${timersRef.current.size} uncleaned timers`)
        }
        
        if (intervalsRef.current.size > 0) {
          console.warn(`⚠️ [Memory] ${componentName} has ${intervalsRef.current.size} uncleaned intervals`)
        }
        
        if (listenersRef.current.size > 0) {
          console.warn(`⚠️ [Memory] ${componentName} has ${listenersRef.current.size} uncleaned listeners`)
        }
      }
    }
  }, [componentName, cleanup])
  
  return {
    safeSetTimeout,
    safeSetInterval,
    safeAddEventListener,
    cleanup
  }
}

/**
 * 组件渲染性能监控Hook
 */
export function useRenderPerformance(componentName: string) {
  const renderCountRef = useRef(0)
  const lastRenderTimeRef = useRef<number>(Date.now())
  
  useEffect(() => {
    renderCountRef.current += 1
    const now = Date.now()
    const timeSinceLastRender = now - lastRenderTimeRef.current
    lastRenderTimeRef.current = now
    
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `🎨 [Render] ${componentName} rendered ${renderCountRef.current} times, ` +
        `${timeSinceLastRender}ms since last render`
      )
      
      // 警告频繁重渲染
      if (timeSinceLastRender < 16 && renderCountRef.current > 1) {
        console.warn(
          `⚠️ [Performance] ${componentName} is rendering too frequently (${timeSinceLastRender}ms)`
        )
      }
    }
  })
  
  return {
    renderCount: renderCountRef.current
  }
}

/**
 * 批量状态更新Hook
 * 用于优化多个状态更新的性能
 */
export function useBatchedUpdates() {
  const updatesRef = useRef<Array<() => void>>([])
  const timeoutRef = useRef<NodeJS.Timeout>()
  
  const batchUpdate = useCallback((updateFn: () => void) => {
    updatesRef.current.push(updateFn)
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      const updates = updatesRef.current.splice(0)
      updates.forEach(update => update())
    }, 0)
  }, [])
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
  
  return batchUpdate
}

/**
 * 懒加载Hook
 * 用于组件的懒加载优化
 */
export function useLazyLoad<T>(
  loadFn: () => Promise<T>,
  deps: React.DependencyList = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const load = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await loadFn()
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)))
    } finally {
      setLoading(false)
    }
  }, deps)
  
  return {
    data,
    loading,
    error,
    load
  }
}
