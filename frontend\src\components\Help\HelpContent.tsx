import React from 'react'
import { Card, CardContent } from '../Common'

export const HelpContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          使用帮助
        </h2>
      </div>

      {/* 快速开始 */}
      <Card title="快速开始" shadow="md">
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>在Notion创建一个集成并获取API密钥</li>
            <li>在Notion中创建一个数据库，并与您的集成共享</li>
            <li>复制数据库ID（从URL中获取）</li>
            <li>在此页面配置API密钥和数据库ID</li>
            <li>配置字段映射，确保Notion属性名称与WordPress字段正确对应</li>
            <li>点击"测试连接"确认设置正确</li>
            <li>点击"保存所有设置"保存您的配置</li>
            <li>点击"手动同步"或设置自动同步频率开始导入内容</li>
          </ol>
        </CardContent>
      </Card>

      {/* 常见问题 */}
      <Card title="常见问题" shadow="md">
        <CardContent className="space-y-4">
          <div>
            <p className="font-medium text-gray-800 mb-2">问：为什么我的Notion页面没有导入？</p>
            <p className="text-sm text-gray-600 mb-2">答：请检查以下几点：</p>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 ml-4">
              <li>确认您的API密钥和数据库ID正确</li>
              <li>确认您的Notion集成已与数据库共享</li>
              <li>检查字段映射是否正确对应Notion中的属性名称</li>
              <li>尝试使用"完全同步"按钮重新同步</li>
            </ul>
          </div>

          <div>
            <p className="font-medium text-gray-800 mb-2">问：如何自定义导入的内容格式？</p>
            <p className="text-sm text-gray-600">
              答：本插件会尽可能保留Notion中的格式，包括标题、列表、表格、代码块等。对于特殊内容（如数学公式、图表），插件也提供了支持。
            </p>
          </div>

          <div>
            <p className="font-medium text-gray-800 mb-2">问：导入后如何更新内容？</p>
            <p className="text-sm text-gray-600">
              答：当您在Notion中更新内容后，可以点击"完全同步"按钮手动更新，或等待自动同步（如果已设置）。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 获取支持 */}
      <Card title="获取支持" shadow="md">
        <CardContent>
          <p className="text-sm text-gray-600 mb-2">
            如果您遇到任何问题或需要帮助，请访问我们的GitHub仓库：
          </p>
          <p>
            <a
              href="https://github.com/Frank-Loong/Notion-to-WordPress"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              https://github.com/Frank-Loong/Notion-to-WordPress
            </a>
          </p>
        </CardContent>
      </Card>
    </div>
  )
}


