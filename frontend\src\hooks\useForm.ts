/**
 * 表单处理hooks
 * 
 * 基于React Hook Form和Zod的类型安全表单处理
 * 
 * @since 2.0.0
 */

import React, { useCallback, useEffect, useMemo } from 'react'
import { useForm as useReactHookForm, UseFormProps, UseFormReturn } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { debounce } from 'lodash'
import { useSettingsStore } from '../stores/settingsStore'
import { useSaveSettings } from './useApi'
import { 
  pluginSettingsSchema, 
  PluginSettingsFormData,
  FieldValidationResult,
  VALIDATION_RULES,
  VALIDATION_MESSAGES
} from '../schemas/validation'

/**
 * 扩展的表单配置
 */
export interface UseFormConfig<T extends z.ZodType> extends Omit<UseFormProps<z.infer<T>>, 'resolver'> {
  /** Zod验证模式 */
  schema: T
  /** 是否自动保存 */
  autoSave?: boolean
  /** 自动保存延迟（毫秒） */
  autoSaveDelay?: number
  /** 提交成功回调 */
  onSubmitSuccess?: (data: z.infer<T>) => void
  /** 提交失败回调 */
  onSubmitError?: (error: Error) => void
}

/**
 * 扩展的表单返回值
 */
export interface UseFormResult<T extends z.ZodType> extends UseFormReturn<z.infer<T>> {
  /** 是否正在提交 */
  isSubmitting: boolean
  /** 提交表单 */
  submitForm: () => Promise<void>
  /** 验证单个字段 */
  validateField: (fieldName: keyof z.infer<T>, value: any) => FieldValidationResult
  /** 重置为初始值 */
  resetToInitial: () => void
}

/**
 * 通用表单hook
 */
export function useForm<T extends z.ZodType>(
  config: UseFormConfig<T>
): UseFormResult<T> {
  const {
    schema,
    autoSave = false,
    autoSaveDelay = 1000,
    onSubmitSuccess,
    onSubmitError,
    ...formConfig
  } = config

  const form = useReactHookForm<z.infer<T>>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    ...formConfig,
  })

  const { handleSubmit, watch, reset, trigger, getValues, formState } = form
  const { isSubmitting } = formState

  // 提交表单
  const submitForm = useCallback(async () => {
    try {
      await handleSubmit(async (data) => {
        onSubmitSuccess?.(data)
      })()
    } catch (error) {
      onSubmitError?.(error as Error)
    }
  }, [handleSubmit, onSubmitSuccess, onSubmitError])

  // 验证单个字段
  const validateField = useCallback((fieldName: keyof z.infer<T>, value: any): FieldValidationResult => {
    try {
      // 创建只包含该字段的临时schema
      const fieldSchema = schema.shape[fieldName as string]
      if (!fieldSchema) {
        return { isValid: true, message: '', level: 'success' }
      }

      fieldSchema.parse(value)
      return { isValid: true, message: '格式正确', level: 'success' }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0]
        return {
          isValid: false,
          message: firstError.message,
          level: 'error'
        }
      }
      return { isValid: false, message: '验证失败', level: 'error' }
    }
  }, [schema])

  // 重置为初始值
  const resetToInitial = useCallback(() => {
    reset(formConfig.defaultValues)
  }, [reset, formConfig.defaultValues])

  // 自动保存功能
  useEffect(() => {
    if (!autoSave) return

    const subscription = watch((data) => {
      const timeoutId = setTimeout(() => {
        // 这里可以实现自动保存逻辑
        console.log('Auto-saving form data:', data)
      }, autoSaveDelay)

      return () => clearTimeout(timeoutId)
    })

    return () => subscription.unsubscribe()
  }, [watch, autoSave, autoSaveDelay])

  return {
    ...form,
    isSubmitting,
    submitForm,
    validateField,
    resetToInitial,
  }
}

/**
 * 插件设置表单hook
 */
export function usePluginSettingsForm() {
  const { settings, updateSettings } = useSettingsStore()
  const saveSettingsMutation = useSaveSettings()

  const form = useForm({
    schema: pluginSettingsSchema,
    defaultValues: settings,
    onSubmitSuccess: async (data) => {
      try {
        await saveSettingsMutation.mutateAsync(data)
        updateSettings(data)
        console.log('设置保存成功')
      } catch (error) {
        console.error('设置保存失败:', error)
        throw error
      }
    },
    onSubmitError: (error) => {
      console.error('表单提交失败:', error)
    },
  })

  // 同步Zustand store的变化到表单
  useEffect(() => {
    form.reset(settings)
  }, [settings, form])

  return {
    ...form,
    isSaving: saveSettingsMutation.isLoading,
    saveError: saveSettingsMutation.error,
  }
}

/**
 * 实时字段验证hook
 */
export function useFieldValidation(
  fieldName: string,
  value: any,
  validationType: keyof typeof VALIDATION_RULES
) {
  const validateField = useCallback((): FieldValidationResult => {
    const rules = VALIDATION_RULES[validationType]
    const messages = VALIDATION_MESSAGES[validationType as keyof typeof VALIDATION_MESSAGES]

    if (!value || value.trim() === '') {
      return {
        isValid: false,
        message: messages?.REQUIRED || '此字段不能为空',
        level: 'error'
      }
    }

    switch (validationType) {
      case 'API_KEY': {
        const trimmedValue = value.trim()
        
        if (trimmedValue.length < rules.MIN_LENGTH || trimmedValue.length > rules.MAX_LENGTH) {
          return {
            isValid: false,
            message: messages.LENGTH,
            level: 'warning'
          }
        }
        
        if (!rules.PATTERN.test(trimmedValue)) {
          return {
            isValid: false,
            message: messages.FORMAT,
            level: 'warning'
          }
        }
        
        return {
          isValid: true,
          message: 'API密钥格式正确',
          level: 'success'
        }
      }

      case 'DATABASE_ID': {
        const cleanValue = value.replace(/-/g, '')
        
        if (cleanValue.length !== rules.LENGTH) {
          return {
            isValid: false,
            message: messages.LENGTH,
            level: 'error'
          }
        }
        
        if (!rules.PATTERN.test(cleanValue)) {
          return {
            isValid: false,
            message: messages.FORMAT,
            level: 'error'
          }
        }
        
        return {
          isValid: true,
          message: '数据库ID格式正确',
          level: 'success'
        }
      }

      default:
        return {
          isValid: true,
          message: '',
          level: 'success'
        }
    }
  }, [value, validationType])

  return validateField()
}

/**
 * WordPress nonce验证hook
 */
export function useNonceValidation() {
  const validateNonce = useCallback((action: string): boolean => {
    // 获取WordPress nonce
    const nonce = (window as any).wpNotionConfig?.nonce
    
    if (!nonce) {
      console.warn('WordPress nonce not found')
      return false
    }

    // 这里可以添加更复杂的nonce验证逻辑
    // 目前只是简单检查nonce是否存在
    return typeof nonce === 'string' && nonce.length > 0
  }, [])

  return { validateNonce }
}

/**
 * 表单防抖提交hook
 */
export function useDebouncedSubmit<T>(
  submitFn: (data: T) => Promise<void>,
  delay: number = 500
) {
  const [timeoutId, setTimeoutId] = React.useState<NodeJS.Timeout | null>(null)

  const debouncedSubmit = useCallback((data: T) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    const newTimeoutId = setTimeout(() => {
      submitFn(data)
    }, delay)

    setTimeoutId(newTimeoutId)
  }, [submitFn, delay, timeoutId])

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [timeoutId])

  return debouncedSubmit
}

/**
 * 增强表单hook - 集成Zustand状态管理
 * 提供实时验证、状态同步和性能优化
 */
export const useEnhancedForm = <T extends z.ZodType>(
  config: UseFormConfig<T>
): UseFormReturn<z.infer<T>> & {
  isFormValid: boolean;
  validationInProgress: boolean;
  fieldValidations: Record<string, any>;
  clearFieldValidation: (field: string) => void;
  clearAllValidations: () => void;
} => {
  const settingsStore = useSettingsStore();
  const form = useReactHookForm<z.infer<T>>({
    ...config,
    resolver: zodResolver(config.schema),
  });

  // 获取表单验证状态
  const {
    fieldValidations,
    isFormValid,
    validationInProgress,
    enableRealTimeValidation,
    validationDebounceMs,
  } = settingsStore.formValidation;

  // 创建防抖验证函数
  const debouncedValidation = useMemo(
    () => debounce((fieldName: string, value: any) => {
      if (!enableRealTimeValidation) return;

      settingsStore.setValidationInProgress(true);

      try {
        // 尝试验证单个字段
        const fieldSchema = config.schema.shape?.[fieldName] || config.schema;
        fieldSchema.parse(value);

        settingsStore.setFieldValidation(fieldName, {
          isValid: true,
          level: 'success',
          message: VALIDATION_MESSAGES.success.field_valid,
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const firstError = error.errors[0];
          settingsStore.setFieldValidation(fieldName, {
            isValid: false,
            level: 'error',
            message: firstError.message,
          });
        }
      } finally {
        settingsStore.setValidationInProgress(false);
      }
    }, validationDebounceMs),
    [config.schema, enableRealTimeValidation, validationDebounceMs, settingsStore]
  );

  // 监听表单值变化，触发实时验证
  const watchedValues = form.watch();
  useEffect(() => {
    if (!enableRealTimeValidation) return;

    Object.entries(watchedValues).forEach(([fieldName, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        debouncedValidation(fieldName, value);
      }
    });

    return () => {
      debouncedValidation.cancel();
    };
  }, [watchedValues, debouncedValidation, enableRealTimeValidation]);

  // 清理函数
  useEffect(() => {
    return () => {
      debouncedValidation.cancel();
    };
  }, [debouncedValidation]);

  return {
    ...form,
    isFormValid,
    validationInProgress,
    fieldValidations,
    clearFieldValidation: settingsStore.clearFieldValidation,
    clearAllValidations: settingsStore.clearAllValidations,
  };
};
