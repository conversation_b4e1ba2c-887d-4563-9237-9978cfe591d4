/**
 * 临时修复脚本：修复React应用的响应式问题
 * 
 * 这个脚本会在页面加载后立即执行，确保React应用正确设置响应式类和属性
 */

(function() {
    'use strict';
    
    console.log('🔧 开始修复React应用响应式问题...');
    
    function fixResponsive() {
        const app = document.querySelector('.notion-wp-app');
        if (!app) {
            console.warn('⚠️ 未找到React应用根元素');
            return false;
        }
        
        // 获取当前视口尺寸
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // 确定设备类型
        let deviceType = 'desktop';
        if (width < 768) {
            deviceType = 'mobile';
        } else if (width < 1024) {
            deviceType = 'tablet';
        }
        
        // 设置data-device属性
        app.setAttribute('data-device', deviceType);
        
        // 设置响应式CSS类
        const baseClasses = 'notion-wp-app';
        const responsiveClass = `notion-wp-${deviceType}`;
        app.className = `${baseClasses} ${responsiveClass}`;
        
        // 确保侧边栏可见（桌面端）
        if (deviceType === 'desktop') {
            const sidebar = app.querySelector('.notion-wp-sidebar');
            if (sidebar) {
                sidebar.style.setProperty('display', 'block', 'important');
            }
        }
        
        console.log(`✅ 响应式修复完成: ${deviceType} (${width}x${height})`);
        return true;
    }

    // 暴露到全局作用域供测试使用
    window.fixResponsive = fixResponsive;
    
    function waitForReactApp() {
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时
        
        const checkInterval = setInterval(() => {
            attempts++;
            
            const reactRoot = document.getElementById('notion-to-wordpress-react-root');
            const hasReactContent = reactRoot && reactRoot.children.length > 0;
            
            if (hasReactContent) {
                clearInterval(checkInterval);
                
                // 等待一小段时间让React完全渲染
                setTimeout(() => {
                    if (fixResponsive()) {
                        // 监听窗口大小变化
                        let resizeTimeout;
                        window.addEventListener('resize', () => {
                            clearTimeout(resizeTimeout);
                            resizeTimeout = setTimeout(fixResponsive, 100);
                        });
                        
                        window.addEventListener('orientationchange', () => {
                            setTimeout(fixResponsive, 200);
                        });
                    }
                }, 100);
                
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.error('❌ React应用加载超时');
            }
        }, 100);
    }
    
    // 页面加载完成后开始检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForReactApp);
    } else {
        waitForReactApp();
    }
    
})();
