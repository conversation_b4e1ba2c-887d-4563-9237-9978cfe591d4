/**
 * Button组件故事
 * 展示Button组件的各种变体和状态
 */

import type { Meta, StoryObj } from '@storybook/react'
import { Button } from './Button'

const meta: Meta<typeof Button> = {
  title: 'Common/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '通用按钮组件，支持多种变体和状态。'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost'],
      description: '按钮变体'
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: '按钮尺寸'
    },
    disabled: {
      control: 'boolean',
      description: '是否禁用'
    },
    loading: {
      control: 'boolean',
      description: '是否显示加载状态'
    },
    onClick: {
      action: 'clicked',
      description: '点击事件处理器'
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// 基础按钮
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: '主要按钮'
  }
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: '次要按钮'
  }
}

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: '轮廓按钮'
  }
}

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: '幽灵按钮'
  }
}

// 不同尺寸
export const Small: Story = {
  args: {
    size: 'sm',
    children: '小按钮'
  }
}

export const Medium: Story = {
  args: {
    size: 'md',
    children: '中等按钮'
  }
}

export const Large: Story = {
  args: {
    size: 'lg',
    children: '大按钮'
  }
}

// 状态变体
export const Disabled: Story = {
  args: {
    disabled: true,
    children: '禁用按钮'
  }
}

export const Loading: Story = {
  args: {
    loading: true,
    children: '加载中...'
  }
}

// 组合示例
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">主要</Button>
      <Button variant="secondary">次要</Button>
      <Button variant="outline">轮廓</Button>
      <Button variant="ghost">幽灵</Button>
      <Button disabled>禁用</Button>
      <Button loading>加载中</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '展示所有按钮变体的组合效果。'
      }
    }
  }
}
