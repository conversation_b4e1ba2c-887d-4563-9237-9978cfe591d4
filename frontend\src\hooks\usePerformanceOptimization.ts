/**
 * React性能优化Hook
 * 
 * 提供组件级别的性能优化工具和监控
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { performanceMonitor } from '../utils/PerformanceMonitor';

/**
 * 防抖Hook
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 节流Hook
 */
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef<number>(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
}

/**
 * 性能监控Hook
 */
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    
    performanceMonitor.addMetric({
      name: `component-render-${componentName}`,
      value: Date.now() - mountTime.current,
      timestamp: Date.now(),
      category: 'render',
      metadata: {
        componentName,
        renderCount: renderCount.current,
        isMount: renderCount.current === 1
      }
    });
  });

  const measureInteraction = useCallback((action: string, fn: () => void) => {
    performanceMonitor.measureUserInteraction(`${componentName}-${action}`, fn);
  }, [componentName]);

  return { measureInteraction, renderCount: renderCount.current };
}

/**
 * 智能重渲染优化Hook
 */
export function useSmartMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  componentName?: string
): T {
  return useMemo(() => {
    if (componentName) {
      return performanceMonitor.measureRender(`memo-${componentName}`, factory);
    }
    return factory();
  }, deps);
}

/**
 * 异步状态Hook（防止竞态条件）
 */
export function useAsyncState<T>(
  initialState: T
): [T, (newState: T | Promise<T>) => void, boolean] {
  const [state, setState] = useState<T>(initialState);
  const [loading, setLoading] = useState(false);
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const setAsyncState = useCallback((newState: T | Promise<T>) => {
    if (newState instanceof Promise) {
      setLoading(true);
      newState.then((resolvedState) => {
        if (mountedRef.current) {
          setState(resolvedState);
          setLoading(false);
        }
      }).catch((error) => {
        if (mountedRef.current) {
          console.error('Async state error:', error);
          setLoading(false);
        }
      });
    } else {
      setState(newState);
    }
  }, []);

  return [state, setAsyncState, loading];
}

/**
 * 虚拟化列表Hook
 */
export function useVirtualization<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    handleScroll,
    totalHeight: visibleItems.totalHeight,
    offsetY: visibleItems.offsetY
  };
}

/**
 * 懒加载Hook
 */
export function useLazyLoad<T>(
  loadFn: () => Promise<T>,
  deps: React.DependencyList = []
): [T | null, boolean, Error | null] {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let cancelled = false;

    const load = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await loadFn();
        
        if (!cancelled) {
          setData(result);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err instanceof Error ? err : new Error(String(err)));
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };

    load();

    return () => {
      cancelled = true;
    };
  }, deps);

  return [data, loading, error];
}

/**
 * 内存泄漏检测Hook
 */
export function useMemoryLeakDetection(componentName: string) {
  const timersRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const listenersRef = useRef<Array<{ element: EventTarget; event: string; handler: EventListener }>>(
    []
  );

  const addTimer = useCallback((timer: NodeJS.Timeout) => {
    timersRef.current.add(timer);
  }, []);

  const addListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener
  ) => {
    element.addEventListener(event, handler);
    listenersRef.current.push({ element, event, handler });
  }, []);

  useEffect(() => {
    return () => {
      // 清理定时器
      timersRef.current.forEach(timer => clearTimeout(timer));
      timersRef.current.clear();

      // 清理事件监听器
      listenersRef.current.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      listenersRef.current.length = 0;

      // 记录清理日志
      performanceMonitor.addMetric({
        name: `cleanup-${componentName}`,
        value: Date.now(),
        timestamp: Date.now(),
        category: 'memory',
        metadata: {
          componentName,
          timersCleared: timersRef.current.size,
          listenersCleared: listenersRef.current.length
        }
      });
    };
  }, [componentName]);

  return { addTimer, addListener };
}

/**
 * 批量状态更新Hook
 */
export function useBatchedUpdates<T extends Record<string, any>>(
  initialState: T
): [T, (updates: Partial<T>) => void, () => void] {
  const [state, setState] = useState<T>(initialState);
  const pendingUpdatesRef = useRef<Partial<T>>({});
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const batchUpdate = useCallback((updates: Partial<T>) => {
    pendingUpdatesRef.current = { ...pendingUpdatesRef.current, ...updates };

    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    updateTimeoutRef.current = setTimeout(() => {
      setState(prevState => ({ ...prevState, ...pendingUpdatesRef.current }));
      pendingUpdatesRef.current = {};
      updateTimeoutRef.current = null;
    }, 0);
  }, []);

  const flushUpdates = useCallback(() => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
      setState(prevState => ({ ...prevState, ...pendingUpdatesRef.current }));
      pendingUpdatesRef.current = {};
      updateTimeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  return [state, batchUpdate, flushUpdates];
}

/**
 * 组件可见性Hook（用于懒加载）
 */
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
): boolean {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, options]);

  return isVisible;
}
