/**
 * 插件页面对象模型
 * 
 * 封装插件管理页面的元素和操作
 * 
 * @since 2.0.0
 */

import { Page, Locator, expect } from '@playwright/test'
import { waitForReactApp, waitForNotification, clearNotifications } from '../fixtures/test-utils'

export class PluginPage {
  readonly page: Page
  
  // 页面元素
  readonly container: Locator
  readonly sidebar: Locator
  readonly content: Locator
  readonly loadingIndicator: Locator
  
  // 侧边栏导航
  readonly apiSettingsTab: Locator
  readonly syncTab: Locator
  readonly logsTab: Locator
  readonly performanceTab: Locator
  readonly settingsTab: Locator
  
  // 通知系统
  readonly notificationContainer: Locator
  readonly successNotification: Locator
  readonly errorNotification: Locator
  readonly warningNotification: Locator

  constructor(page: Page) {
    this.page = page
    
    // 主要容器
    this.container = page.locator('.notion-wp-admin')
    this.sidebar = page.locator('.notion-wp-sidebar')
    this.content = page.locator('.notion-wp-content')
    this.loadingIndicator = page.locator('.loading, [data-loading="true"]')
    
    // 侧边栏导航
    this.apiSettingsTab = page.locator('[data-tab="api-settings"]')
    this.syncTab = page.locator('[data-tab="sync"]')
    this.logsTab = page.locator('[data-tab="logs"]')
    this.performanceTab = page.locator('[data-tab="performance"]')
    this.settingsTab = page.locator('[data-tab="settings"]')
    
    // 通知系统
    this.notificationContainer = page.locator('.notification-container')
    this.successNotification = page.locator('.notification.success')
    this.errorNotification = page.locator('.notification.error')
    this.warningNotification = page.locator('.notification.warning')
  }

  /**
   * 导航到插件页面
   */
  async goto() {
    await this.page.goto('/wp-admin/admin.php?page=notion-to-wordpress')
    await waitForReactApp(this.page)
  }

  /**
   * 等待页面加载完成
   */
  async waitForLoad() {
    await waitForReactApp(this.page)
    await expect(this.container).toBeVisible()
  }

  /**
   * 切换到指定标签页
   */
  async switchToTab(tabName: 'api-settings' | 'sync' | 'logs' | 'performance' | 'settings') {
    const tabLocator = this.page.locator(`[data-tab="${tabName}"]`)
    await tabLocator.click()
    
    // 等待内容加载
    await this.page.waitForTimeout(500)
    
    // 验证标签页已激活
    await expect(tabLocator).toHaveClass(/active/)
  }

  /**
   * 等待并获取通知消息
   */
  async waitForNotification(type: 'success' | 'error' | 'warning' = 'success') {
    return await waitForNotification(this.page, type)
  }

  /**
   * 清除所有通知
   */
  async clearNotifications() {
    await clearNotifications(this.page)
  }

  /**
   * 检查是否有错误通知
   */
  async hasErrorNotification(): Promise<boolean> {
    return await this.errorNotification.isVisible()
  }

  /**
   * 检查是否有成功通知
   */
  async hasSuccessNotification(): Promise<boolean> {
    return await this.successNotification.isVisible()
  }

  /**
   * 获取当前活动的标签页
   */
  async getActiveTab(): Promise<string> {
    const activeTab = this.sidebar.locator('.active')
    const tabId = await activeTab.getAttribute('data-tab')
    return tabId || ''
  }

  /**
   * 检查页面是否正在加载
   */
  async isLoading(): Promise<boolean> {
    return await this.loadingIndicator.isVisible()
  }

  /**
   * 等待加载完成
   */
  async waitForLoadingComplete() {
    await expect(this.loadingIndicator).toBeHidden({ timeout: 30000 })
  }

  /**
   * 获取页面标题
   */
  async getPageTitle(): Promise<string> {
    const titleElement = this.page.locator('.notion-wp-header h1')
    return await titleElement.textContent() || ''
  }

  /**
   * 检查侧边栏是否可见
   */
  async isSidebarVisible(): Promise<boolean> {
    return await this.sidebar.isVisible()
  }

  /**
   * 检查响应式布局
   */
  async checkResponsiveLayout() {
    const viewport = this.page.viewportSize()
    
    if (viewport && viewport.width < 768) {
      // 移动端布局检查
      await expect(this.container).toHaveClass(/mobile/)
    } else if (viewport && viewport.width < 1024) {
      // 平板端布局检查
      await expect(this.container).toHaveClass(/tablet/)
    } else {
      // 桌面端布局检查
      await expect(this.sidebar).toBeVisible()
    }
  }

  /**
   * 截图当前页面
   */
  async takeScreenshot(name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `plugin-page-${name}-${timestamp}.png`
    
    await this.page.screenshot({
      path: `test-results/screenshots/${filename}`,
      fullPage: true
    })
    
    return filename
  }

  /**
   * 检查页面性能
   */
  async checkPerformance() {
    const metrics = await this.page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
      }
    })
    
    // 性能断言
    expect(metrics.totalLoadTime).toBeLessThan(10000) // 总加载时间小于10秒
    expect(metrics.domContentLoaded).toBeLessThan(5000) // DOM加载时间小于5秒
    
    return metrics
  }

  /**
   * 模拟网络错误
   */
  async simulateNetworkError() {
    await this.page.route('**/wp-admin/admin-ajax.php', route => {
      route.abort('failed')
    })
  }

  /**
   * 恢复网络连接
   */
  async restoreNetwork() {
    await this.page.unroute('**/wp-admin/admin-ajax.php')
  }

  /**
   * 检查无障碍访问
   */
  async checkAccessibility() {
    // 检查基本的无障碍属性
    const focusableElements = this.page.locator('button, input, select, textarea, a[href]')
    const count = await focusableElements.count()
    
    for (let i = 0; i < Math.min(count, 10); i++) {
      const element = focusableElements.nth(i)
      
      // 检查是否可以获得焦点
      await element.focus()
      await expect(element).toBeFocused()
    }
    
    // 检查ARIA标签
    const ariaElements = this.page.locator('[aria-label], [aria-labelledby], [role]')
    const ariaCount = await ariaElements.count()
    
    expect(ariaCount).toBeGreaterThan(0) // 应该有ARIA标签
  }

  /**
   * 获取控制台错误
   */
  getConsoleErrors() {
    const errors: string[] = []
    
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    return errors
  }
}
