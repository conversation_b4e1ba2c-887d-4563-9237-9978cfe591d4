/**
 * Playwright全局清理
 * 
 * 在所有测试运行后执行的清理工作
 * 
 * @since 2.0.0
 */

import { FullConfig } from '@playwright/test'
import fs from 'fs'
import path from 'path'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始E2E测试全局清理...')
  
  try {
    // 1. 清理测试数据文件
    const testDataDir = path.join(__dirname, '../test-data')
    if (fs.existsSync(testDataDir)) {
      const files = fs.readdirSync(testDataDir)
      files.forEach(file => {
        if (file.startsWith('temp-') || file.includes('test-run-')) {
          const filePath = path.join(testDataDir, file)
          fs.unlinkSync(filePath)
          console.log(`🗑️ 删除临时文件: ${file}`)
        }
      })
    }
    
    // 2. 生成测试报告摘要
    await generateTestSummary()
    
    // 3. 清理认证文件（可选，保留用于调试）
    // const authFile = path.join(__dirname, 'admin-auth.json')
    // if (fs.existsSync(authFile)) {
    //   fs.unlinkSync(authFile)
    //   console.log('🗑️ 认证文件已清理')
    // }
    
    console.log('✅ 全局清理完成')
    
  } catch (error) {
    console.error('❌ 全局清理失败:', error)
    // 不抛出错误，避免影响测试结果
  }
}

/**
 * 生成测试报告摘要
 */
async function generateTestSummary() {
  try {
    const resultsFile = path.join(process.cwd(), 'test-results/results.json')
    
    if (!fs.existsSync(resultsFile)) {
      console.log('📊 未找到测试结果文件，跳过报告生成')
      return
    }
    
    const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'))
    
    const summary = {
      timestamp: new Date().toISOString(),
      config: {
        projects: results.config?.projects?.length || 0,
        workers: results.config?.workers || 1,
        timeout: results.config?.timeout || 30000
      },
      stats: {
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0
      },
      suites: results.suites?.map((suite: any) => ({
        title: suite.title,
        file: suite.file,
        tests: suite.tests?.length || 0,
        passed: suite.tests?.filter((t: any) => t.outcome === 'passed').length || 0,
        failed: suite.tests?.filter((t: any) => t.outcome === 'failed').length || 0
      })) || []
    }
    
    // 保存摘要
    const summaryFile = path.join(process.cwd(), 'test-results/summary.json')
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2))
    
    // 生成简单的文本报告
    const textReport = generateTextReport(summary)
    const textReportFile = path.join(process.cwd(), 'test-results/summary.txt')
    fs.writeFileSync(textReportFile, textReport)
    
    console.log('📊 测试报告摘要已生成')
    console.log(`📈 测试结果: ${summary.stats.passed}/${summary.stats.total} 通过`)
    
  } catch (error) {
    console.warn('⚠️ 生成测试报告摘要失败:', error.message)
  }
}

/**
 * 生成文本格式的测试报告
 */
function generateTextReport(summary: any): string {
  const { stats, suites, timestamp } = summary
  
  let report = `
# E2E测试报告摘要

## 测试概览
- 执行时间: ${new Date(timestamp).toLocaleString('zh-CN')}
- 总测试数: ${stats.total}
- 通过: ${stats.passed} ✅
- 失败: ${stats.failed} ❌
- 跳过: ${stats.skipped} ⏭️
- 执行时长: ${Math.round(stats.duration / 1000)}秒

## 通过率
${Math.round((stats.passed / stats.total) * 100)}% (${stats.passed}/${stats.total})

## 测试套件详情
`

  suites.forEach((suite: any) => {
    const passRate = suite.tests > 0 ? Math.round((suite.passed / suite.tests) * 100) : 0
    report += `
### ${suite.title}
- 文件: ${suite.file}
- 测试数: ${suite.tests}
- 通过: ${suite.passed}
- 失败: ${suite.failed}
- 通过率: ${passRate}%
`
  })

  if (stats.failed > 0) {
    report += `
## ⚠️ 注意事项
- 有 ${stats.failed} 个测试失败
- 请查看详细的HTML报告: test-results/html-report/index.html
- 检查失败的截图和视频: test-results/artifacts/
`
  } else {
    report += `
## 🎉 恭喜！
所有测试都通过了！系统功能正常。
`
  }

  report += `
## 报告文件
- HTML报告: test-results/html-report/index.html
- JSON结果: test-results/results.json
- JUnit XML: test-results/junit.xml

---
生成时间: ${new Date(timestamp).toLocaleString('zh-CN')}
`

  return report
}

export default globalTeardown
