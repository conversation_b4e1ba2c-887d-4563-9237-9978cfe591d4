/**
 * Playwright E2E测试配置
 * 
 * 配置端到端测试环境和参数
 * 
 * @since 2.0.0
 */

import { defineConfig, devices } from '@playwright/test'

/**
 * 从环境变量读取配置
 */
const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://frankloong.local'
const headless = process.env.CI ? true : false
const workers = process.env.CI ? 1 : undefined

export default defineConfig({
  // 测试目录
  testDir: './tests/e2e',
  
  // 全局测试超时时间
  timeout: 60000,
  
  // 期望超时时间
  expect: {
    timeout: 10000,
  },
  
  // 失败时重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行工作进程数
  workers: workers,
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    process.env.CI ? ['github'] : ['list']
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: baseURL,
    
    // 浏览器上下文选项
    viewport: { width: 1280, height: 720 },
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 截图设置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 追踪设置
    trace: 'retain-on-failure',
    
    // 用户代理
    userAgent: 'Playwright E2E Tests',
    
    // 额外的HTTP头
    extraHTTPHeaders: {
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    },
    
    // 等待策略
    actionTimeout: 15000,
    navigationTimeout: 30000,
  },

  // 项目配置 - 不同浏览器和设备
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // WordPress管理员登录状态
        storageState: 'tests/fixtures/admin-auth.json'
      },
    },
    
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        storageState: 'tests/fixtures/admin-auth.json'
      },
    },
    
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        storageState: 'tests/fixtures/admin-auth.json'
      },
    },
    
    // 移动端测试
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 5'],
        storageState: 'tests/fixtures/admin-auth.json'
      },
    },
    
    {
      name: 'mobile-safari',
      use: { 
        ...devices['iPhone 12'],
        storageState: 'tests/fixtures/admin-auth.json'
      },
    },
  ],

  // 全局设置和清理
  globalSetup: require.resolve('./tests/fixtures/global-setup.ts'),
  globalTeardown: require.resolve('./tests/fixtures/global-teardown.ts'),

  // Web服务器配置（用于开发环境）
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 5173,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },

  // 输出目录
  outputDir: 'test-results/artifacts',
  
  // 测试匹配模式
  testMatch: [
    '**/*.spec.ts',
    '**/*.test.ts'
  ],
  
  // 忽略的文件
  testIgnore: [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**'
  ],
  
  // 元数据
  metadata: {
    'test-environment': 'local-wordpress',
    'base-url': baseURL,
    'wordpress-version': '6.4+',
    'plugin-version': '2.0.0-beta.1'
  }
})
