import React, { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { useSyncStore } from '../../stores/syncStore'
import { useUIStore } from '../../stores/uiStore'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Loading } from '../Common'
import { DatabaseOptimizer } from './DatabaseOptimizer'
import { getApiService } from '../../services/api'
import { useI18n } from '../../hooks/useI18n'
import { useDebounce, useRenderPerformance, useMemoryLeakDetection } from '../../utils/performance'
import { performanceMonitor } from '../../utils/codeQuality'
import {
  CpuChipIcon,
  ServerIcon,
  ClockIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  QueueListIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import type { SystemInfo, PerformanceMetrics, QueueStatus } from '../../types'

/**
 * 性能监控组件
 *
 * 使用React.memo优化重渲染性能
 * 集成性能监控和内存泄漏检测
 */
export const PerformanceMonitor: React.FC = memo(() => {
  // 性能监控和内存泄漏检测
  useRenderPerformance('PerformanceMonitor')
  const { safeSetTimeout, safeSetInterval, cleanup } = useMemoryLeakDetection('PerformanceMonitor')

  // Store hooks - 使用选择器优化
  const { sseConnected, progress } = useSyncStore(
    useCallback(state => ({
      sseConnected: state.sseConnected,
      progress: state.progress
    }), [])
  )
  const { showSuccess, showError } = useUIStore(
    useCallback(state => ({
      showSuccess: state.showSuccess,
      showError: state.showError
    }), [])
  )
  const { settings } = useSettingsStore(
    useCallback(state => ({ settings: state.settings }), [])
  )
  const { __ } = useI18n()

  // 状态管理
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null)
  const [queueStatus, setQueueStatus] = useState<QueueStatus | null>(null)
  const [asyncStatus, setAsyncStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  const [performanceRecommendations, setPerformanceRecommendations] = useState<string[]>([])
  const [showAdvancedMetrics, setShowAdvancedMetrics] = useState(false)

  // 获取API服务实例 - 使用useMemo缓存
  const apiService = useMemo(() => getApiService(), [])

  // 防抖的数据获取函数
  const debouncedFetchSystemInfo = useDebounce(
    useCallback(async () => {
      const startTime = performance.now()
      performanceMonitor.recordComponentRender('PerformanceMonitor', startTime)

      setIsLoading(true)
      try {
        // 并行获取所有数据，使用Promise.allSettled确保部分失败不影响其他数据
        const [systemInfoResponse, performanceResponse, queueResponse, asyncResponse] = await Promise.allSettled([
          apiService.getSystemInfo(),
          apiService.getPerformanceMetrics(),
          apiService.getQueueStatus(),
          apiService.getAsyncStatus()
        ])

      // 处理系统信息
      if (systemInfoResponse.status === 'fulfilled') {
        setSystemInfo(systemInfoResponse.value)
      }

      // 处理性能指标
      if (performanceResponse.status === 'fulfilled') {
        const perfData = performanceResponse.value
        const metrics: PerformanceMetrics = {
          enhanced_fetch_time: perfData.memory_usage?.current || 0,
          enhanced_fetch_count: perfData.api_calls?.total || 0,
          enhanced_fetch_memory: perfData.memory_usage?.peak || 0,
          memory_usage: perfData.memory_usage?.current || 0,
          peak_memory: perfData.memory_usage?.peak || 0,
          execution_time: perfData.execution_time || 0
        }
        setPerformanceMetrics(metrics)
      }

      // 处理队列状态
      if (queueResponse.status === 'fulfilled') {
        setQueueStatus(queueResponse.value)
      }

      // 处理异步状态
      if (asyncResponse.status === 'fulfilled') {
        setAsyncStatus(asyncResponse.value)
      }

        // 生成性能建议
        generatePerformanceRecommendations()

        setLastRefresh(new Date())

        // 记录性能指标
        const endTime = performance.now()
        const fetchTime = endTime - startTime
        performanceMonitor.recordComponentRender('PerformanceMonitor-fetch', fetchTime)

        showSuccess(__('System information updated'), __('System information has been successfully refreshed'))
      } catch (error) {
        console.error(__('Failed to fetch system information'), error)
        showError(__('Failed to fetch system information'), __('Please check your network connection and try again'))
      } finally {
        setIsLoading(false)
      }
    }, [apiService, showSuccess, showError, __, generatePerformanceRecommendations]),
    300 // 300ms防抖延迟
  )

  // 为了向后兼容，保留原函数名
  const fetchSystemInfo = debouncedFetchSystemInfo

  // 使用useMemo缓存性能建议计算
  const memoizedRecommendations = useMemo(() => {
    const recommendations: string[] = []

    if (systemInfo && performanceMetrics) {
      // 内存使用建议
      const memoryUsagePercent = (performanceMetrics.memory_usage / (systemInfo.memory_limit_bytes || 134217728)) * 100
      if (memoryUsagePercent > 80) {
        recommendations.push(__('Memory usage is high ({{percent}}%). Consider increasing memory limit or optimizing queries.', { percent: memoryUsagePercent.toFixed(1) }))
      }

      // API性能建议
      if (performanceMetrics.enhanced_fetch_time > 5000) {
        recommendations.push(__('API response time is slow ({{time}}ms). Consider reducing API page size or enabling caching.', { time: performanceMetrics.enhanced_fetch_time }))
      }

      // 并发请求建议
      if (settings?.concurrent_requests && settings.concurrent_requests > 5 && memoryUsagePercent > 60) {
        recommendations.push(__('High concurrent requests with limited memory. Consider reducing concurrent requests to {{recommended}}.', { recommended: Math.max(2, Math.floor(settings.concurrent_requests * 0.7)) }))
      }

      // 队列状态建议
      if (queueStatus && queueStatus.pending_count > 100) {
        recommendations.push(__('Large queue detected ({{count}} items). Consider enabling async processing or increasing batch size.', { count: queueStatus.pending_count }))
      }
    }

    return recommendations
  }, [systemInfo, performanceMetrics, queueStatus, settings, __])

  // 生成性能建议函数
  const generatePerformanceRecommendations = useCallback(() => {
    setPerformanceRecommendations(memoizedRecommendations)
  }, [memoizedRecommendations])

  const clearCache = async () => {
    setIsLoading(true)
    try {
      await apiService.clearCache()
      showSuccess(__('Cache cleared'), __('System cache has been successfully cleared'))
      await fetchSystemInfo() // 刷新数据
    } catch (error) {
      console.error(__('Failed to clear cache'), error)
      showError(__('Failed to clear cache'), __('Please try again later'))
    } finally {
      setIsLoading(false)
    }
  }

  const optimizeDatabase = async () => {
    setIsLoading(true)
    try {
      await apiService.optimizeDatabase()
      showSuccess(__('Database optimization completed'), __('Database has been successfully optimized'))
      await fetchSystemInfo() // 刷新数据
    } catch (error) {
      console.error(__('Database optimization failed'), error)
      showError(__('Database optimization failed'), __('Please try again later'))
    } finally {
      setIsLoading(false)
    }
  }

  // 重置性能统计
  const resetPerformanceStats = async () => {
    setIsLoading(true)
    try {
      await apiService.resetPerformanceStats()
      showSuccess(__('Performance statistics reset'), __('Performance statistics have been successfully reset'))
      await fetchSystemInfo()
    } catch (error) {
      console.error(__('Failed to reset performance statistics'), error)
      showError(__('Failed to reset performance statistics'), __('Please try again later'))
    } finally {
      setIsLoading(false)
    }
  }

  // 组件挂载时获取数据
  useEffect(() => {
    performanceMonitor.recordComponentMount('PerformanceMonitor')
    fetchSystemInfo()

    return () => {
      performanceMonitor.recordComponentUnmount('PerformanceMonitor')
      cleanup()
    }
  }, [fetchSystemInfo, cleanup])

  // 自动刷新逻辑 - 使用安全的定时器
  useEffect(() => {
    if (autoRefresh) {
      const interval = safeSetInterval(() => {
        fetchSystemInfo()
      }, 10000) // 10秒自动刷新

      return () => clearInterval(interval)
    }
  }, [autoRefresh, fetchSystemInfo, safeSetInterval])

  // 使用useMemo缓存格式化函数
  const formatMemorySize = useMemo(() => {
    return (bytes: number): string => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }, [])

  const formatDuration = useMemo(() => {
    return (ms: number): string => {
      if (ms < 1000) return `${ms.toFixed(0)}ms`
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
      return `${(ms / 60000).toFixed(1)}min`
    }
  }, [])

  const getStatusColor = useMemo(() => {
    return (status: string): string => {
      switch (status) {
        case 'running': case 'active': return 'text-green-600 bg-green-50'
        case 'idle': case 'completed': return 'text-blue-600 bg-blue-50'
        case 'error': case 'failed': return 'text-red-600 bg-red-50'
        case 'pending': case 'waiting': return 'text-yellow-600 bg-yellow-50'
        default: return 'text-gray-600 bg-gray-50'
      }
    }
  }, [])

  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
              <ChartBarIcon className="h-6 w-6 mr-2 text-blue-500" />
              {__('Performance Monitor')}
            </h2>
            <p className="text-sm text-gray-600">
              {__('Real-time monitoring of system performance and sync status')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {lastRefresh && (
              <span className="text-xs text-gray-500">
                {__('Last updated')}: {lastRefresh.toLocaleTimeString()}
              </span>
            )}
            <Button
              variant="secondary"
              size="sm"
              onClick={fetchSystemInfo}
              loading={isLoading}
              disabled={isLoading}
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              {isLoading ? __('Refreshing...') : __('Refresh')}
            </Button>
            <Button
              variant={autoRefresh ? "primary" : "secondary"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              <ClockIcon className="h-4 w-4 mr-1" />
              {autoRefresh ? __('Auto Refresh On') : __('Auto Refresh Off')}
            </Button>
          </div>
        </div>
      </div>

      {/* 系统信息概览 */}
      {systemInfo && (
        <Card
          title={`🖥️ ${__('System Information')}`}
          subtitle={__('Current system environment and resource status')}
          shadow="md"
        >
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <ServerIcon className="h-6 w-6 mx-auto text-blue-500 mb-2" />
                <div className="text-lg font-semibold text-blue-600">
                  {systemInfo.php_version || 'N/A'}
                </div>
                <div className="text-sm text-blue-700">{__('PHP Version')}</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <CpuChipIcon className="h-6 w-6 mx-auto text-green-500 mb-2" />
                <div className="text-lg font-semibold text-green-600">
                  {formatMemorySize(systemInfo.memory_limit_bytes || 0)}
                </div>
                <div className="text-sm text-green-700">{__('Memory Limit')}</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-semibold text-purple-600">
                  {formatMemorySize(performanceMetrics?.memory_usage || 0)}
                </div>
                <div className="text-sm text-purple-700">{__('Current Memory')}</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-lg font-semibold text-yellow-600">
                  {formatMemorySize(performanceMetrics?.peak_memory || 0)}
                </div>
                <div className="text-sm text-yellow-700">{__('Peak Memory')}</div>
              </div>
            </div>

            {/* 连接状态 */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{__('SSE Connection')}:</span>
                  <span className={`text-sm font-medium px-2 py-1 rounded ${
                    sseConnected ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                  }`}>
                    {sseConnected ? __('Connected') : __('Disconnected')}
                  </span>
                </div>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{__('WordPress Version')}:</span>
                  <span className="text-sm font-medium text-gray-600">
                    {systemInfo.wp_version || 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 当前配置 */}
      <Card
        title={`⚙️ ${__('Current Configuration')}`}
        subtitle={__('View current performance configuration parameters')}
        shadow="md"
      >
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-semibold text-blue-600">
                {settings?.api_page_size || 100}
              </div>
              <div className="text-sm text-blue-700">{__('API Page Size')}</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-semibold text-green-600">
                {settings?.concurrent_requests || 5}
              </div>
              <div className="text-sm text-green-700">{__('Concurrent Requests')}</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-lg font-semibold text-purple-600">
                {settings?.batch_size || 20}
              </div>
              <div className="text-sm text-purple-700">{__('Batch Size')}</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-lg font-semibold text-yellow-600">
                {settings?.log_buffer_size || 50}
              </div>
              <div className="text-sm text-yellow-700">{__('Log Buffer Size')}</div>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{__('Performance Mode')}:</span>
                <span className={`text-sm font-medium px-2 py-1 rounded ${
                  settings?.enable_performance_mode
                    ? 'text-green-600 bg-green-100'
                    : 'text-red-600 bg-red-100'
                }`}>
                  {settings?.enable_performance_mode ? __('Enabled') : __('Disabled')}
                </span>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{__('Performance Level')}:</span>
                <span className="text-sm font-medium text-gray-600">
                  {settings?.performance_level || __('Not Set')}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card shadow="sm" className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">连接状态</p>
                <p className="text-lg font-semibold text-green-600">
                  {sseConnected ? '已连接' : '未连接'}
                </p>
              </div>
              <div className="text-2xl">🔗</div>
            </div>
          </CardContent>
        </Card>

        <Card shadow="sm" className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">同步进度</p>
                <p className="text-lg font-semibold text-blue-600">
                  {progress || 0}%
                </p>
              </div>
              <div className="text-2xl">⚡</div>
            </div>
          </CardContent>
        </Card>

        <Card shadow="sm" className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">总同步数</p>
                <p className="text-lg font-semibold text-purple-600">
                  0
                </p>
              </div>
              <div className="text-2xl">📈</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 系统信息 */}
      <Card
        title="系统信息"
        subtitle="查看当前系统状态和配置信息"
        shadow="md"
      >
        <CardContent className="space-y-4">
          {isLoading && !systemInfo ? (
            <Loading text="正在加载系统信息..." />
          ) : systemInfo ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">PHP 版本</h4>
                <p className="text-sm text-gray-600">{systemInfo.php_version}</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">内存限制</h4>
                <p className="text-sm text-gray-600">
                  {systemInfo.memory_limit}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">最大执行时间</h4>
                <p className="text-sm text-gray-600">{systemInfo.max_execution_time}</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">WordPress 版本</h4>
                <p className="text-sm text-gray-600">{systemInfo.wp_version}</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">插件版本</h4>
                <p className="text-sm text-gray-600">{systemInfo.plugin_version}</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">当前时间</h4>
                <p className="text-sm text-gray-600">{systemInfo.current_time}</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">无法获取系统信息</p>
          )}

          <div className="flex flex-wrap gap-2 pt-4 border-t">
            <Button
              variant="primary"
              onClick={fetchSystemInfo}
              loading={isLoading}
              disabled={isLoading}
            >
              刷新信息
            </Button>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">自动刷新 (30秒)</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* 异步处理状态 */}
      {(asyncStatus || queueStatus) && (
        <Card
          title={`🔄 ${__('Async Processing Status')}`}
          subtitle={__('Real-time monitoring of background tasks and queue status')}
          shadow="md"
        >
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 异步状态 */}
              {asyncStatus && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <BoltIcon className="h-4 w-4 mr-2" />
                    {__('Async Engine Status')}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-600">{__('Status')}:</span>
                      <span className={`text-sm font-medium px-2 py-1 rounded ${getStatusColor(asyncStatus.status || 'idle')}`}>
                        {asyncStatus.status || 'idle'}
                      </span>
                    </div>
                    {asyncStatus.operation && (
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm text-gray-600">{__('Current Operation')}:</span>
                        <span className="text-sm font-medium text-gray-800">{asyncStatus.operation}</span>
                      </div>
                    )}
                    {asyncStatus.progress !== undefined && (
                      <div className="p-2 bg-gray-50 rounded">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-600">{__('Progress')}:</span>
                          <span className="text-sm font-medium text-gray-800">{asyncStatus.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${asyncStatus.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 队列状态 */}
              {queueStatus && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <QueueListIcon className="h-4 w-4 mr-2" />
                    {__('Queue Status')}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-600">{__('Pending Tasks')}:</span>
                      <span className="text-sm font-medium text-gray-800">{queueStatus.pending_count || 0}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-600">{__('Processing Tasks')}:</span>
                      <span className="text-sm font-medium text-gray-800">{queueStatus.processing_count || 0}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-600">{__('Completed Tasks')}:</span>
                      <span className="text-sm font-medium text-gray-800">{queueStatus.completed_count || 0}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-600">{__('Failed Tasks')}:</span>
                      <span className="text-sm font-medium text-gray-800">{queueStatus.failed_count || 0}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 性能指标 */}
      <Card
        title={`📈 ${__('Performance Metrics')}`}
        subtitle={__('View detailed performance statistics')}
        shadow="md"
      >
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">{__('Performance Statistics')}</h4>
            <div className="flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowAdvancedMetrics(!showAdvancedMetrics)}
              >
                {showAdvancedMetrics ? __('Hide Advanced') : __('Show Advanced')}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={resetPerformanceStats}
                loading={isLoading}
              >
                {__('Reset Stats')}
              </Button>
            </div>
          </div>

          {performanceMetrics ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {formatDuration(performanceMetrics.enhanced_fetch_time)}
                </div>
                <div className="text-sm text-blue-700">{__('API Response Time')}</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {performanceMetrics.enhanced_fetch_count}
                </div>
                <div className="text-sm text-green-700">{__('API Calls Count')}</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {formatMemorySize(performanceMetrics.enhanced_fetch_memory)}
                </div>
                <div className="text-sm text-red-700">{__('API Memory Usage')}</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {formatMemorySize(performanceMetrics.peak_memory)}
                </div>
                <div className="text-sm text-purple-700">{__('Peak Memory')}</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {formatMemorySize(performanceMetrics.memory_usage)}
                </div>
                <div className="text-sm text-yellow-700">{__('Current Memory')}</div>
              </div>
              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="text-2xl font-bold text-indigo-600">
                  {formatDuration(performanceMetrics.execution_time * 1000)}
                </div>
                <div className="text-sm text-indigo-700">{__('Execution Time')}</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">{__('Unable to retrieve performance metrics')}</p>
              <Button
                variant="secondary"
                size="sm"
                onClick={fetchSystemInfo}
                className="mt-2"
              >
                {__('Retry')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 性能建议 */}
      {performanceRecommendations.length > 0 && (
        <Card
          title={`💡 ${__('Performance Recommendations')}`}
          subtitle={__('AI-generated suggestions to improve system performance')}
          shadow="md"
        >
          <CardContent>
            <div className="space-y-3">
              {performanceRecommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-yellow-800">{recommendation}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 维护工具 */}
      <Card
        title={`🔧 ${__('Maintenance Tools')}`}
        subtitle={__('System maintenance and optimization tools')}
        shadow="md"
      >
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="secondary"
              onClick={clearCache}
              loading={isLoading}
              disabled={isLoading}
              className="w-full"
            >
              {__('Clear Cache')}
            </Button>
            <Button
              variant="secondary"
              onClick={optimizeDatabase}
              loading={isLoading}
              disabled={isLoading}
              className="w-full"
            >
              {__('Optimize Database')}
            </Button>
            <Button
              variant="secondary"
              onClick={resetPerformanceStats}
              loading={isLoading}
              disabled={isLoading}
              className="w-full"
            >
              {__('Reset Performance Stats')}
            </Button>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
              {__('Maintenance Tips')}
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• {__('Clearing cache may temporarily affect performance, recommended during off-peak hours')}</li>
              <li>• {__('Database optimization may take a long time, please be patient')}</li>
              <li>• {__('Regular maintenance operations are recommended to maintain optimal performance')}</li>
              <li>• {__('Performance statistics reset will clear all historical data')}</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 数据库索引优化 */}
      <DatabaseOptimizer />
    </div>
  )
})

// 设置displayName用于调试
PerformanceMonitor.displayName = 'PerformanceMonitor'