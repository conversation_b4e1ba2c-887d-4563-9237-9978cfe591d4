<?php
declare(strict_types=1);

/**
 * 插件主后台页面 - React版本
 * 
 * 此文件负责渲染React应用的挂载点和必要的配置数据。
 * 所有UI渲染都由React应用处理。
 * 
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问本文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

// 为内联脚本生成 nonce
$script_nonce = wp_create_nonce('notion_wp_script_nonce');

?>
<div class="wrap notion-wp-admin-react">
    <?php settings_errors('notion_wp_messages'); ?>
    
    <!-- React应用挂载点 -->
    <div id="notion-to-wordpress-react-root" class="notion-to-wordpress-react-app"></div>
    
    <!-- 加载指示器 -->
    <div id="notion-wp-loading" class="notion-wp-loading" style="display: none;">
        <div class="notion-wp-spinner">
            <div class="spinner"></div>
            <p><?php esc_html_e('正在加载应用...', 'notion-to-wordpress'); ?></p>
        </div>
    </div>
    
    <!-- 错误回退 -->
    <div id="notion-wp-fallback" class="notion-wp-fallback" style="display: none;">
        <div class="notice notice-error">
            <p>
                <strong><?php esc_html_e('React应用加载失败', 'notion-to-wordpress'); ?></strong><br>
                <?php esc_html_e('请刷新页面重试，或检查浏览器控制台获取更多信息。', 'notion-to-wordpress'); ?>
            </p>
            <p>
                <button type="button" class="button button-primary" onclick="location.reload();">
                    <?php esc_html_e('刷新页面', 'notion-to-wordpress'); ?>
                </button>
            </p>
        </div>
    </div>
</div>

<style>
.notion-wp-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    flex-direction: column;
}

.notion-wp-spinner .spinner {
    float: none;
    margin: 0 auto 20px;
}

.notion-wp-fallback {
    margin: 20px 0;
}

/* 确保React应用正常显示 */
.notion-wp-admin-react .notion-wp-app {
    display: block !important;
}

/* 隐藏传统UI元素（如果存在） */
.notion-wp-admin:not(.notion-wp-admin-react) .notion-wp-sidebar,
.notion-wp-admin:not(.notion-wp-admin-react) .notion-wp-header {
    display: none !important;
}
</style>

<script nonce="<?php echo esc_attr($script_nonce); ?>">
(function() {
    'use strict';
    
    // 显示加载指示器
    const loading = document.getElementById('notion-wp-loading');
    const fallback = document.getElementById('notion-wp-fallback');
    const reactRoot = document.getElementById('notion-to-wordpress-react-root');
    
    if (loading) {
        loading.style.display = 'flex';
    }
    
    // 检查React应用是否成功加载
    let checkCount = 0;
    const maxChecks = 50; // 5秒超时
    
    const checkReactApp = function() {
        checkCount++;
        
        if (reactRoot && reactRoot.children.length > 0) {
            // React应用已加载
            if (loading) loading.style.display = 'none';
            console.log('✅ React应用加载成功');
            return;
        }
        
        if (checkCount >= maxChecks) {
            // 超时，显示错误回退
            if (loading) loading.style.display = 'none';
            if (fallback) fallback.style.display = 'block';
            console.error('❌ React应用加载超时');
            return;
        }
        
        // 继续检查
        setTimeout(checkReactApp, 100);
    };
    
    // 开始检查
    setTimeout(checkReactApp, 100);
})();
</script>

<!-- 原版后台复刻脚本 -->
<script nonce="<?php echo esc_attr($script_nonce); ?>">
<?php include plugin_dir_path(__FILE__) . '../../original-backend-replica.js'; ?>
</script>
