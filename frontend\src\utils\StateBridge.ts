/**
 * 状态桥接器 - jQuery与React状态同步
 * 
 * 提供jQuery和Zustand状态管理之间的双向同步机制
 * 确保在混合技术栈环境中状态的一致性
 * 
 * 特性：
 * - 双向状态同步
 * - 冲突检测和解决
 * - 性能优化（防抖/节流）
 * - 调试和监控
 * - 自动清理机制
 */

import { debounce, throttle } from 'lodash';
import { performanceMonitor } from './PerformanceMonitor';
import { useSyncStore } from '../stores/syncStore';
import { useSettingsStore } from '../stores/settingsStore';
import { useUIStore } from '../stores/uiStore';

// 状态同步事件类型
export type StateSyncEvent = 
  | 'sync-status-changed'
  | 'settings-updated'
  | 'ui-state-changed'
  | 'form-validation-changed'
  | 'notification-added'
  | 'modal-opened'
  | 'modal-closed';

// 状态同步数据接口
export interface StateSyncData {
  source: 'jquery' | 'react';
  event: StateSyncEvent;
  data: any;
  timestamp: number;
  id: string;
}

// 状态桥接器配置
export interface StateBridgeConfig {
  enableDebug: boolean;
  syncDelay: number;
  conflictResolution: 'jquery-priority' | 'react-priority' | 'timestamp-priority';
  enablePerformanceMonitoring: boolean;
}

// 默认配置
const defaultConfig: StateBridgeConfig = {
  enableDebug: process.env.NODE_ENV === 'development',
  syncDelay: 100,
  conflictResolution: 'timestamp-priority',
  enablePerformanceMonitoring: true,
};

/**
 * 状态桥接器类
 * 管理jQuery和React之间的状态同步
 */
export class StateBridge {
  private config: StateBridgeConfig;
  private eventListeners: Map<StateSyncEvent, Set<Function>> = new Map();
  private syncQueue: StateSyncData[] = [];
  private isProcessing = false;
  private performanceMetrics: Map<string, number> = new Map();
  private cleanupFunctions: Function[] = [];
  private debouncedProcessQueue: ReturnType<typeof debounce>;
  private throttledStateSync: ReturnType<typeof throttle>;
  private lastSyncTime: number = 0;
  private syncBatchSize: number = 10;
  private maxQueueSize: number = 100;

  constructor(config: Partial<StateBridgeConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.debouncedProcessQueue = debounce(() => {
      this.processQueue();
    }, this.config.syncDelay);

    // 性能优化：节流状态同步
    this.throttledStateSync = throttle((data: StateSyncData) => {
      this.performStateSync(data);
    }, Math.max(this.config.syncDelay / 2, 50));

    this.initialize();
  }

  /**
   * 初始化状态桥接器
   */
  private initialize(): void {
    this.setupJQueryListeners();
    this.setupReactListeners();
    this.setupGlobalErrorHandling();
    
    if (this.config.enableDebug) {
      console.log('🌉 [StateBridge] 状态桥接器已初始化', this.config);
    }
  }

  /**
   * 设置jQuery事件监听器
   */
  private setupJQueryListeners(): void {
    // 监听jQuery的同步状态变化
    const handleJQuerySyncStatus = (event: any, data: any) => {
      this.queueSync({
        source: 'jquery',
        event: 'sync-status-changed',
        data,
        timestamp: Date.now(),
        id: this.generateId(),
      });
    };

    // 监听jQuery的设置更新
    const handleJQuerySettingsUpdate = (event: any, data: any) => {
      this.queueSync({
        source: 'jquery',
        event: 'settings-updated',
        data,
        timestamp: Date.now(),
        id: this.generateId(),
      });
    };

    // 监听jQuery的表单验证
    const handleJQueryFormValidation = (event: any, data: any) => {
      this.queueSync({
        source: 'jquery',
        event: 'form-validation-changed',
        data,
        timestamp: Date.now(),
        id: this.generateId(),
      });
    };

    // 绑定jQuery事件（如果jQuery可用）
    if (typeof window !== 'undefined' && (window as any).jQuery) {
      const $ = (window as any).jQuery;
      
      $(document).on('notion-wp:sync-status-changed', handleJQuerySyncStatus);
      $(document).on('notion-wp:settings-updated', handleJQuerySettingsUpdate);
      $(document).on('notion-wp:form-validation-changed', handleJQueryFormValidation);

      // 添加清理函数
      this.cleanupFunctions.push(() => {
        $(document).off('notion-wp:sync-status-changed', handleJQuerySyncStatus);
        $(document).off('notion-wp:settings-updated', handleJQuerySettingsUpdate);
        $(document).off('notion-wp:form-validation-changed', handleJQueryFormValidation);
      });
    }
  }

  /**
   * 设置React状态监听器
   */
  private setupReactListeners(): void {
    // 监听同步状态变化
    const unsubscribeSync = useSyncStore.subscribe(
      (state) => ({ status: state.status, progress: state.progress, isRunning: state.isRunning }),
      (syncState) => {
        this.queueSync({
          source: 'react',
          event: 'sync-status-changed',
          data: syncState,
          timestamp: Date.now(),
          id: this.generateId(),
        });
      }
    );

    // 监听设置状态变化
    const unsubscribeSettings = useSettingsStore.subscribe(
      (state) => ({ settings: state.settings, hasUnsavedChanges: state.hasUnsavedChanges }),
      (settingsState) => {
        this.queueSync({
          source: 'react',
          event: 'settings-updated',
          data: settingsState,
          timestamp: Date.now(),
          id: this.generateId(),
        });
      }
    );

    // 监听UI状态变化
    const unsubscribeUI = useUIStore.subscribe(
      (state) => ({ 
        notifications: state.notifications, 
        modals: state.modals,
        activeTab: state.activeTab,
        globalLoading: state.globalLoading 
      }),
      (uiState) => {
        this.queueSync({
          source: 'react',
          event: 'ui-state-changed',
          data: uiState,
          timestamp: Date.now(),
          id: this.generateId(),
        });
      }
    );

    // 添加清理函数
    this.cleanupFunctions.push(() => {
      unsubscribeSync();
      unsubscribeSettings();
      unsubscribeUI();
    });
  }

  /**
   * 设置全局错误处理
   */
  private setupGlobalErrorHandling(): void {
    const handleError = (error: Error, context: string) => {
      console.error(`❌ [StateBridge] ${context}:`, error);
      
      // 通知UI状态管理器显示错误
      useUIStore.getState().addNotification({
        id: this.generateId(),
        type: 'error',
        title: '状态同步错误',
        message: `${context}: ${error.message}`,
        duration: 5000,
      });
    };

    // 监听未捕获的错误
    window.addEventListener('error', (event) => {
      if (event.error && event.error.message.includes('StateBridge')) {
        handleError(event.error, '全局错误');
      }
    });

    // 监听Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason && event.reason.message && event.reason.message.includes('StateBridge')) {
        handleError(event.reason, 'Promise拒绝');
      }
    });
  }

  /**
   * 将状态同步事件加入队列（性能优化版本）
   */
  private queueSync(syncData: StateSyncData): void {
    // 性能监控
    performanceMonitor.measureStateSync('queue-sync', () => {
      // 性能优化：限制队列大小
      if (this.syncQueue.length >= this.maxQueueSize) {
        this.syncQueue = this.syncQueue.slice(-this.maxQueueSize / 2);
        performanceMonitor.addMetric({
          name: 'state-bridge-queue-overflow',
          value: this.syncQueue.length,
          timestamp: Date.now(),
          category: 'state',
          metadata: { action: 'queue-trimmed' }
        });
      }

      // 性能优化：去重相同类型的连续事件
      const lastEvent = this.syncQueue[this.syncQueue.length - 1];
      if (lastEvent &&
          lastEvent.source === syncData.source &&
          lastEvent.event === syncData.event &&
          Date.now() - lastEvent.timestamp < 100) {
        // 替换最后一个事件而不是添加新的
        this.syncQueue[this.syncQueue.length - 1] = syncData;

        if (this.config.enableDebug) {
          console.log('🔄 [StateBridge] 状态同步事件已合并:', syncData);
        }
      } else {
        this.syncQueue.push(syncData);

        if (this.config.enableDebug) {
          console.log('📤 [StateBridge] 状态同步已加入队列:', syncData);
        }
      }
    });

    // 防抖处理队列
    this.debouncedProcessQueue();
  }



  /**
   * 处理同步队列（性能优化版本）
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const startTime = performance.now();

    try {
      // 性能优化：批量处理，限制单次处理数量
      const batchSize = Math.min(this.syncBatchSize, this.syncQueue.length);
      const eventsToProcess = this.syncQueue.splice(0, batchSize);

      // 按时间戳排序，处理冲突
      eventsToProcess.sort((a, b) => a.timestamp - b.timestamp);

      // 性能优化：并行处理非冲突事件
      const eventGroups = this.groupEventsByType(eventsToProcess);

      for (const [eventType, events] of eventGroups) {
        if (events.length === 1) {
          await this.processSyncEvent(events[0]);
        } else {
          // 对于相同类型的多个事件，只处理最新的
          const latestEvent = events[events.length - 1];
          await this.processSyncEvent(latestEvent);

          if (this.config.enableDebug) {
            console.log(`🔄 [StateBridge] 合并处理 ${events.length} 个 ${eventType} 事件`);
          }
        }
      }

      // 记录性能指标
      const duration = performance.now() - startTime;
      performanceMonitor.addMetric({
        name: 'state-bridge-process-queue',
        value: duration,
        timestamp: Date.now(),
        category: 'state',
        metadata: {
          eventsProcessed: eventsToProcess.length,
          queueRemaining: this.syncQueue.length,
          batchSize
        }
      });

      if (this.config.enablePerformanceMonitoring) {
        this.performanceMetrics.set('lastProcessDuration', duration);

        if (this.config.enableDebug) {
          console.log(`⚡ [StateBridge] 队列处理完成，耗时: ${duration.toFixed(2)}ms，处理事件: ${eventsToProcess.length}`);
        }
      }

      // 如果还有事件在队列中，继续处理
      if (this.syncQueue.length > 0) {
        setTimeout(() => this.processQueue(), 0);
      }
    } catch (error) {
      console.error('❌ [StateBridge] 处理同步队列时发生错误:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 按事件类型分组事件（性能优化）
   */
  private groupEventsByType(events: StateSyncData[]): Map<string, StateSyncData[]> {
    const groups = new Map<string, StateSyncData[]>();

    for (const event of events) {
      const key = `${event.source}-${event.event}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(event);
    }

    return groups;
  }

  /**
   * 处理单个同步事件
   */
  private async processSyncEvent(syncData: StateSyncData): Promise<void> {
    try {
      switch (syncData.event) {
        case 'sync-status-changed':
          await this.handleSyncStatusChange(syncData);
          break;
        case 'settings-updated':
          await this.handleSettingsUpdate(syncData);
          break;
        case 'ui-state-changed':
          await this.handleUIStateChange(syncData);
          break;
        case 'form-validation-changed':
          await this.handleFormValidationChange(syncData);
          break;
        default:
          if (this.config.enableDebug) {
            console.warn('⚠️ [StateBridge] 未知的同步事件类型:', syncData.event);
          }
      }
    } catch (error) {
      console.error(`❌ [StateBridge] 处理同步事件失败 (${syncData.event}):`, error);
    }
  }

  /**
   * 处理同步状态变化
   */
  private async handleSyncStatusChange(syncData: StateSyncData): Promise<void> {
    if (syncData.source === 'jquery') {
      // jQuery -> React
      const { status, progress, isRunning } = syncData.data;
      const syncStore = useSyncStore.getState();
      
      // 避免循环更新
      if (syncStore.status !== status || syncStore.progress !== progress || syncStore.isRunning !== isRunning) {
        syncStore.updateStatus(status, { progress, message: syncStore.currentStep });
      }
    } else {
      // React -> jQuery
      if (typeof window !== 'undefined' && (window as any).jQuery) {
        const $ = (window as any).jQuery;
        $(document).trigger('notion-wp:react-sync-status-changed', [syncData.data]);
      }
    }
  }

  /**
   * 处理设置更新
   */
  private async handleSettingsUpdate(syncData: StateSyncData): Promise<void> {
    if (syncData.source === 'jquery') {
      // jQuery -> React
      const settingsStore = useSettingsStore.getState();
      if (syncData.data.settings) {
        settingsStore.updateSettings(syncData.data.settings);
      }
    } else {
      // React -> jQuery
      if (typeof window !== 'undefined' && (window as any).jQuery) {
        const $ = (window as any).jQuery;
        $(document).trigger('notion-wp:react-settings-updated', [syncData.data]);
      }
    }
  }

  /**
   * 处理UI状态变化
   */
  private async handleUIStateChange(syncData: StateSyncData): Promise<void> {
    if (syncData.source === 'react') {
      // React -> jQuery
      if (typeof window !== 'undefined' && (window as any).jQuery) {
        const $ = (window as any).jQuery;
        $(document).trigger('notion-wp:react-ui-state-changed', [syncData.data]);
      }
    }
  }

  /**
   * 处理表单验证变化
   */
  private async handleFormValidationChange(syncData: StateSyncData): Promise<void> {
    if (syncData.source === 'jquery') {
      // jQuery -> React
      const settingsStore = useSettingsStore.getState();
      if (syncData.data.fieldErrors) {
        Object.entries(syncData.data.fieldErrors).forEach(([field, error]) => {
          settingsStore.setFieldError(field, error as string);
        });
      }
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `bridge_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): Record<string, number> {
    return Object.fromEntries(this.performanceMetrics);
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 执行所有清理函数
    this.cleanupFunctions.forEach(cleanup => cleanup());
    this.cleanupFunctions = [];
    
    // 清空队列和监听器
    this.syncQueue = [];
    this.eventListeners.clear();
    this.performanceMetrics.clear();
    
    if (this.config.enableDebug) {
      console.log('🧹 [StateBridge] 状态桥接器已清理');
    }
  }
}

// 全局状态桥接器实例
let globalStateBridge: StateBridge | null = null;

/**
 * 获取全局状态桥接器实例
 */
export const getStateBridge = (config?: Partial<StateBridgeConfig>): StateBridge => {
  if (!globalStateBridge) {
    globalStateBridge = new StateBridge(config);
  }
  return globalStateBridge;
};

/**
 * 初始化状态桥接器
 */
export const initializeStateBridge = (config?: Partial<StateBridgeConfig>): StateBridge => {
  if (globalStateBridge) {
    globalStateBridge.cleanup();
  }
  globalStateBridge = new StateBridge(config);
  return globalStateBridge;
};

/**
 * 清理全局状态桥接器
 */
export const cleanupStateBridge = (): void => {
  if (globalStateBridge) {
    globalStateBridge.cleanup();
    globalStateBridge = null;
  }
};
