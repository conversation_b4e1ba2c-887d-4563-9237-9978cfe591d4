/**
 * 性能优化的React组件包装器
 * 
 * 提供自动性能监控、内存泄漏检测和渲染优化
 */

import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { usePerformanceMonitor, useMemoryLeakDetection } from '../../hooks/usePerformanceOptimization';
import { performanceMonitor } from '../../utils/PerformanceMonitor';

interface PerformanceOptimizedComponentProps {
  children: React.ReactNode;
  componentName: string;
  enableMonitoring?: boolean;
  enableMemoryLeakDetection?: boolean;
  renderOptimization?: 'memo' | 'callback' | 'both' | 'none';
  className?: string;
}

/**
 * 性能优化组件包装器
 */
const PerformanceOptimizedComponent: React.FC<PerformanceOptimizedComponentProps> = memo(({
  children,
  componentName,
  enableMonitoring = true,
  enableMemoryLeakDetection = true,
  renderOptimization = 'both',
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { measureInteraction, renderCount } = usePerformanceMonitor(componentName);
  const { addTimer, addListener } = useMemoryLeakDetection(componentName);

  // 性能监控
  useEffect(() => {
    if (!enableMonitoring) return;

    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      performanceMonitor.addMetric({
        name: `component-lifecycle-${componentName}`,
        value: endTime - startTime,
        timestamp: Date.now(),
        category: 'render',
        metadata: {
          componentName,
          renderCount,
          lifecycle: 'unmount'
        }
      });
    };
  }, [componentName, enableMonitoring, renderCount]);

  // 渲染优化的子组件包装
  const optimizedChildren = useMemo(() => {
    if (renderOptimization === 'none') {
      return children;
    }

    return performanceMonitor.measureRender(`children-${componentName}`, () => children);
  }, [children, componentName, renderOptimization]);

  // 优化的事件处理器
  const handleInteraction = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    if (!enableMonitoring) return;

    const action = event.type;
    measureInteraction(action, () => {
      // 事件处理逻辑
      if (event.type === 'click') {
        performanceMonitor.addMetric({
          name: `user-click-${componentName}`,
          value: Date.now(),
          timestamp: Date.now(),
          category: 'user',
          metadata: {
            componentName,
            target: (event.target as HTMLElement)?.tagName
          }
        });
      }
    });
  }, [componentName, enableMonitoring, measureInteraction]);

  return (
    <div
      ref={containerRef}
      className={className}
      onClick={handleInteraction}
      onKeyDown={handleInteraction}
      data-component={componentName}
      data-render-count={renderCount}
    >
      {optimizedChildren}
    </div>
  );
});

PerformanceOptimizedComponent.displayName = 'PerformanceOptimizedComponent';

export default PerformanceOptimizedComponent;

/**
 * 高阶组件：为任何组件添加性能优化
 */
export function withPerformanceOptimization<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    componentName?: string;
    enableMonitoring?: boolean;
    enableMemoryLeakDetection?: boolean;
    renderOptimization?: 'memo' | 'callback' | 'both' | 'none';
  } = {}
) {
  const {
    componentName = WrappedComponent.displayName || WrappedComponent.name || 'Unknown',
    enableMonitoring = true,
    enableMemoryLeakDetection = true,
    renderOptimization = 'both'
  } = options;

  const OptimizedComponent = memo((props: P) => {
    const { measureInteraction } = usePerformanceMonitor(componentName);
    const { addTimer, addListener } = useMemoryLeakDetection(componentName);

    // 包装props中的函数，添加性能监控
    const optimizedProps = useMemo(() => {
      const newProps = { ...props };
      
      Object.keys(newProps).forEach(key => {
        const value = (newProps as any)[key];
        if (typeof value === 'function' && key.startsWith('on')) {
          (newProps as any)[key] = (...args: any[]) => {
            measureInteraction(key, () => value(...args));
          };
        }
      });

      return newProps;
    }, [props, measureInteraction]);

    return (
      <PerformanceOptimizedComponent
        componentName={componentName}
        enableMonitoring={enableMonitoring}
        enableMemoryLeakDetection={enableMemoryLeakDetection}
        renderOptimization={renderOptimization}
      >
        <WrappedComponent {...optimizedProps} />
      </PerformanceOptimizedComponent>
    );
  });

  OptimizedComponent.displayName = `withPerformanceOptimization(${componentName})`;
  
  return OptimizedComponent;
}

/**
 * 懒加载组件包装器
 */
export const LazyPerformanceComponent = React.lazy(() => 
  Promise.resolve({ default: PerformanceOptimizedComponent })
);

/**
 * 性能优化的Suspense包装器
 */
interface PerformanceSuspenseProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  componentName: string;
}

export const PerformanceSuspense: React.FC<PerformanceSuspenseProps> = ({
  children,
  fallback = <div>Loading...</div>,
  componentName
}) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      performanceMonitor.addMetric({
        name: `suspense-${componentName}`,
        value: endTime - startTime,
        timestamp: Date.now(),
        category: 'render',
        metadata: {
          componentName,
          type: 'suspense'
        }
      });
    };
  }, [componentName]);

  return (
    <React.Suspense fallback={fallback}>
      {children}
    </React.Suspense>
  );
};

/**
 * 虚拟化列表组件
 */
interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  componentName: string;
}

export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  componentName
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = React.useState(0);
  const { measureInteraction } = usePerformanceMonitor(`${componentName}-virtualized`);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    measureInteraction('scroll', () => {
      setScrollTop(event.currentTarget.scrollTop);
    });
  }, [measureInteraction]);

  return (
    <div
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: visibleItems.totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${visibleItems.offsetY}px)` }}>
          {visibleItems.items.map((item, index) => (
            <div key={visibleItems.startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, visibleItems.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
