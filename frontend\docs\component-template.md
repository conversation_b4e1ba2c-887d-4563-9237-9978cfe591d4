# 组件文档模板

## 组件名称

### 概述
简要描述组件的用途和功能。

### 特性
- 特性1
- 特性2
- 特性3

### 使用示例

#### 基础用法
```tsx
import { ComponentName } from '@/components/ComponentName'

function Example() {
  return (
    <ComponentName
      prop1="value1"
      prop2={value2}
    />
  )
}
```

#### 高级用法
```tsx
import { ComponentName } from '@/components/ComponentName'

function AdvancedExample() {
  const handleEvent = (data: any) => {
    console.log('Event triggered:', data)
  }

  return (
    <ComponentName
      prop1="value1"
      prop2={value2}
      onEvent={handleEvent}
      advanced={true}
    />
  )
}
```

### API 参考

#### Props

| 属性名 | 类型 | 默认值 | 必需 | 描述 |
|--------|------|--------|------|------|
| prop1 | string | - | ✅ | 属性1的描述 |
| prop2 | number | 0 | ❌ | 属性2的描述 |
| prop3 | boolean | false | ❌ | 属性3的描述 |
| onEvent | (data: any) => void | - | ❌ | 事件回调函数 |

#### 类型定义

```typescript
interface ComponentProps {
  prop1: string
  prop2?: number
  prop3?: boolean
  onEvent?: (data: any) => void
}

interface ComponentState {
  loading: boolean
  error: string | null
  data: any[]
}
```

### 样式定制

#### CSS 类名
- `.component-root` - 根容器
- `.component-header` - 头部区域
- `.component-content` - 内容区域
- `.component-footer` - 底部区域

#### CSS 变量
```css
.component-root {
  --component-primary-color: #3b82f6;
  --component-secondary-color: #6b7280;
  --component-border-radius: 0.5rem;
  --component-spacing: 1rem;
}
```

### 无障碍访问

#### ARIA 属性
- `aria-label` - 组件标签
- `aria-describedby` - 描述信息
- `role` - 组件角色

#### 键盘导航
- `Tab` - 焦点移动
- `Enter` - 激活操作
- `Escape` - 取消操作

### 性能考虑

#### 优化建议
1. 使用 `React.memo` 避免不必要的重渲染
2. 使用 `useMemo` 缓存计算结果
3. 使用 `useCallback` 缓存事件处理函数
4. 避免在渲染函数中创建对象和数组

#### 性能指标
- 首次渲染时间: < 16ms
- 重渲染时间: < 8ms
- 内存使用: < 1MB

### 测试

#### 单元测试
```tsx
import { render, screen } from '@testing-library/react'
import { ComponentName } from './ComponentName'

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName prop1="test" />)
    expect(screen.getByText('test')).toBeInTheDocument()
  })

  it('handles events correctly', () => {
    const handleEvent = jest.fn()
    render(<ComponentName prop1="test" onEvent={handleEvent} />)
    
    // 触发事件
    // ...
    
    expect(handleEvent).toHaveBeenCalledWith(expectedData)
  })
})
```

#### E2E 测试
```typescript
import { test, expect } from '@playwright/test'

test('component interaction', async ({ page }) => {
  await page.goto('/component-demo')
  
  // 测试组件交互
  await page.click('[data-testid="component-button"]')
  await expect(page.locator('[data-testid="result"]')).toBeVisible()
})
```

### 故障排除

#### 常见问题

**问题1: 组件不显示**
- 检查是否正确导入组件
- 检查必需的props是否传递
- 检查CSS样式是否正确加载

**问题2: 事件不触发**
- 检查事件处理函数是否正确绑定
- 检查事件名称是否正确
- 检查组件是否处于可交互状态

**问题3: 性能问题**
- 检查是否有不必要的重渲染
- 检查是否有内存泄漏
- 使用React DevTools分析性能

### 更新日志

#### v2.0.0
- 新增功能A
- 优化性能B
- 修复问题C

#### v1.1.0
- 新增功能D
- 改进用户体验E

#### v1.0.0
- 初始版本发布

### 相关组件
- [RelatedComponent1](./RelatedComponent1.md)
- [RelatedComponent2](./RelatedComponent2.md)

### 贡献指南

#### 开发流程
1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

#### 代码规范
- 遵循 ESLint 规则
- 使用 TypeScript 类型注解
- 编写单元测试
- 更新文档

### 许可证
MIT License
