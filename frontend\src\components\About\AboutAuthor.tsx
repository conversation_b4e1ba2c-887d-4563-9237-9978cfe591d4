import React from 'react'
import { Card, CardContent } from '../Common'

export const AboutAuthor: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="notion-wp-header-section">
        <h2 className="text-xl font-semibold text-gray-800">
          关于作者
        </h2>
      </div>

      {/* 作者信息 */}
      <Card shadow="md">
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-2xl font-bold">
              FL
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-800">Frank-Loong</h3>
              <p className="text-sm text-gray-600 mb-2">科技爱好者 & AI玩家</p>
              <p className="text-sm text-gray-700">
                对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。
                此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。
              </p>
              <div className="mt-4 space-y-2">
                <a
                  href="https://frankloong.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:underline text-sm"
                >
                  <span className="mr-2">🌐</span>
                  个人网站
                </a>
                <br />
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center text-blue-600 hover:underline text-sm"
                >
                  <span className="mr-2">📧</span>
                  联系邮箱
                </a>
                <br />
                <a
                  href="https://github.com/Frank-Loong/Notion-to-WordPress"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:underline text-sm"
                >
                  <span className="mr-2">💻</span>
                  GitHub
                </a>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 插件信息 */}
      <Card title="插件信息" shadow="md">
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-800">版本：</div>
              <div className="text-sm text-gray-600">2.0.0</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-800">许可证：</div>
              <div className="text-sm text-gray-600">GPL v3</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-800">兼容性：</div>
              <div className="text-sm text-gray-600">WordPress 5.0+</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}