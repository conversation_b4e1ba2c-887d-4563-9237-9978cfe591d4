/**
 * 标签页组件 - 现代化的Tab系统
 * 
 * 替代jQuery的标签切换逻辑，提供更好的用户体验
 * 支持键盘导航、动画效果、状态持久化等功能
 */

import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { cn } from '../../utils/cn';
import { useUIStore } from '../../stores/uiStore';

// 标签页上下文
interface TabsContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  orientation: 'horizontal' | 'vertical';
}

const TabsContext = createContext<TabsContextType | null>(null);

// 标签页容器Props
interface TabsProps {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  children: React.ReactNode;
  persistKey?: string; // 本地存储键名
}

// 标签页列表Props
interface TabsListProps {
  className?: string;
  children: React.ReactNode;
}

// 标签页触发器Props
interface TabsTriggerProps {
  value: string;
  className?: string;
  disabled?: boolean;
  children: React.ReactNode;
  icon?: React.ReactNode;
  badge?: string | number;
}

// 标签页内容Props
interface TabsContentProps {
  value: string;
  className?: string;
  children: React.ReactNode;
  forceMount?: boolean;
}

// 标签页容器组件
export const Tabs: React.FC<TabsProps> = ({
  defaultValue,
  value,
  onValueChange,
  orientation = 'horizontal',
  className,
  children,
  persistKey,
}) => {
  const [internalValue, setInternalValue] = useState(() => {
    // 从本地存储恢复状态
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(persistKey);
      if (saved) return saved;
    }
    return value || defaultValue || '';
  });

  const activeTab = value !== undefined ? value : internalValue;

  const handleTabChange = (newValue: string) => {
    if (value === undefined) {
      setInternalValue(newValue);
    }
    
    // 保存到本地存储
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(persistKey, newValue);
    }
    
    onValueChange?.(newValue);
  };

  const contextValue: TabsContextType = {
    activeTab,
    setActiveTab: handleTabChange,
    orientation,
  };

  return (
    <TabsContext.Provider value={contextValue}>
      <div 
        className={cn(
          'notion-wp-tabs',
          orientation === 'vertical' ? 'flex' : 'block',
          className
        )}
        data-orientation={orientation}
      >
        {children}
      </div>
    </TabsContext.Provider>
  );
};

// 标签页列表组件
export const TabsList: React.FC<TabsListProps> = ({ className, children }) => {
  const context = useContext(TabsContext);
  if (!context) throw new Error('TabsList must be used within Tabs');

  const { orientation } = context;

  return (
    <div
      role="tablist"
      aria-orientation={orientation}
      className={cn(
        'notion-wp-tabs-list',
        'flex',
        orientation === 'horizontal' 
          ? 'border-b border-gray-200 space-x-1' 
          : 'flex-col border-r border-gray-200 space-y-1 min-w-48',
        className
      )}
    >
      {children}
    </div>
  );
};

// 标签页触发器组件
export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  value,
  className,
  disabled,
  children,
  icon,
  badge,
}) => {
  const context = useContext(TabsContext);
  if (!context) throw new Error('TabsTrigger must be used within Tabs');

  const { activeTab, setActiveTab, orientation } = context;
  const isActive = activeTab === value;
  const triggerRef = useRef<HTMLButtonElement>(null);

  const handleClick = () => {
    if (!disabled) {
      setActiveTab(value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    const triggers = Array.from(
      triggerRef.current?.parentElement?.querySelectorAll('[role="tab"]') || []
    ) as HTMLElement[];
    
    const currentIndex = triggers.indexOf(triggerRef.current!);
    let nextIndex = currentIndex;

    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        nextIndex = currentIndex > 0 ? currentIndex - 1 : triggers.length - 1;
        break;
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        nextIndex = currentIndex < triggers.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        e.preventDefault();
        nextIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        nextIndex = triggers.length - 1;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        handleClick();
        return;
    }

    if (nextIndex !== currentIndex) {
      const nextTrigger = triggers[nextIndex] as HTMLButtonElement;
      nextTrigger.focus();
      const nextValue = nextTrigger.getAttribute('data-value');
      if (nextValue) setActiveTab(nextValue);
    }
  };

  return (
    <button
      ref={triggerRef}
      role="tab"
      type="button"
      aria-selected={isActive}
      aria-controls={`panel-${value}`}
      data-value={value}
      disabled={disabled}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      className={cn(
        'notion-wp-tab-trigger',
        'relative flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        orientation === 'horizontal' 
          ? 'border-b-2 border-transparent hover:border-gray-300'
          : 'border-r-2 border-transparent hover:border-gray-300 justify-start w-full',
        isActive
          ? orientation === 'horizontal'
            ? 'border-blue-500 text-blue-600 bg-blue-50'
            : 'border-blue-500 text-blue-600 bg-blue-50'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
        className
      )}
    >
      {icon && (
        <span className="notion-wp-tab-icon flex-shrink-0">
          {icon}
        </span>
      )}
      
      <span className="notion-wp-tab-text">
        {children}
      </span>
      
      {badge && (
        <span className={cn(
          'notion-wp-tab-badge',
          'inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full',
          isActive 
            ? 'bg-blue-100 text-blue-800'
            : 'bg-gray-100 text-gray-600'
        )}>
          {badge}
        </span>
      )}
    </button>
  );
};

// 标签页内容组件
export const TabsContent: React.FC<TabsContentProps> = ({
  value,
  className,
  children,
  forceMount = false,
}) => {
  const context = useContext(TabsContext);
  if (!context) throw new Error('TabsContent must be used within Tabs');

  const { activeTab } = context;
  const isActive = activeTab === value;
  const [hasBeenActive, setHasBeenActive] = useState(isActive);

  useEffect(() => {
    if (isActive) {
      setHasBeenActive(true);
    }
  }, [isActive]);

  // 如果不是当前活动标签且不强制挂载且从未激活过，则不渲染
  if (!isActive && !forceMount && !hasBeenActive) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      id={`panel-${value}`}
      aria-labelledby={`tab-${value}`}
      hidden={!isActive}
      className={cn(
        'notion-wp-tab-content',
        'focus:outline-none',
        isActive ? 'block' : 'hidden',
        className
      )}
    >
      <div className={cn(
        'transition-opacity duration-200',
        isActive ? 'opacity-100' : 'opacity-0'
      )}>
        {children}
      </div>
    </div>
  );
};

// 导出所有组件
export { TabsContext };

// 默认导出
export default {
  Root: Tabs,
  List: TabsList,
  Trigger: TabsTrigger,
  Content: TabsContent,
};
