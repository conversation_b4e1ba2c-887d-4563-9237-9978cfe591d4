/**
 * 响应式设计样式
 * 
 * 为不同设备提供优化的用户界面
 * 
 * @since 2.0.0
 */

/* ===== 基础响应式变量 ===== */
:root {
  /* 断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* 间距 */
  --spacing-mobile: 1rem;
  --spacing-tablet: 1.5rem;
  --spacing-desktop: 2rem;
  
  /* 字体大小 */
  --font-size-mobile-base: 14px;
  --font-size-tablet-base: 15px;
  --font-size-desktop-base: 16px;
  
  /* 侧边栏宽度 */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  --sidebar-mobile-width: 280px;
}

/* ===== 设备类型基础样式 ===== */
.device-mobile {
  font-size: var(--font-size-mobile-base);
}

.device-tablet {
  font-size: var(--font-size-tablet-base);
}

.device-desktop {
  font-size: var(--font-size-desktop-base);
}

/* 触摸设备优化 */
.touch-device {
  /* 增大触摸目标 */
  --min-touch-target: 44px;
}

.touch-device button,
.touch-device .clickable {
  min-height: var(--min-touch-target);
  min-width: var(--min-touch-target);
}

/* ===== 布局响应式 ===== */

/* 移动端布局 */
@media (max-width: 767px) {
  .notion-wp-admin {
    padding: 0;
  }
  
  .notion-wp-layout.mobile-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
  
  .notion-wp-content.mobile-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-mobile);
    padding-top: 60px; /* 为移动端头部留空间 */
  }
}

/* 平板端布局 */
@media (min-width: 768px) and (max-width: 1023px) {
  .notion-wp-layout.tablet-layout {
    display: flex;
    height: calc(100vh - 60px);
  }
  
  .notion-wp-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-tablet);
  }
}

/* 桌面端布局 */
@media (min-width: 1024px) {
  .notion-wp-layout {
    display: flex;
    height: calc(100vh - 60px);
  }
  
  .notion-wp-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-desktop);
    transition: margin-left 0.3s ease;
  }
}

/* ===== 侧边栏响应式 ===== */

/* 移动端侧边栏 */
.notion-wp-mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  z-index: 1000;
}

.notion-wp-mobile-menu-toggle {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #374151;
}

.notion-wp-mobile-title {
  flex: 1;
  text-align: center;
  font-weight: 600;
  color: #111827;
}

.notion-wp-mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.notion-wp-sidebar-mobile {
  position: fixed;
  top: 0;
  left: -100%;
  width: var(--sidebar-mobile-width);
  height: 100vh;
  background: #fff;
  border-right: 1px solid #e5e7eb;
  transition: left 0.3s ease;
  z-index: 1002;
  overflow-y: auto;
}

.notion-wp-sidebar-mobile.open {
  left: 0;
}

.notion-wp-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.notion-wp-sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.notion-wp-sidebar-close {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #6b7280;
}

.notion-wp-menu-mobile {
  padding: 1rem 0;
}

.notion-wp-menu-item-mobile {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notion-wp-menu-item-mobile:hover {
  background-color: #f3f4f6;
}

.notion-wp-menu-item-mobile.active {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.notion-wp-menu-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.notion-wp-menu-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* 桌面端侧边栏 */
@media (min-width: 1024px) {
  .notion-wp-sidebar {
    width: var(--sidebar-width);
    background: #fff;
    border-right: 1px solid #e5e7eb;
    transition: width 0.3s ease;
    position: relative;
  }
  
  .notion-wp-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
  }
  
  .notion-wp-sidebar-toggle {
    position: absolute;
    top: 1rem;
    right: -12px;
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
  }
  
  .notion-wp-menu-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .notion-wp-menu-item.collapsed {
    justify-content: center;
    padding: 0.75rem 0.5rem;
  }
  
  .notion-wp-menu-item.collapsed .notion-wp-menu-text {
    display: none;
  }
  
  .notion-wp-menu-icon-only {
    font-size: 1.25rem;
  }
}

/* ===== 响应式表格 ===== */

/* 移动端卡片式表格 */
.mobile-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-card.clickable {
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.mobile-card.clickable:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mobile-card-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-card-row:last-child {
  border-bottom: none;
}

.mobile-card-label {
  font-weight: 600;
  color: #374151;
  flex: 0 0 40%;
  font-size: 0.875rem;
}

.mobile-card-value {
  flex: 1;
  text-align: right;
  color: #111827;
  font-size: 0.875rem;
}

.mobile-card-expand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f3f4f6;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
}

.mobile-card-expanded {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 桌面端表格 */
.responsive-table-element {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.responsive-table-element th {
  background: #f9fafb;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.responsive-table-element th.sortable {
  cursor: pointer;
  user-select: none;
}

.responsive-table-element th.sortable:hover {
  background: #f3f4f6;
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-icon {
  margin-left: 0.5rem;
  color: #9ca3af;
}

.responsive-table-element td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  color: #111827;
}

.responsive-table-element tr.clickable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.responsive-table-element tr.clickable:hover {
  background-color: #f9fafb;
}

.expand-column,
.expand-cell {
  width: 40px;
  text-align: center;
}

.expand-button {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  color: #6b7280;
  border-radius: 0.25rem;
}

.expand-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.expanded-row td {
  background: #f9fafb;
  padding: 1rem;
}

/* 表格控制器 */
.table-controls {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.column-toggles {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.column-toggle {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.column-toggle:hover {
  background: #f3f4f6;
}

.column-toggle.hidden {
  background: #fee2e2;
  border-color: #fca5a5;
  color: #dc2626;
}

/* 分页 */
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.mobile-pagination {
  justify-content: center;
  gap: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pagination-controls button {
  padding: 0.5rem 1rem;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.pagination-controls button:hover:not(:disabled) {
  background: #f3f4f6;
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载和空状态 */
.table-loading,
.table-empty {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 响应式工具类 ===== */

/* 显示/隐藏工具类 */
@media (max-width: 639px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 640px) and (max-width: 767px) {
  .hidden-sm { display: none !important; }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hidden-tablet { display: none !important; }
}

@media (min-width: 1024px) {
  .hidden-desktop { display: none !important; }
}

.show-mobile { display: none !important; }
.show-tablet { display: none !important; }
.show-desktop { display: none !important; }

@media (max-width: 767px) {
  .show-mobile { display: block !important; }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .show-tablet { display: block !important; }
}

@media (min-width: 1024px) {
  .show-desktop { display: block !important; }
}

/* 间距工具类 */
@media (max-width: 767px) {
  .responsive-spacing {
    padding: var(--spacing-mobile);
  }
  
  .responsive-gap {
    gap: var(--spacing-mobile);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-spacing {
    padding: var(--spacing-tablet);
  }
  
  .responsive-gap {
    gap: var(--spacing-tablet);
  }
}

@media (min-width: 1024px) {
  .responsive-spacing {
    padding: var(--spacing-desktop);
  }
  
  .responsive-gap {
    gap: var(--spacing-desktop);
  }
}
