/**
 * 快速配置组件
 * 
 * 提供预设模板快速配置插件功能：
 * - 性能级别预设 (保守/平衡/激进)
 * - 字段映射模板 (英文/中文/混合/自定义)
 * - 智能推荐引擎
 * 
 * @since 2.0.0
 */

import React, { useState } from 'react'
import { useSettingsStore } from '../../stores/settingsStore'
import { Card, CardContent, Button, Select } from '../Common'
import { SparklesIcon, CogIcon, CheckCircleIcon } from '@heroicons/react/24/outline'
import { useI18n } from '../../hooks/useI18n'
import { SmartRecommendations } from './SmartRecommendations'

interface PerformanceLevel {
  level: string
  name: string
  description: string
  config: {
    api_page_size: number
    concurrent_requests: number
    batch_size: number
    log_buffer_size: number
  }
}

interface FieldTemplate {
  template: string
  name: string
  description: string
  mapping: {
    title: string
    status: string
    post_type: string
    date: string
    excerpt: string
    featured_image: string
    categories: string
    tags: string
    password: string
  }
}

const PERFORMANCE_LEVELS: PerformanceLevel[] = [
  {
    level: 'conservative',
    name: '保守模式',
    description: '适合配置较低的服务器，稳定可靠',
    config: {
      api_page_size: 50,
      concurrent_requests: 3,
      batch_size: 10,
      log_buffer_size: 25
    }
  },
  {
    level: 'balanced',
    name: '平衡模式',
    description: '推荐的默认配置，性能与稳定性并重',
    config: {
      api_page_size: 100,
      concurrent_requests: 5,
      batch_size: 20,
      log_buffer_size: 50
    }
  },
  {
    level: 'aggressive',
    name: '激进模式',
    description: '适合高性能服务器，最大化同步速度',
    config: {
      api_page_size: 200,
      concurrent_requests: 10,
      batch_size: 50,
      log_buffer_size: 100
    }
  }
]

const FIELD_TEMPLATES: FieldTemplate[] = [
  {
    template: 'english',
    name: '英文模板',
    description: '适合英文Notion数据库',
    mapping: {
      title: 'Title,Name',
      status: 'Status,Published',
      post_type: 'Type,Post Type',
      date: 'Date,Created,Published Date',
      excerpt: 'Summary,Description,Excerpt',
      featured_image: 'Featured Image,Cover,Image',
      categories: 'Categories,Category',
      tags: 'Tags,Tag',
      password: 'Password,Private'
    }
  },
  {
    template: 'chinese',
    name: '中文模板',
    description: '适合中文Notion数据库',
    mapping: {
      title: '标题,名称,题目',
      status: '状态,发布状态',
      post_type: '类型,文章类型',
      date: '日期,创建日期,发布日期',
      excerpt: '摘要,描述,简介',
      featured_image: '特色图片,封面,图片',
      categories: '分类,类别',
      tags: '标签,tag',
      password: '密码,私密'
    }
  },
  {
    template: 'mixed',
    name: '混合模板',
    description: '中英文兼容，推荐使用',
    mapping: {
      title: 'Title,标题,Name,名称',
      status: 'Status,状态,Published,发布状态',
      post_type: 'Type,类型,Post Type,文章类型',
      date: 'Date,日期,Created,创建日期',
      excerpt: 'Summary,摘要,Description,描述,Excerpt,简介',
      featured_image: 'Featured Image,特色图片,Cover,封面',
      categories: 'Categories,分类,Category,类别',
      tags: 'Tags,标签,Tag',
      password: 'Password,密码,Private,私密'
    }
  }
]

export const QuickConfig: React.FC = () => {
  const { settings, updateSettings, saveSettings, hasUnsavedChanges, isSaving } = useSettingsStore()
  const [selectedPerformance, setSelectedPerformance] = useState(settings?.performance_level || 'balanced')
  const [selectedTemplate, setSelectedTemplate] = useState(settings?.field_template || 'mixed')
  const [isGettingRecommendations, setIsGettingRecommendations] = useState(false)
  const [recommendations, setRecommendations] = useState<any>(null)
  const [showSmartRecommendations, setShowSmartRecommendations] = useState(false)
  const { __ } = useI18n()

  // 应用性能级别
  const applyPerformanceLevel = (level: string) => {
    const config = PERFORMANCE_LEVELS.find(p => p.level === level)
    if (config) {
      updateSettings({
        performance_level: level,
        ...config.config
      })
      setSelectedPerformance(level)
    }
  }

  // 应用字段模板
  const applyFieldTemplate = (template: string) => {
    const fieldConfig = FIELD_TEMPLATES.find(t => t.template === template)
    if (fieldConfig) {
      updateSettings({
        field_template: template,
        field_mapping: fieldConfig.mapping
      })
      setSelectedTemplate(template)
    }
  }



  return (
    <div className="space-y-6">
      {/* 智能推荐组件 */}
      {showSmartRecommendations && (
        <SmartRecommendations />
      )}

      <Card
        title={`🚀 ${__('Quick Configuration')}`}
        subtitle={__('Use preset templates to quickly configure the plugin, suitable for most users')}
        shadow="md"
      >
      <CardContent className="space-y-6">
        {/* 智能推荐切换 */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <SparklesIcon className="h-6 w-6 text-blue-500" />
            <div>
              <h4 className="text-sm font-medium text-gray-900">{__('AI-Powered Smart Recommendations')}</h4>
              <p className="text-xs text-gray-600">{__('Get personalized configuration based on your environment')}</p>
            </div>
          </div>
          <Button
            variant={showSmartRecommendations ? "secondary" : "primary"}
            size="sm"
            onClick={() => setShowSmartRecommendations(!showSmartRecommendations)}
          >
            {showSmartRecommendations ? __('Hide Smart Recommendations') : __('Show Smart Recommendations')}
          </Button>
        </div>

        {/* 性能级别配置 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <CogIcon className="h-5 w-5 text-gray-500" />
            <h4 className="text-sm font-medium text-gray-700">{__('Performance Level')}</h4>
          </div>
          
          <Select
            value={selectedPerformance}
            onChange={(value) => applyPerformanceLevel(value)}
            options={PERFORMANCE_LEVELS.map(level => ({
              value: level.level,
              label: `${level.name} - ${level.description}`
            }))}
          />
          
          <p className="text-xs text-gray-500">
            {__('Choose the performance level suitable for your server configuration. The system will automatically set optimal API page size, baseline concurrency, and other parameters, and dynamically adjust based on real-time performance.')}
          </p>
        </div>

        {/* 字段映射模板 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-gray-500" />
            <h4 className="text-sm font-medium text-gray-700">{__('Field Mapping Template')}</h4>
          </div>
          
          <Select
            value={selectedTemplate}
            onChange={(value) => applyFieldTemplate(value)}
            options={FIELD_TEMPLATES.map(template => ({
              value: template.template,
              label: `${template.name} - ${template.description}`
            }))}
          />
          
          <p className="text-xs text-gray-500">
            {__('Choose a field mapping template that matches your Notion database language. Select "Custom" to configure detailed settings in the Field Mapping tab.')}
          </p>
        </div>



        {/* 保存提示 */}
        {hasUnsavedChanges && (
          <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">{__('You have unsaved quick configuration changes')}</p>
            <Button
              variant="primary"
              onClick={saveSettings}
              loading={isSaving}
              disabled={isSaving}
            >
              {isSaving ? __('Saving...') : __('Save Configuration')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
    </div>
  )
}