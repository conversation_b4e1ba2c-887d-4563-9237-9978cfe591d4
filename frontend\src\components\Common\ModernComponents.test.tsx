/**
 * 现代化组件测试套件
 * 
 * 测试新的通知、标签页、表单验证组件
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useUIStore } from '../../stores/uiStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { NotificationContainer, useNotification } from './Notification';
import { Tabs, TabsList, TabsTrigger, TabsContent } from './Tabs';
import { ValidatedInput, FormValidationContainer } from './FormValidation';

// 测试通知组件
const NotificationTestComponent: React.FC = () => {
  const notification = useNotification();

  return (
    <div>
      <button onClick={() => notification.success('成功', '操作成功完成')}>
        Success
      </button>
      <button onClick={() => notification.error('错误', '操作失败')}>
        Error
      </button>
      <button onClick={() => notification.warning('警告', '请注意')}>
        Warning
      </button>
      <button onClick={() => notification.info('信息', '这是一条信息')}>
        Info
      </button>
      <NotificationContainer />
    </div>
  );
};

// 测试标签页组件
const TabsTestComponent: React.FC = () => {
  return (
    <Tabs defaultValue="tab1" persistKey="test-tabs">
      <TabsList>
        <TabsTrigger value="tab1" icon="🏠">
          标签1
        </TabsTrigger>
        <TabsTrigger value="tab2" badge="3">
          标签2
        </TabsTrigger>
        <TabsTrigger value="tab3" disabled>
          禁用标签
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="tab1">
        <div>标签1的内容</div>
      </TabsContent>
      
      <TabsContent value="tab2">
        <div>标签2的内容</div>
      </TabsContent>
      
      <TabsContent value="tab3">
        <div>标签3的内容</div>
      </TabsContent>
    </Tabs>
  );
};

// 测试表单验证组件
const FormValidationTestComponent: React.FC = () => {
  return (
    <FormValidationContainer>
      <ValidatedInput
        id="api-key"
        name="api_key"
        placeholder="输入API Key"
        validationRule={{ type: 'api-key', required: true }}
        data-testid="api-key-input"
      />
      
      <ValidatedInput
        id="database-id"
        name="database_id"
        placeholder="输入Database ID"
        validationRule={{ type: 'database-id', required: true }}
        data-testid="database-id-input"
      />
      
      <ValidatedInput
        id="custom-field"
        name="custom_field"
        placeholder="自定义验证"
        validationRule={{
          type: 'custom',
          customValidator: (value) => ({
            isValid: value.length >= 5,
            level: value.length >= 5 ? 'success' : 'error',
            message: value.length >= 5 ? '验证通过' : '至少需要5个字符',
          }),
        }}
        data-testid="custom-input"
      />
    </FormValidationContainer>
  );
};

describe('现代化组件测试', () => {
  beforeEach(() => {
    // 重置stores
    useUIStore.getState().clearNotifications();
    useSettingsStore.getState().clearAllValidations();
  });

  describe('通知组件', () => {
    it('应该显示成功通知', async () => {
      const user = userEvent.setup();
      render(<NotificationTestComponent />);

      await user.click(screen.getByText('Success'));

      await waitFor(() => {
        expect(screen.getByText('成功')).toBeInTheDocument();
        expect(screen.getByText('操作成功完成')).toBeInTheDocument();
      });
    });

    it('应该显示错误通知', async () => {
      const user = userEvent.setup();
      render(<NotificationTestComponent />);

      await user.click(screen.getByText('Error'));

      await waitFor(() => {
        expect(screen.getByText('错误')).toBeInTheDocument();
        expect(screen.getByText('操作失败')).toBeInTheDocument();
      });
    });

    it('应该能够关闭通知', async () => {
      const user = userEvent.setup();
      render(<NotificationTestComponent />);

      await user.click(screen.getByText('Info'));

      await waitFor(() => {
        expect(screen.getByText('信息')).toBeInTheDocument();
      });

      const closeButton = screen.getByLabelText('关闭通知');
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('信息')).not.toBeInTheDocument();
      });
    });

    it('应该支持多个通知', async () => {
      const user = userEvent.setup();
      render(<NotificationTestComponent />);

      await user.click(screen.getByText('Success'));
      await user.click(screen.getByText('Error'));

      await waitFor(() => {
        expect(screen.getByText('成功')).toBeInTheDocument();
        expect(screen.getByText('错误')).toBeInTheDocument();
      });
    });
  });

  describe('标签页组件', () => {
    it('应该正确渲染标签页', () => {
      render(<TabsTestComponent />);

      expect(screen.getByText('标签1')).toBeInTheDocument();
      expect(screen.getByText('标签2')).toBeInTheDocument();
      expect(screen.getByText('禁用标签')).toBeInTheDocument();
    });

    it('应该显示默认活动标签的内容', () => {
      render(<TabsTestComponent />);

      // 标签1应该是活动的
      const tab1Button = screen.getByRole('tab', { name: /标签1/ });
      expect(tab1Button).toHaveAttribute('aria-selected', 'true');

      // 标签1的内容应该可见
      expect(screen.getByText('标签1的内容')).toBeInTheDocument();
      expect(screen.getByText('标签1的内容')).toBeVisible();

      // 标签2的内容不应该存在（因为从未激活过）
      expect(screen.queryByText('标签2的内容')).not.toBeInTheDocument();
    });

    it('应该能够切换标签', async () => {
      const user = userEvent.setup();
      render(<TabsTestComponent />);

      await user.click(screen.getByText('标签2'));

      await waitFor(() => {
        expect(screen.getByText('标签2的内容')).toBeVisible();
        expect(screen.queryByText('标签1的内容')).not.toBeVisible();
      });
    });

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup();
      render(<TabsTestComponent />);

      // 获取按钮元素而不是文本元素
      const tab1Button = screen.getByRole('tab', { name: /标签1/ });
      tab1Button.focus();

      await user.keyboard('{ArrowRight}');

      const tab2Button = screen.getByRole('tab', { name: /标签2/ });
      expect(tab2Button).toHaveFocus();
    });

    it('应该正确处理禁用状态', async () => {
      const user = userEvent.setup();
      render(<TabsTestComponent />);

      // 获取禁用的按钮元素
      const disabledTabButton = screen.getByRole('tab', { name: /禁用标签/ });
      expect(disabledTabButton).toBeDisabled();

      // 先切换到标签2，然后尝试点击禁用标签
      const tab2Button = screen.getByRole('tab', { name: /标签2/ });
      await user.click(tab2Button);

      // 现在应该显示标签2的内容
      expect(screen.getByText('标签2的内容')).toBeVisible();

      // 尝试点击禁用的标签
      await user.click(disabledTabButton);

      // 内容不应该切换，仍然显示标签2
      expect(screen.getByText('标签2的内容')).toBeVisible();
      expect(tab2Button).toHaveAttribute('aria-selected', 'true');
    });

    it('应该显示图标和徽章', () => {
      render(<TabsTestComponent />);

      expect(screen.getByText('🏠')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  describe('表单验证组件', () => {
    it('应该验证API Key格式', async () => {
      const user = userEvent.setup();
      render(<FormValidationTestComponent />);

      const apiKeyInput = screen.getByTestId('api-key-input');

      // 输入无效的API Key
      await user.type(apiKeyInput, 'short');
      fireEvent.blur(apiKeyInput);

      await waitFor(() => {
        expect(screen.getByText('API密钥长度可能不正确，请检查是否完整')).toBeInTheDocument();
      });

      // 清空并输入有效的API Key
      await user.clear(apiKeyInput);
      await user.type(apiKeyInput, 'valid_api_key_1234567890123456789012345678');
      fireEvent.blur(apiKeyInput);

      await waitFor(() => {
        expect(screen.getByText('API密钥格式正确')).toBeInTheDocument();
      });
    });

    it('应该验证Database ID格式', async () => {
      const user = userEvent.setup();
      render(<FormValidationTestComponent />);

      const databaseIdInput = screen.getByTestId('database-id-input');

      // 输入无效的Database ID
      await user.type(databaseIdInput, 'invalid');
      fireEvent.blur(databaseIdInput);

      await waitFor(() => {
        expect(screen.getByText('数据库ID长度应为32位字符')).toBeInTheDocument();
      });

      // 清空并输入有效的Database ID
      await user.clear(databaseIdInput);
      await user.type(databaseIdInput, '12345678901234567890123456789012');
      fireEvent.blur(databaseIdInput);

      await waitFor(() => {
        expect(screen.getByText('数据库ID格式正确')).toBeInTheDocument();
      });
    });

    it('应该支持自定义验证器', async () => {
      const user = userEvent.setup();
      render(<FormValidationTestComponent />);

      const customInput = screen.getByTestId('custom-input');

      // 输入少于5个字符
      await user.type(customInput, 'abc');
      fireEvent.blur(customInput);

      await waitFor(() => {
        expect(screen.getByText('至少需要5个字符')).toBeInTheDocument();
      });

      // 输入5个或更多字符
      await user.clear(customInput);
      await user.type(customInput, 'abcdef');
      fireEvent.blur(customInput);

      await waitFor(() => {
        expect(screen.getByText('验证通过')).toBeInTheDocument();
      });
    });

    it('应该显示验证图标', async () => {
      const user = userEvent.setup();
      render(<FormValidationTestComponent />);

      const apiKeyInput = screen.getByTestId('api-key-input');

      // 输入有效值应该显示成功图标
      await user.type(apiKeyInput, 'valid_api_key_1234567890123456789012345678');
      fireEvent.blur(apiKeyInput);

      await waitFor(() => {
        const successIcon = apiKeyInput.parentElement?.querySelector('svg');
        expect(successIcon).toBeInTheDocument();
      });
    });

    it('应该与Zustand store同步', async () => {
      const user = userEvent.setup();
      render(<FormValidationTestComponent />);

      const apiKeyInput = screen.getByTestId('api-key-input');

      await user.type(apiKeyInput, 'short');
      fireEvent.blur(apiKeyInput);

      await waitFor(() => {
        const store = useSettingsStore.getState();
        expect(store.formValidation.fieldValidations.api_key).toBeDefined();
        expect(store.formValidation.fieldValidations.api_key.isValid).toBe(false);
      });
    });
  });
});
