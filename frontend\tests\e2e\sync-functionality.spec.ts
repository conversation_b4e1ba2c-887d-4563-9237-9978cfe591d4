/**
 * 同步功能E2E测试
 * 
 * 测试Notion到WordPress的同步功能
 * 
 * @since 2.0.0
 */

import { test, expect } from '@playwright/test'
import { PluginPage } from '../page-objects/PluginPage'
import { loadTestData, waitForApiRequest, setupConsoleErrorTracking } from '../fixtures/test-utils'

test.describe('同步功能', () => {
  let pluginPage: PluginPage
  let consoleErrors: ReturnType<typeof setupConsoleErrorTracking>

  test.beforeEach(async ({ page }) => {
    consoleErrors = setupConsoleErrorTracking(page)
    pluginPage = new PluginPage(page)
    
    // 导航到同步页面
    await pluginPage.goto()
    await pluginPage.switchToTab('sync')
  })

  test.afterEach(async () => {
    if (consoleErrors.hasErrors()) {
      console.warn('发现控制台错误:', consoleErrors.getErrors())
    }
  })

  test('应该显示同步页面基本元素', async () => {
    // 验证同步按钮
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    await expect(syncButton).toBeVisible()
    
    // 验证同步类型选择
    const syncTypeSelect = pluginPage.page.locator('[data-testid="sync-type-select"]')
    await expect(syncTypeSelect).toBeVisible()
    
    // 验证同步状态显示
    const syncStatus = pluginPage.page.locator('[data-testid="sync-status"]')
    await expect(syncStatus).toBeVisible()
  })

  test('应该能够启动智能同步', async () => {
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    const syncTypeSelect = pluginPage.page.locator('[data-testid="sync-type-select"]')
    
    // 选择智能同步
    await syncTypeSelect.selectOption('smart')
    
    // 模拟同步API响应
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_start_sync')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              taskId: 'test-task-123',
              status: 'started',
              stats: {
                total_pages: 0,
                imported_pages: 0,
                updated_pages: 0,
                failed_pages: 0
              }
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 启动同步
    await syncButton.click()
    
    // 等待同步开始
    await waitForApiRequest(pluginPage.page, 'admin-ajax.php')
    
    // 验证同步状态更新
    const syncStatus = pluginPage.page.locator('[data-testid="sync-status"]')
    await expect(syncStatus).toContainText('运行中')
  })

  test('应该能够启动完整同步', async () => {
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    const syncTypeSelect = pluginPage.page.locator('[data-testid="sync-type-select"]')
    
    // 选择完整同步
    await syncTypeSelect.selectOption('full')
    
    // 模拟同步API响应
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_start_sync')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              taskId: 'test-task-456',
              status: 'started',
              stats: {
                total_pages: 100,
                imported_pages: 0,
                updated_pages: 0,
                failed_pages: 0
              }
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 启动同步
    await syncButton.click()
    
    // 等待同步开始
    await waitForApiRequest(pluginPage.page, 'admin-ajax.php')
    
    // 验证同步状态
    const syncStatus = pluginPage.page.locator('[data-testid="sync-status"]')
    await expect(syncStatus).toContainText('运行中')
  })

  test('应该显示同步进度', async () => {
    // 模拟正在进行的同步
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_get_sync_status')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              status: 'running',
              operation: '正在同步页面',
              progress: 45,
              data_count: 45,
              details: [
                {
                  step: '获取Notion页面',
                  status: 'completed',
                  message: '已获取100个页面',
                  timestamp: new Date().toISOString()
                },
                {
                  step: '处理页面内容',
                  status: 'running',
                  message: '正在处理第45个页面',
                  timestamp: new Date().toISOString(),
                  progress: 45
                }
              ]
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 刷新状态
    const refreshButton = pluginPage.page.locator('[data-testid="refresh-status"]')
    await refreshButton.click()
    
    // 验证进度显示
    const progressBar = pluginPage.page.locator('[data-testid="sync-progress"]')
    await expect(progressBar).toBeVisible()
    
    const progressText = pluginPage.page.locator('[data-testid="progress-text"]')
    await expect(progressText).toContainText('45%')
  })

  test('应该能够停止同步', async () => {
    // 先启动同步
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_start_sync')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { taskId: 'test-task-789', status: 'started' }
          })
        })
      } else if (postData?.includes('notion_to_wordpress_stop_sync')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { status: 'cancelled' }
          })
        })
      } else {
        route.continue()
      }
    })
    
    await syncButton.click()
    await waitForApiRequest(pluginPage.page, 'admin-ajax.php')
    
    // 停止同步
    const stopButton = pluginPage.page.locator('[data-testid="stop-sync"]')
    await expect(stopButton).toBeVisible()
    await stopButton.click()
    
    // 等待停止请求
    await waitForApiRequest(pluginPage.page, 'admin-ajax.php')
    
    // 验证同步已停止
    const syncStatus = pluginPage.page.locator('[data-testid="sync-status"]')
    await expect(syncStatus).toContainText('已取消')
  })

  test('应该显示同步统计信息', async () => {
    // 模拟完成的同步
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_get_sync_status')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              status: 'completed',
              operation: '同步完成',
              progress: 100,
              data_count: 100,
              stats: {
                total_pages: 100,
                imported_pages: 85,
                updated_pages: 10,
                failed_pages: 5,
                execution_time: 120,
                memory_usage: 64000000
              }
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 刷新状态
    const refreshButton = pluginPage.page.locator('[data-testid="refresh-status"]')
    await refreshButton.click()
    
    // 验证统计信息
    const statsContainer = pluginPage.page.locator('[data-testid="sync-stats"]')
    await expect(statsContainer).toBeVisible()
    
    await expect(statsContainer).toContainText('总页面: 100')
    await expect(statsContainer).toContainText('导入: 85')
    await expect(statsContainer).toContainText('更新: 10')
    await expect(statsContainer).toContainText('失败: 5')
  })

  test('应该处理同步错误', async () => {
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    
    // 模拟同步失败
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_start_sync')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            data: 'API连接失败：无效的API密钥'
          })
        })
      } else {
        route.continue()
      }
    })
    
    await syncButton.click()
    
    // 验证错误处理
    await expect(pluginPage.errorNotification).toBeVisible()
    const errorText = await pluginPage.errorNotification.textContent()
    expect(errorText).toContain('API连接失败')
  })

  test('应该支持增量同步', async () => {
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    const incrementalCheckbox = pluginPage.page.locator('[data-testid="incremental-sync"]')
    
    // 启用增量同步
    await incrementalCheckbox.check()
    
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_start_sync') && postData?.includes('incremental')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              taskId: 'incremental-task-123',
              status: 'started',
              type: 'incremental'
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    await syncButton.click()
    await waitForApiRequest(pluginPage.page, 'admin-ajax.php')
    
    // 验证增量同步已启动
    const syncStatus = pluginPage.page.locator('[data-testid="sync-status"]')
    await expect(syncStatus).toContainText('增量同步')
  })

  test('应该显示同步历史', async () => {
    // 切换到同步历史标签
    const historyTab = pluginPage.page.locator('[data-testid="sync-history-tab"]')
    await historyTab.click()
    
    // 模拟历史数据
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_get_sync_history')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: [
              {
                id: 1,
                start_time: '2024-01-01 10:00:00',
                end_time: '2024-01-01 10:05:00',
                status: 'completed',
                type: 'smart',
                stats: {
                  total_pages: 50,
                  imported_pages: 45,
                  updated_pages: 3,
                  failed_pages: 2
                }
              },
              {
                id: 2,
                start_time: '2024-01-01 09:00:00',
                end_time: '2024-01-01 09:10:00',
                status: 'completed',
                type: 'full',
                stats: {
                  total_pages: 100,
                  imported_pages: 95,
                  updated_pages: 5,
                  failed_pages: 0
                }
              }
            ]
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 验证历史记录显示
    const historyTable = pluginPage.page.locator('[data-testid="sync-history-table"]')
    await expect(historyTable).toBeVisible()
    
    const historyRows = historyTable.locator('tbody tr')
    await expect(historyRows).toHaveCount(2)
  })

  test('应该支持实时状态更新', async () => {
    // 模拟SSE连接
    let sseEventCount = 0
    
    await pluginPage.page.route('**/wp-admin/admin-ajax.php', route => {
      const postData = route.request().postData()
      if (postData?.includes('notion_to_wordpress_get_sync_status')) {
        sseEventCount++
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              status: 'running',
              progress: Math.min(sseEventCount * 10, 100),
              operation: `正在处理第${sseEventCount * 10}个页面`
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    // 启动同步
    const syncButton = pluginPage.page.locator('[data-testid="start-sync"]')
    await syncButton.click()
    
    // 等待多次状态更新
    for (let i = 0; i < 3; i++) {
      await pluginPage.page.waitForTimeout(2000)
      const progressText = pluginPage.page.locator('[data-testid="progress-text"]')
      const progress = await progressText.textContent()
      console.log(`进度更新 ${i + 1}: ${progress}`)
    }
    
    // 验证进度确实在更新
    expect(sseEventCount).toBeGreaterThan(1)
  })
})
