/**
 * 原版后台完全复刻 - 纯JavaScript实现
 * 
 * 这个脚本完全复刻原版PHP后台的所有界面、功能、样式
 * 确保视觉效果和功能完全一致
 */

(function() {
    'use strict';
    
    console.log('🎨 开始渲染原版后台复刻版本...');
    
    // 菜单项定义（与原版完全一致）
    const MENU_ITEMS = [
        { id: 'api-settings', label: '🔄 同步设置', emoji: '🔄', text: '同步设置' },
        { id: 'field-mapping', label: '🔗 字段映射', emoji: '🔗', text: '字段映射' },
        { id: 'performance-config', label: '⚡ 性能配置', emoji: '⚡', text: '性能配置' },
        { id: 'performance', label: '📊 性能监控', emoji: '📊', text: '性能监控' },
        { id: 'other-settings', label: '⚙️ 其他设置', emoji: '⚙️', text: '其他设置' },
        { id: 'debug', label: '🐞 调试工具', emoji: '🐞', text: '调试工具' },
        { id: 'help', label: '📖 使用帮助', emoji: '📖', text: '使用帮助' },
        { id: 'about-author', label: '👨‍💻 关于作者', emoji: '👨‍💻', text: '关于作者' }
    ];
    
    let activeTab = 'api-settings';
    
    // 获取WordPress配置
    const wpConfig = window.wpNotionConfig || {};
    const version = wpConfig.version || '2.0.0-beta.1';
    
    // 检测设备类型
    function getDeviceType() {
        const width = window.innerWidth;
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }
    
    // 渲染统计卡片
    function renderStatsGrid() {
        return `
            <div class="notion-stats-grid">
                <div class="stat-card">
                    <h3 class="stat-imported-count">0</h3>
                    <span>已导入页面</span>
                </div>
                <div class="stat-card">
                    <h3 class="stat-published-count">0</h3>
                    <span>已发布页面</span>
                </div>
                <div class="stat-card">
                    <h3 class="stat-last-update">从未</h3>
                    <span>最后同步</span>
                </div>
                <div class="stat-card">
                    <h3 class="stat-next-run">未计划</h3>
                    <span>下次同步</span>
                </div>
            </div>
        `;
    }
    
    // 渲染API设置表单
    function renderApiSettingsForm() {
        return `
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row">
                            <label for="notion_to_wordpress_api_key">API密钥</label>
                        </th>
                        <td>
                            <div class="input-with-validation">
                                <div class="input-with-button">
                                    <input
                                        type="password"
                                        id="notion_to_wordpress_api_key"
                                        name="notion_to_wordpress_api_key"
                                        class="regular-text notion-wp-validated-input"
                                        autocomplete="off"
                                        placeholder="输入您的Notion API密钥"
                                        data-validation="api-key"
                                    />
                                    <button
                                        type="button"
                                        class="button button-secondary show-hide-password"
                                        title="显示/隐藏密钥"
                                    >
                                        <span class="dashicons dashicons-visibility"></span>
                                    </button>
                                </div>
                                <div class="validation-feedback" id="api-key-feedback"></div>
                            </div>
                            <p class="description">在Notion的"我的集成"页面创建并获取API密钥。</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="notion_to_wordpress_database_id">数据库ID</label>
                        </th>
                        <td>
                            <div class="input-with-validation">
                                <input
                                    type="text"
                                    id="notion_to_wordpress_database_id"
                                    name="notion_to_wordpress_database_id"
                                    class="regular-text notion-wp-validated-input"
                                    placeholder="输入您的Notion数据库ID"
                                    data-validation="database-id"
                                />
                                <div class="validation-feedback" id="database-id-feedback"></div>
                            </div>
                            <p class="description">
                                可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/<strong>数据库ID</strong>?v=xxx
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="sync_schedule">自动同步频率</label>
                        </th>
                        <td>
                            <select id="sync_schedule" name="sync_schedule" class="regular-text">
                                <option value="manual">手动同步</option>
                                <option value="twicedaily">每天两次</option>
                                <option value="daily">每天一次</option>
                                <option value="weekly">每周一次</option>
                                <option value="biweekly">每两周一次</option>
                                <option value="monthly">每月一次</option>
                            </select>
                            <p class="description">选择 "手动同步" 以禁用定时任务。</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        `;
    }
    
    // 渲染同步操作区域
    function renderSyncActions() {
        return `
            <div class="notion-wp-button-row">
                <button type="button" id="notion-test-connection" class="button button-secondary">
                    <span class="dashicons dashicons-admin-network"></span> 测试连接
                </button>
            </div>

            <div class="notion-wp-sync-actions">
                <h3>同步操作</h3>
                <div class="sync-buttons">
                    <button
                        type="button"
                        class="button button-primary notion-wp-sync-btn"
                        id="notion-manual-import"
                        data-sync-type="manual"
                    >
                        <span class="dashicons dashicons-lightbulb"></span>
                        <span class="button-text">智能同步</span>
                    </button>
                    <button
                        type="button"
                        class="button button-secondary notion-wp-sync-btn"
                        id="notion-full-import"
                        data-sync-type="full"
                    >
                        <span class="dashicons dashicons-update"></span>
                        <span class="button-text">完全同步</span>
                    </button>
                </div>

                <div class="sync-progress notion-wp-hidden" id="sync-progress">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span class="current-step">准备同步...</span>
                        <span class="progress-percentage">0%</span>
                    </div>
                </div>

                <div class="sync-info">
                    <p><strong>智能同步</strong>: 只同步有变化的页面，速度更快</p>
                    <p><strong>完全同步</strong>: 同步所有页面，确保数据一致性</p>
                </div>
            </div>
        `;
    }
    
    // 渲染快速配置区域
    function renderQuickConfig() {
        return `
            <div class="notion-wp-settings-section">
                <h2>🚀 快速配置</h2>
                <p class="description">使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。</p>

                <table class="form-table">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="performance_level">性能级别</label>
                            </th>
                            <td>
                                <select id="performance_level" name="performance_level" class="regular-text">
                                    <option value="conservative">保守模式 - 适合配置较低的服务器</option>
                                    <option value="balanced" selected>平衡模式 - 推荐的默认配置</option>
                                    <option value="aggressive">激进模式 - 适合高性能服务器</option>
                                </select>
                                <p class="description">选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="field_template">字段映射模板</label>
                            </th>
                            <td>
                                <select id="field_template" name="field_template" class="regular-text">
                                    <option value="english">英文模板 - 适合英文Notion数据库</option>
                                    <option value="chinese">中文模板 - 适合中文Notion数据库</option>
                                    <option value="mixed" selected>混合模板 - 中英文兼容</option>
                                    <option value="custom">自定义 - 手动配置所有字段</option>
                                </select>
                                <p class="description">选择与您的Notion数据库语言匹配的字段映射模板。选择"自定义"可在字段映射标签页进行详细配置。</p>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="notion-wp-smart-recommendations">
                    <h3>💡 智能推荐</h3>
                    <div id="config-recommendations">
                        <button type="button" class="button button-secondary" id="get-smart-recommendations">
                            获取配置建议
                        </button>
                        <div id="recommendations-result" class="notion-wp-hidden"></div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染同步设置页面内容
    function renderApiSettingsContent() {
        return `
            <div class="notion-wp-tab-content active" id="api-settings">
                <div class="notion-wp-settings-section">
                    <h2>Notion API 设置</h2>
                    
                    ${renderStatsGrid()}
                    
                    <p class="description">
                        连接到您的Notion数据库所需的设置。
                        <a href="https://developers.notion.com/docs/getting-started" target="_blank">了解如何获取API密钥</a>
                    </p>
                    
                    ${renderApiSettingsForm()}
                    ${renderSyncActions()}
                </div>
                
                ${renderQuickConfig()}
            </div>
        `;
    }
    
    // 渲染其他页面的占位符内容
    function renderPlaceholderContent(tabId) {
        const menuItem = MENU_ITEMS.find(item => item.id === tabId);
        return `
            <div class="notion-wp-tab-content active">
                <div class="notion-wp-settings-section">
                    <h2>${menuItem ? menuItem.text : '未知页面'}</h2>
                    <p>此页面内容正在开发中...</p>
                    <p class="description">原版后台的完整功能正在逐步迁移到React版本中。</p>
                </div>
            </div>
        `;
    }
    
    // 渲染主要内容
    function renderMainContent() {
        if (activeTab === 'api-settings') {
            return renderApiSettingsContent();
        } else {
            return renderPlaceholderContent(activeTab);
        }
    }
    
    // 渲染完整的后台界面
    function renderBackend() {
        const deviceType = getDeviceType();
        const isMobile = deviceType === 'mobile';
        
        return `
            <div class="notion-wp-app notion-wp-${deviceType}" data-device="${deviceType}">
                ${!isMobile ? `
                    <div class="notion-wp-header">
                        <div class="notion-wp-header-content">
                            <h1 class="wp-heading-inline">
                                <span class="notion-wp-logo"></span>
                                Notion to WordPress
                            </h1>
                            <div class="notion-wp-version">${version}</div>
                        </div>
                    </div>
                ` : ''}

                <div class="notion-wp-layout">
                    <div class="notion-wp-sidebar">
                        <div class="notion-wp-menu">
                            ${MENU_ITEMS.map(item => `
                                <button
                                    class="notion-wp-menu-item ${activeTab === item.id ? 'active' : ''}"
                                    data-tab="${item.id}"
                                >
                                    ${item.label}
                                </button>
                            `).join('')}
                        </div>
                    </div>

                    <div class="notion-wp-content">
                        ${renderMainContent()}
                    </div>
                </div>
            </div>
        `;
    }
    
    // 绑定事件处理器
    function bindEventHandlers() {
        // 菜单切换事件
        document.addEventListener('click', function(e) {
            const menuItem = e.target.closest('.notion-wp-menu-item');
            if (menuItem) {
                const tabId = menuItem.getAttribute('data-tab');
                if (tabId && tabId !== activeTab) {
                    activeTab = tabId;
                    
                    // 更新菜单状态
                    document.querySelectorAll('.notion-wp-menu-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    menuItem.classList.add('active');
                    
                    // 更新内容
                    const contentArea = document.querySelector('.notion-wp-content');
                    if (contentArea) {
                        contentArea.innerHTML = renderMainContent();
                    }
                    
                    console.log(`🔄 切换到标签页: ${tabId}`);
                }
            }
        });
        
        // 响应式处理
        window.addEventListener('resize', function() {
            const app = document.querySelector('.notion-wp-app');
            if (app) {
                const deviceType = getDeviceType();
                app.setAttribute('data-device', deviceType);
                app.className = `notion-wp-app notion-wp-${deviceType}`;
            }
        });
    }
    
    // 初始化应用
    function initializeApp() {
        const reactRoot = document.getElementById('notion-to-wordpress-react-root');
        if (!reactRoot) {
            console.error('❌ React挂载点未找到：#notion-to-wordpress-react-root');
            return;
        }
        
        // 渲染界面
        reactRoot.innerHTML = renderBackend();
        
        // 绑定事件
        bindEventHandlers();
        
        console.log('✅ 原版后台复刻版本渲染完成');
    }
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }
    
})();
