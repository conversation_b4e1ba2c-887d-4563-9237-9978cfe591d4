/**
 * API设置页面对象模型
 * 
 * 封装API设置页面的元素和操作
 * 
 * @since 2.0.0
 */

import { Page, Locator, expect } from '@playwright/test'
import { PluginPage } from './PluginPage'
import { waitForApiRequest, fillForm } from '../fixtures/test-utils'

export class ApiSettingsPage extends PluginPage {
  // API设置表单元素
  readonly apiKeyInput: Locator
  readonly databaseIdInput: Locator
  readonly saveButton: Locator
  readonly testConnectionButton: Locator
  readonly resetButton: Locator
  
  // 连接状态
  readonly connectionStatus: Locator
  readonly connectionIndicator: Locator
  readonly lastTestTime: Locator
  
  // 高级设置
  readonly advancedSettingsToggle: Locator
  readonly apiVersionSelect: Locator
  readonly timeoutInput: Locator
  readonly retryCountInput: Locator
  
  // 验证消息
  readonly validationMessages: Locator
  readonly apiKeyValidation: Locator
  readonly databaseIdValidation: Locator

  constructor(page: Page) {
    super(page)
    
    // API设置表单
    this.apiKeyInput = page.locator('[data-testid="api-key-input"]')
    this.databaseIdInput = page.locator('[data-testid="database-id-input"]')
    this.saveButton = page.locator('[data-testid="save-api-settings"]')
    this.testConnectionButton = page.locator('[data-testid="test-connection"]')
    this.resetButton = page.locator('[data-testid="reset-api-settings"]')
    
    // 连接状态
    this.connectionStatus = page.locator('[data-testid="connection-status"]')
    this.connectionIndicator = page.locator('[data-testid="connection-indicator"]')
    this.lastTestTime = page.locator('[data-testid="last-test-time"]')
    
    // 高级设置
    this.advancedSettingsToggle = page.locator('[data-testid="advanced-settings-toggle"]')
    this.apiVersionSelect = page.locator('[data-testid="api-version-select"]')
    this.timeoutInput = page.locator('[data-testid="timeout-input"]')
    this.retryCountInput = page.locator('[data-testid="retry-count-input"]')
    
    // 验证消息
    this.validationMessages = page.locator('.validation-message')
    this.apiKeyValidation = page.locator('[data-testid="api-key-validation"]')
    this.databaseIdValidation = page.locator('[data-testid="database-id-validation"]')
  }

  /**
   * 导航到API设置页面
   */
  async goto() {
    await super.goto()
    await this.switchToTab('api-settings')
  }

  /**
   * 填写API密钥
   */
  async fillApiKey(apiKey: string) {
    await this.apiKeyInput.clear()
    await this.apiKeyInput.fill(apiKey)
  }

  /**
   * 填写数据库ID
   */
  async fillDatabaseId(databaseId: string) {
    await this.databaseIdInput.clear()
    await this.databaseIdInput.fill(databaseId)
  }

  /**
   * 填写完整的API设置
   */
  async fillApiSettings(apiKey: string, databaseId: string) {
    await fillForm(this.page, {
      '[data-testid="api-key-input"]': apiKey,
      '[data-testid="database-id-input"]': databaseId
    })
  }

  /**
   * 保存API设置
   */
  async saveSettings() {
    await this.saveButton.click()
    
    // 等待保存请求完成
    await waitForApiRequest(this.page, 'admin-ajax.php')
    
    // 等待通知出现
    await this.waitForNotification('success')
  }

  /**
   * 测试连接
   */
  async testConnection() {
    await this.testConnectionButton.click()
    
    // 等待测试请求完成
    await waitForApiRequest(this.page, 'admin-ajax.php')
    
    // 等待连接状态更新
    await this.page.waitForTimeout(2000)
  }

  /**
   * 重置设置
   */
  async resetSettings() {
    await this.resetButton.click()
    
    // 确认重置对话框
    await this.page.locator('.confirm-dialog .confirm-button').click()
    
    // 等待重置完成
    await this.waitForNotification('success')
  }

  /**
   * 展开高级设置
   */
  async expandAdvancedSettings() {
    const isExpanded = await this.advancedSettingsToggle.getAttribute('aria-expanded')
    
    if (isExpanded !== 'true') {
      await this.advancedSettingsToggle.click()
      await this.page.waitForTimeout(500)
    }
  }

  /**
   * 设置API版本
   */
  async setApiVersion(version: string) {
    await this.expandAdvancedSettings()
    await this.apiVersionSelect.selectOption(version)
  }

  /**
   * 设置超时时间
   */
  async setTimeout(timeout: number) {
    await this.expandAdvancedSettings()
    await this.timeoutInput.clear()
    await this.timeoutInput.fill(timeout.toString())
  }

  /**
   * 设置重试次数
   */
  async setRetryCount(count: number) {
    await this.expandAdvancedSettings()
    await this.retryCountInput.clear()
    await this.retryCountInput.fill(count.toString())
  }

  /**
   * 获取当前API密钥值
   */
  async getApiKeyValue(): Promise<string> {
    return await this.apiKeyInput.inputValue()
  }

  /**
   * 获取当前数据库ID值
   */
  async getDatabaseIdValue(): Promise<string> {
    return await this.databaseIdInput.inputValue()
  }

  /**
   * 获取连接状态
   */
  async getConnectionStatus(): Promise<string> {
    return await this.connectionStatus.textContent() || ''
  }

  /**
   * 检查连接是否成功
   */
  async isConnectionSuccessful(): Promise<boolean> {
    const status = await this.getConnectionStatus()
    return status.includes('成功') || status.includes('Connected')
  }

  /**
   * 检查是否有验证错误
   */
  async hasValidationErrors(): Promise<boolean> {
    return await this.validationMessages.count() > 0
  }

  /**
   * 获取验证错误消息
   */
  async getValidationErrors(): Promise<string[]> {
    const messages = await this.validationMessages.all()
    const errors: string[] = []
    
    for (const message of messages) {
      const text = await message.textContent()
      if (text) {
        errors.push(text.trim())
      }
    }
    
    return errors
  }

  /**
   * 检查API密钥格式
   */
  async validateApiKeyFormat(apiKey: string): Promise<boolean> {
    // Notion API密钥格式验证
    const notionApiKeyPattern = /^secret_[a-zA-Z0-9]{43}$/
    return notionApiKeyPattern.test(apiKey)
  }

  /**
   * 检查数据库ID格式
   */
  async validateDatabaseIdFormat(databaseId: string): Promise<boolean> {
    // Notion数据库ID格式验证
    const notionDbIdPattern = /^[a-f0-9]{32}$|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/
    return notionDbIdPattern.test(databaseId.replace(/-/g, ''))
  }

  /**
   * 测试无效的API设置
   */
  async testInvalidSettings() {
    // 测试空值
    await this.fillApiSettings('', '')
    await this.saveSettings()
    expect(await this.hasValidationErrors()).toBe(true)
    
    // 测试无效格式
    await this.fillApiSettings('invalid-key', 'invalid-id')
    await this.saveSettings()
    expect(await this.hasValidationErrors()).toBe(true)
  }

  /**
   * 测试连接超时
   */
  async testConnectionTimeout() {
    // 设置很短的超时时间
    await this.setTimeout(1)
    
    // 模拟慢速网络
    await this.page.route('**/notion.com/**', route => {
      setTimeout(() => route.continue(), 5000) // 5秒延迟
    })
    
    await this.testConnection()
    
    // 应该显示超时错误
    const status = await this.getConnectionStatus()
    expect(status).toContain('超时')
    
    // 恢复网络
    await this.page.unroute('**/notion.com/**')
  }

  /**
   * 检查表单自动保存
   */
  async checkAutoSave() {
    const initialApiKey = await this.getApiKeyValue()
    
    // 修改API密钥
    await this.fillApiKey('test-auto-save-key')
    
    // 等待自动保存
    await this.page.waitForTimeout(3000)
    
    // 刷新页面
    await this.page.reload()
    await this.waitForLoad()
    
    // 检查值是否保存
    const savedApiKey = await this.getApiKeyValue()
    expect(savedApiKey).toBe('test-auto-save-key')
    
    // 恢复原值
    await this.fillApiKey(initialApiKey)
    await this.saveSettings()
  }

  /**
   * 检查敏感信息遮蔽
   */
  async checkSensitiveDataMasking() {
    // API密钥应该被遮蔽显示
    const apiKeyType = await this.apiKeyInput.getAttribute('type')
    expect(apiKeyType).toBe('password')
    
    // 或者检查是否有显示/隐藏按钮
    const toggleButton = this.page.locator('[data-testid="toggle-api-key-visibility"]')
    if (await toggleButton.isVisible()) {
      // 测试显示/隐藏功能
      await toggleButton.click()
      const newType = await this.apiKeyInput.getAttribute('type')
      expect(newType).toBe('text')
      
      await toggleButton.click()
      const hiddenType = await this.apiKeyInput.getAttribute('type')
      expect(hiddenType).toBe('password')
    }
  }
}
